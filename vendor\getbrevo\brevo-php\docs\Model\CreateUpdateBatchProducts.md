# CreateUpdateBatchProducts

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**products** | [**\Brevo\Client\Model\CreateUpdateProducts[]**](CreateUpdateProducts.md) | array of products objects | 
**updateEnabled** | **bool** | Facilitate to update the existing categories in the same request (updateEnabled &#x3D; true) | [optional] 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


