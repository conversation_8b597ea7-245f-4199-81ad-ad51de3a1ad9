       * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
        }

        :root {
            --primary: #FF6B35;
            --primary-light: #ff9f1c;
            --accent: #E63946;
            --neutral: #F7F7F7;
            --dark: #1D3557;
            --text: #333333;
            --success: #2ecc71;
            --shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
            --transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            --border-radius: 20px;
        }

        body {
            background: linear-gradient(135deg, #fff9f0 0%, #fff2e5 100%);
            color: var(--text);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            position: relative;
            overflow-x: hidden;
        }

        .food-bg-element {
            position: absolute;
            z-index: -1;
            opacity: 0.08;
            animation: float 15s ease-in-out infinite;
        }
        
        .food-bg-element:nth-child(1) {
            top: 5%;
            left: 2%;
            font-size: 7rem;
            color: var(--primary);
            animation-delay: 0s;
        }
        
        .food-bg-element:nth-child(2) {
            bottom: 10%;
            right: 5%;
            font-size: 5rem;
            color: var(--accent);
            animation-delay: 2s;
        }
        
        .food-bg-element:nth-child(3) {
            top: 20%;
            right: 7%;
            font-size: 6rem;
            color: var(--primary-light);
            animation-delay: 4s;
        }
        
        .food-bg-element:nth-child(4) {
            bottom: 25%;
            left: 4%;
            font-size: 4rem;
            color: var(--accent);
            animation-delay: 6s;
        }

        .container {
            max-width: 500px;
            width: 100%;
            background: rgba(255, 255, 255, 0.92);
            backdrop-filter: blur(10px);
            border-radius: var(--border-radius);
            padding: 2rem 1.5rem;
            box-shadow: var(--shadow);
            text-align: center;
            position: relative;
            z-index: 2;
            display: flex;
            flex-direction: column;
            align-items: center;
            border: 1px solid rgba(255, 107, 53, 0.1);
        }

        .logo {
            margin-bottom: 1.5rem;
            position: relative;
        }
        
        .logo-icon {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
            width: 80px;
            height: 80px;
            border-radius: 20px; 
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 2.5rem;
            box-shadow: var(--shadow);
            transition: var(--transition);
        }
        
        .logo-icon:hover {
            transform: rotate(15deg) scale(1.05);
        }
        
        .logo-text {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--dark);
            letter-spacing: -0.5px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            display: inline-block;
        }
        
        .logo-text::after {
            content: "";
            position: absolute;
            bottom: -6px;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, var(--primary), var(--accent));
            border-radius: 2px;
            transform: scaleX(0.8);
            transition: var(--transition);
        }

        .logo:hover .logo-text::after {
            transform: scaleX(1);
        }

        .offline-icon-container {
            position: relative;
            margin: 1.5rem 0;
            width: 120px;
            height: 120px;
        }

        .offline-icon {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4.5rem;
            color: var(--detail);
            animation: fadePulse 3s ease-in-out infinite;
            z-index: 2;
        }
        
        .connection-ring {
            position: absolute;
            width: 100%;
            height: 100%;
            border: 3px dashed var(--detail);
            border-radius: 50%;
            opacity: 0.3;
            animation: rotate 20s linear infinite;
        }
        
        .food-icon {
            position: absolute;
            font-size: 2.5rem;
            color: var(--primary);
            animation: bounce 3s ease-in-out infinite;
        }
        
        .food-icon:nth-child(1) {
            top: -40px;
            left: 50%;
            transform: translateX(-50%);
            animation-delay: 0s;
        }
        
        .food-icon:nth-child(2) {
            bottom: -40px;
            left: 50%;
            transform: translateX(-50%);
            animation-delay: 0.5s;
        }
        
        .food-icon:nth-child(3) {
            top: 50%;
            right: -40px;
            transform: translateY(-50%);
            animation-delay: 1s;
        }
        
        .food-icon:nth-child(4) {
            top: 50%;
            left: -40px;
            transform: translateY(-50%);
            animation-delay: 1.5s;
        }

        h1 {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: var(--dark);
            font-weight: 700;
            line-height: 1.3;
        }

        .message {
            font-size: 1rem;
            color: var(--text);
            line-height: 1.5;
            margin-bottom: 1.5rem;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
            padding: 0 1rem;
        }

        .message::before {
            content: "";
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 70%;
            background: linear-gradient(to bottom, var(--primary), var(--accent));
            border-radius: 2px;
        }

        .actions {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 1rem;
            max-width: 400px;
            width: 100%;
            margin: 0 auto 1rem;
        }

        .btn {
            padding: 0.9rem 1.5rem;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: var(--transition);
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.8rem;
            min-width: 160px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
            color: white;
            box-shadow: 0 6px 20px rgba(255, 107, 53, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(255, 107, 53, 0.4);
        }

        .btn-secondary {
            background: white;
            color: var(--dark);
            border: 2px solid var(--primary-light);
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        }

        .btn-secondary:hover {
            background: rgba(255, 159, 28, 0.08);
            transform: translateY(-3px);
            box-shadow: 0 6px 18px rgba(255, 159, 28, 0.15);
        }

        .custom-message-box {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: white;
            padding: 25px 30px;
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
            z-index: 1000;
            text-align: center;
            font-family: 'Segoe UI', system-ui, sans-serif;
            color: var(--text);
            max-width: 380px;
            width: 90%;
            border: 1px solid rgba(255, 107, 53, 0.2);
            animation: popIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
        }

        .custom-message-box h3 {
            color: var(--primary);
            margin-bottom: 15px;
            font-size: 1.4rem;
            font-weight: 700;
        }

        .custom-message-box p {
            margin-bottom: 20px;
            font-size: 1rem;
            line-height: 1.5;
            color: var(--text);
        }

        .custom-message-box .button-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-top: 15px;
        }

        .custom-message-box .button-group button {
            width: 100%;
            padding: 12px 18px;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            border: none;
        }

        .custom-message-box .btn-whatsapp {
            background: linear-gradient(135deg, #25D366 0%, #1DA851 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
        }

        .custom-message-box .btn-whatsapp:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        }

        .custom-message-box .btn-phone {
            background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
        }

        .custom-message-box .btn-phone:hover {
            transform: translateY(-3px);
            box-shadow: 0 6样的6px 20px rgba(255, 107, 53, 0.4);
        }

        .custom-message-box .close-btn {
            background: linear-gradient(135deg, #A7A7A7 0%, #888888 100%);
            color: white;
            margin-top: 15px;
            padding: 12px 18px;
            border-radius: 10px;
            font-weight: 600;
            transition: var(--transition);
            box-shadow: 0 4px 15px rgba(167, 167, 167, 0.3);
        }

        .custom-message-box .close-btn:hover {
            background: linear-gradient(135deg, #888888 0%, #666666 100%);
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(167, 167, 167, 0.4);
        }

        @keyframes fadePulse {
            0%, 100% { opacity: 0.6; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.05); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            25% { transform: translateY(-15px) rotate(5deg); }
            50% { transform: translateY(0) rotate(0deg); }
            75% { transform: translateY(15px) rotate(-5deg); }
        }
        
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-15px); }
        }

        @keyframes popIn {
            0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
            70% { opacity: 1; transform: translate(-50%, -50%) scale(1.05); }
            100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        }

        footer {
            text-align: center;
            padding: 0.5rem 1rem 0.5rem;
            color: var(--detail);
            font-size: 0.8rem;
            width: 100%;
            margin-top: 1rem;
        }

        @media (max-width: 600px) {
            body {
                padding: 1rem;
            }
            
            .container {
                padding: 2rem 1rem;
            }
            
            .logo-text {
                font-size: 2.2rem;
            }
            
            h1 {
                font-size: 1.8rem;
            }
            
            .message {
                font-size: 0.95rem;
                line-height: 1.4;
            }
            
            .offline-icon-container {
                margin: 1rem 0;
                width: 100px;
                height: 100px;
            }

            .offline-icon {
                font-size: 3.8rem;
            }
            
            .actions {
                gap: 0.8rem;
            }
            
            .btn {
                padding: 0.8rem 1.2rem;
                font-size: 0.95rem;
                min-width: 140px;
            }

            .custom-message-box {
                padding: 20px;
            }

            .custom-message-box h3 {
                font-size: 1.2rem;
            }

            .custom-message-box p {
                font-size: 0.9rem;
            }

            .custom-message-box .button-group button {
                padding: 10px 15px;
                font-size: 0.95rem;
            }

            .custom-message-box .close-btn {
                padding: 10px 15px;
            }
        }
        
        @media (max-width: 400px) {
            .logo-icon {
                width: 70px;
                height: 70px;
                font-size: 2.2rem;
            }
            
            .logo-text {
                font-size: 2rem;
            }
            
            h1 {
                font-size: 1.6rem;
            }
        }