# UpdateSender

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **string** | From Name to update the sender | [optional] 
**email** | **string** | From Email to update the sender | [optional] 
**ips** | [**\Brevo\Client\Model\CreateSenderIps[]**](CreateSenderIps.md) | Only in case of dedicated IP, IPs to associate to the sender. If passed, will replace all the existing IPs. | [optional] 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


