<?php

require_once 'CloudinaryFilter.php';

use <PERSON>\Parser\Filter\CloudinaryFilter;
use <PERSON>\<PERSON>;

$docsDir = __DIR__ . '/';
$srcDir  = $docsDir . '../src/';

return new Sami(
    $srcDir,
    [
        'theme'                => 'cloudinary',
        'template_dirs'        => [$docsDir . 'themes'],
        'title'                => 'Cloudinary PHP Transformation Builder',
        'version'              => '2.1.2',
        'build_dir'            => $docsDir . 'build',
        'cache_dir'            => $docsDir . 'cache',
        'default_opened_level' => 2,
        'filter'               => new CloudinaryFilter(),
    ]
);
