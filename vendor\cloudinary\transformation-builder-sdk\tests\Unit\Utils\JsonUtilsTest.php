<?php
/**
 * This file is part of the Cloudinary PHP package.
 *
 * (c) Cloudinary
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Cloudinary\Test\Unit\Utils;

use Cloudinary\JsonUtils;
use Exception;
use PHPUnit\Framework\TestCase;

/**
 * Class JsonUtilsTest
 */
final class JsonUtilsTest extends TestCase
{
    public function testDecode()
    {
        self::assertEquals(
            ['foo' => 'bar'],
            JsonUtils::decode('{"foo": "bar"}')
        );

        $this->expectException(Exception::class);
        JsonUtils::decode('{NOT_A_JSON}');
    }
}
