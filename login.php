<?php
require_once __DIR__ . '/env_loader.php';
require_once __DIR__ . '/cookie-manager.php';
loadEnv();

// Configure PHP session cookies to be HttpOnly and Secure
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 1 : 0);
ini_set('session.cookie_samesite', 'Strict');
ini_set('session.use_only_cookies', 1);
ob_start();
session_start();
error_reporting(0);
ini_set('display_errors', 'Off');
set_exception_handler(function ($exception) {
    ob_clean();
    header('Content-Type: application/json');
    http_response_code(500);
    $response = [
        "success" => false,
        "message" => "An unhandled server error occurred.",
        "error_details" => $exception->getMessage()
    ];
    error_log("Unhandled exception: " . $exception->getMessage() . " in " . $exception->getFile() . " on line " . $exception->getLine());
    echo json_encode($response);
    exit();
});
ini_set('log_errors', 'On');
ini_set('error_log', __DIR__ . '/logs/php_errors.log');

// Get allowed origins from environment variable
$allowedOriginsEnv = $_ENV['ALLOWED_ORIGINS'] ?? 'http://localhost:8000';
$allowedOrigins = array_map('trim', explode(',', $allowedOriginsEnv));

// Add any additional hardcoded origins if needed (for backward compatibility)
$additionalOrigins = [
    'https://efd948bb4834.ngrok-free.app',
    'https://semi-automated-load-unload-extravaganza.kesug.com',
];

// Merge and remove duplicates
$allowedOrigins = array_unique(array_merge($allowedOrigins, $additionalOrigins));

error_log("Configured Allowed Origins: " . implode(', ', $allowedOrigins));

$requestOrigin = $_SERVER['HTTP_ORIGIN'] ?? '';
if (!empty($requestOrigin) && in_array($requestOrigin, $allowedOrigins)) {
    header("Access-Control-Allow-Origin: " . $requestOrigin);
} else {
    error_log("CORS: Request from disallowed origin: " . $requestOrigin);
}
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    ob_end_clean();
    exit();
}

header('Content-Type: application/json');

$supabaseUrl = "https://xswrokjllrkdyepluztn.supabase.co";
$supabaseServiceRoleKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inhzd3Jva2psbHJrZHllcGx1enRuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjczMDU4MywiZXhwIjoyMDY4MzA2NTgzfQ.5Ek5Rlj3Iozc476u9ebVTlREp6RtTRYqmK8Sq_1uzrA";

/**
 * Normalizes email address to lowercase and trims whitespace
 * @param string $email The email address to normalize
 * @return string The normalized email address
 */
function normalizeEmail($email) {
    return strtolower(trim($email));
}

$brevoConfig = [
    "api_key" => "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",
    "sender_email" => "<EMAIL>",
    "sender_name" => "MIKO App"
];
$brevoApiUrl = "https://api.brevo.com/v3/smtp/email";

/**
 * Fetches a user from the public.users table in Supabase by email.
 *
 * @param string $email The email of the user to retrieve.
 * @return array|null The user data as an associative array, or null if not found.
 */
function get_user_by_email($email) {
    global $supabaseUrl, $supabaseServiceRoleKey;

    // Normalize email to lowercase
    $email = normalizeEmail($email);

    $url = $supabaseUrl . "/rest/v1/users?email=eq." . urlencode($email);
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "apikey: $supabaseServiceRoleKey",
        "Authorization: Bearer $supabaseServiceRoleKey",
        "Content-Type: application/json",
        "Accept: application/json"
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode === 200) {
        $users = json_decode($response, true);
        return !empty($users) ? $users[0] : null;
    } else {
        error_log("Supabase API error fetching user by email {$email} from public.users: HTTP $httpCode - $response");
        return null;
    }
}

/**
 * Creates a new user in the public.users table in Supabase.
 * The password will be hashed before storage.
 *
 * @param string $email The user's email.
 * @param string|null $password The user's password (will be hashed).
 * @param string $name The user's name (required by schema, defaults to part of email).
 * @return int|bool The ID of the newly created user, or false on failure, or "User already exists" string.
 */
function create_user($email, $password = null, $name = null) {
    global $supabaseUrl, $supabaseServiceRoleKey;

    // Normalize email to lowercase
    $email = normalizeEmail($email);

    // Use a default name if not provided (e.g., from email prefix)
    if ($name === null) {
        $name = explode('@', $email)[0] ?? 'New User';
    }

    // Check if user already exists to avoid duplicate entries and provide specific message
    if (get_user_by_email($email)) {
        return "User already exists";
    }

    // Ensure password is provided since the database schema requires it
    if (empty($password)) {
        error_log("Cannot create user {$email}: password is required");
        return false;
    }

    $url = $supabaseUrl . "/rest/v1/users";
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

    $payload = [
        "name" => $name,
        "email" => $email,
        "password" => $hashedPassword,
        // Add other default fields as per your schema if needed, e.g., phone, type, avatar
        "type" => "customer", // Default type
        "avatar" => "/avatar/default.webp", // Default avatar
    ];

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "apikey: $supabaseServiceRoleKey",
        "Authorization: Bearer $supabaseServiceRoleKey",
        "Content-Type: application/json",
        "Prefer: return=representation" // Ensure Supabase returns the inserted row
    ]);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode === 201) {
        $userData = json_decode($response, true);
        return $userData[0]['id'] ?? true; // Return user ID if available, otherwise true for success
    } else {
        error_log("Supabase API error creating user {$email} in public.users: HTTP $httpCode - $response");
        return false;
    }
}

/**
 * Updates the password for a user in the public.users table in Supabase.
 *
 * @param string $email The email of the user to update.
 * @param string $newPassword The new password (will be hashed).
 * @return bool True on success, false on failure.
 */
function update_user_password_in_supabase($email, $newPassword) {
    global $supabaseUrl, $supabaseServiceRoleKey;
    $url = $supabaseUrl . "/rest/v1/users?email=eq." . urlencode($email);
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    $payload = [
        "password" => $hashedPassword,
        "updated_at" => time() // Update timestamp
    ];

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PATCH");
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "apikey: $supabaseServiceRoleKey",
        "Authorization: Bearer $supabaseServiceRoleKey",
        "Content-Type: application/json",
        "Accept: application/json"
    ]);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode === 200 || $httpCode === 204) { // 200 for successful PATCH with content, 204 for no content
        return true;
    } else {
        error_log("Supabase API error updating password for user {$email} in public.users: HTTP $httpCode - $response");
        return false;
    }
}

/**
 * Deletes existing pending requests from both access_tokens and sent_emails tables.
 *
 * @param string $email The user's email.
 * @param string $type The type of request.
 * @throws Exception If there's an issue with Supabase API.
 */
function deleteExistingPendingRequests($email, $type) {
    global $supabaseUrl, $supabaseServiceRoleKey;

    // Delete from access_tokens table
    $deleteUrl = $supabaseUrl . "/rest/v1/access_tokens?user_email=eq." . urlencode($email) . "&type=eq." . urlencode($type) . "&status=eq.pending";
    $chDelete = curl_init($deleteUrl);
    curl_setopt($chDelete, CURLOPT_CUSTOMREQUEST, "DELETE");
    curl_setopt($chDelete, CURLOPT_HTTPHEADER, [
        "apikey: $supabaseServiceRoleKey",
        "Authorization: Bearer $supabaseServiceRoleKey",
        "Content-Type: application/json",
        "Accept: application/json"
    ]);
    curl_setopt($chDelete, CURLOPT_RETURNTRANSFER, true);
    $deleteResponse = curl_exec($chDelete);
    $httpCodeDelete = curl_getinfo($chDelete, CURLINFO_HTTP_CODE);
    curl_close($chDelete);

    if ($httpCodeDelete !== 204 && $httpCodeDelete !== 200) {
        error_log("Failed to delete existing tokens for $email and type $type from access_tokens. Response: $deleteResponse");
        throw new Exception("Failed to delete existing tokens from access_tokens.");
    }

    // Delete from sent_emails table
    $deleteUrl = $supabaseUrl . "/rest/v1/sent_emails?user_email=eq." . urlencode($email) . "&type=eq." . urlencode($type) . "&status=eq.pending";
    $chDelete = curl_init($deleteUrl);
    curl_setopt($chDelete, CURLOPT_CUSTOMREQUEST, "DELETE");
    curl_setopt($chDelete, CURLOPT_HTTPHEADER, [
        "apikey: $supabaseServiceRoleKey",
        "Authorization: Bearer $supabaseServiceRoleKey",
        "Content-Type: application/json",
        "Accept: application/json"
    ]);
    curl_setopt($chDelete, CURLOPT_RETURNTRANSFER, true);
    $deleteResponse = curl_exec($chDelete);
    $httpCodeDelete = curl_getinfo($chDelete, CURLINFO_HTTP_CODE);
    curl_close($chDelete);

    if ($httpCodeDelete !== 204 && $httpCodeDelete !== 200) {
        error_log("Failed to delete existing entries for $email and type $type from sent_emails. Response: $deleteResponse");
        throw new Exception("Failed to delete existing entries from sent_emails.");
    }
}

/**
 * Inserts a record into the sent_emails table.
 *
 * @param string $email The user's email.
 * @param string|null $otp The OTP if applicable.
 * @param string|null $token The token if applicable.
 * @param string $type The type of email.
 * @param int $expiresAt The expiration timestamp.
 * @param int $createdAt The creation timestamp.
 * @throws Exception If there's an issue with Supabase API.
 */
function insertIntoSentEmails($email, $otp, $token, $type, $expiresAt, $createdAt) {
    global $supabaseUrl, $supabaseServiceRoleKey;

    $insertUrl = $supabaseUrl . "/rest/v1/sent_emails";
    $payload = [
        "user_email" => $email,
        "type" => $type,
        "expires_at" => $expiresAt,
        "created_at" => $createdAt,
        "status" => "pending"
    ];

    if ($otp !== null) {
        $payload["otp"] = $otp;
    }

    if ($token !== null) {
        $payload["token"] = $token;
    }

    $chInsert = curl_init($insertUrl);
    curl_setopt($chInsert, CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($chInsert, CURLOPT_POSTFIELDS, json_encode($payload));
    curl_setopt($chInsert, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($chInsert, CURLOPT_HTTPHEADER, [
        "apikey: $supabaseServiceRoleKey",
        "Authorization: Bearer $supabaseServiceRoleKey",
        "Content-Type: application/json",
        "Prefer: return=representation"
    ]);
    $insertResponse = curl_exec($chInsert);
    $httpCodeInsert = curl_getinfo($chInsert, CURLINFO_HTTP_CODE);
    curl_close($chInsert);

    if ($httpCodeInsert !== 201) {
        error_log("Failed to insert into sent_emails for $email and type $type. Response: $insertResponse");
        throw new Exception("Failed to insert into sent_emails.");
    }
}

/**
 * Updates the status of sent_emails records.
 *
 * @param string $email The user's email.
 * @param string $type The type of email.
 * @param string $status The new status ('sent', 'used', 'expired', 'failed').
 * @param string|null $otp The OTP if applicable.
 * @param string|null $token The token if applicable.
 */
function updateSentEmailsStatus($email, $type, $status, $otp = null, $token = null) {
    global $supabaseUrl, $supabaseServiceRoleKey;

    $updateUrl = $supabaseUrl . "/rest/v1/sent_emails?user_email=eq." . urlencode($email) . "&type=eq." . urlencode($type);

    if ($otp !== null) {
        $updateUrl .= "&otp=eq." . urlencode($otp);
    }

    if ($token !== null) {
        $updateUrl .= "&token=eq." . urlencode($token);
    }

    $payload = ["status" => $status];

    $chUpdate = curl_init($updateUrl);
    curl_setopt($chUpdate, CURLOPT_CUSTOMREQUEST, "PATCH");
    curl_setopt($chUpdate, CURLOPT_POSTFIELDS, json_encode($payload));
    curl_setopt($chUpdate, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($chUpdate, CURLOPT_HTTPHEADER, [
        "apikey: $supabaseServiceRoleKey",
        "Authorization: Bearer $supabaseServiceRoleKey",
        "Content-Type: application/json",
        "Accept: application/json"
    ]);
    $updateResponse = curl_exec($chUpdate);
    $httpCodeUpdate = curl_getinfo($chUpdate, CURLINFO_HTTP_CODE);
    curl_close($chUpdate);

    if ($httpCodeUpdate !== 200 && $httpCodeUpdate !== 204) {
        error_log("Failed to update sent_emails status for $email and type $type to $status. Response: $updateResponse");
    }
}


/**
 * Generates a unique token and stores it in both access_tokens and sent_emails tables in Supabase.
 *
 * @param string $email The user's email associated with the token.
 * @param string $type The type of token (e.g., 'login_link', 'password_reset').
 * @param int $expiresInMinutes The expiration time in minutes.
 * @return string The generated token.
 * @throws Exception If there's an issue with Supabase API.
 */
function generateAndStoreToken($email, $type, $expiresInMinutes = 60) {
    global $supabaseUrl, $supabaseServiceRoleKey;

    // Normalize email to lowercase
    $email = normalizeEmail($email);

    $token = bin2hex(random_bytes(32));
    $expiresAt = time() + ($expiresInMinutes * 60);
    $createdAt = time();

    // Delete existing pending tokens of this type for the user in both tables
    deleteExistingPendingRequests($email, $type);

    // Insert new token into access_tokens table
    $insertUrl = $supabaseUrl . "/rest/v1/access_tokens";
    $payload = [
        "user_email" => $email,
        "token" => $token,
        "type" => $type,
        "expires_at" => $expiresAt,
        "created_at" => $createdAt,
        "status" => "pending"
    ];
    $chInsert = curl_init($insertUrl);
    curl_setopt($chInsert, CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($chInsert, CURLOPT_POSTFIELDS, json_encode($payload));
    curl_setopt($chInsert, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($chInsert, CURLOPT_HTTPHEADER, [
        "apikey: $supabaseServiceRoleKey",
        "Authorization: Bearer $supabaseServiceRoleKey",
        "Content-Type: application/json",
        "Prefer: return=representation"
    ]);
    $insertResponse = curl_exec($chInsert);
    $httpCodeInsert = curl_getinfo($chInsert, CURLINFO_HTTP_CODE);
    curl_close($chInsert);

    if ($httpCodeInsert !== 201) {
        error_log("Failed to insert token for $email and type $type into access_tokens. Response: $insertResponse");
        throw new Exception("Failed to insert token into access_tokens.");
    }

    // Insert into sent_emails table for tracking
    insertIntoSentEmails($email, null, $token, $type, $expiresAt, $createdAt);

    return $token;
}

/**
 * Generates a unique OTP and stores it in both access_tokens and sent_emails tables in Supabase.
 *
 * @param string $email The user's email associated with the OTP.
 * @param int $expiresInMinutes The expiration time in minutes.
 * @return string The generated OTP.
 * @throws Exception If there's an issue with Supabase API.
 */
function generateAndStoreOtp($email, $expiresInMinutes = 5) {
    global $supabaseUrl, $supabaseServiceRoleKey;

    // Normalize email to lowercase
    $email = normalizeEmail($email);

    $otp = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
    $expiresAt = time() + ($expiresInMinutes * 60);
    $createdAt = time();

    // Delete existing pending OTPs for the user from both tables
    deleteExistingPendingRequests($email, 'otp_verification');

    // Insert new OTP into access_tokens table
    $insertUrl = $supabaseUrl . "/rest/v1/access_tokens";
    $payload = [
        "user_email" => $email,
        "otp" => $otp,
        "type" => "otp_verification",
        "expires_at" => $expiresAt,
        "created_at" => $createdAt,
        "status" => "pending"
    ];
    $chInsert = curl_init($insertUrl);
    curl_setopt($chInsert, CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($chInsert, CURLOPT_POSTFIELDS, json_encode($payload));
    curl_setopt($chInsert, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($chInsert, CURLOPT_HTTPHEADER, [
        "apikey: $supabaseServiceRoleKey",
        "Authorization: Bearer $supabaseServiceRoleKey",
        "Content-Type: application/json",
        "Prefer: return=representation"
    ]);
    $insertResponse = curl_exec($chInsert);
    $httpCodeInsert = curl_getinfo($chInsert, CURLINFO_HTTP_CODE);
    curl_close($chInsert);

    if ($httpCodeInsert !== 201) {
        error_log("Failed to insert OTP for $email into access_tokens. Response: $insertResponse");
        throw new Exception("Failed to insert OTP into access_tokens.");
    }

    // Insert into sent_emails table for tracking
    insertIntoSentEmails($email, $otp, null, 'otp_verification', $expiresAt, $createdAt);

    return $otp;
}

/**
 * Validates a token against the public.access_tokens table in Supabase.
 * Marks the token as 'used' if valid and not expired.
 *
 * @param string $email The user's email associated with the token.
 * @param string $token The token to validate.
 * @param string $type The type of token.
 * @return array An associative array with 'success' (bool), 'userId' (int|null), and 'message' (string).
 */
function validateToken($email, $token, $type) {
    global $supabaseUrl, $supabaseServiceRoleKey;

    // Normalize email to lowercase
    $email = normalizeEmail($email);

    try {
        // Select token from Supabase
        $selectUrl = $supabaseUrl . "/rest/v1/access_tokens?user_email=eq." . urlencode($email) . "&token=eq." . urlencode($token) . "&type=eq." . urlencode($type) . "&status=eq.pending";
        $chSelect = curl_init($selectUrl);
        curl_setopt($chSelect, CURLOPT_HTTPHEADER, [
            "apikey: $supabaseServiceRoleKey",
            "Authorization: Bearer $supabaseServiceRoleKey",
            "Content-Type: application/json",
            "Accept: application/json"
        ]);
        curl_setopt($chSelect, CURLOPT_RETURNTRANSFER, true);
        $selectResponse = curl_exec($chSelect);
        $httpCodeSelect = curl_getinfo($chSelect, CURLINFO_HTTP_CODE);
        curl_close($chSelect);

        if ($httpCodeSelect !== 200) {
            error_log("Failed to select token for $email from Supabase. Response: $selectResponse");
            return ['success' => false, 'userId' => null, 'message' => 'Failed to validate token.'];
        }

        $rows = json_decode($selectResponse, true);
        if (empty($rows)) {
            error_log("Token for $email of type $type not found or already used/invalid in Supabase.");
            return ['success' => false, 'userId' => null, 'message' => 'Invalid or already used token.'];
        }

        $row = $rows[0];
        if ($row['expires_at'] <= time()) {
            // Mark token as expired in Supabase
            $updateUrl = $supabaseUrl . "/rest/v1/access_tokens?id=eq." . $row['id'];
            $updatePayload = ['status' => 'expired'];
            $chUpdate = curl_init($updateUrl);
            curl_setopt($chUpdate, CURLOPT_CUSTOMREQUEST, "PATCH");
            curl_setopt($chUpdate, CURLOPT_POSTFIELDS, json_encode($updatePayload));
            curl_setopt($chUpdate, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($chUpdate, CURLOPT_HTTPHEADER, [
                "apikey: $supabaseServiceRoleKey",
                "Authorization: Bearer $supabaseServiceRoleKey",
                "Content-Type: application/json",
                "Accept: application/json"
            ]);
            curl_exec($chUpdate);
            curl_close($chUpdate);

            // Also update sent_emails table
            updateSentEmailsStatus($email, $type, 'expired', null, $token);

            error_log("Token for $email of type $type expired in Supabase.");
            return ['success' => false, 'userId' => null, 'message' => 'Token expired.'];
        }

        // Mark token as used in Supabase upon successful validation
        $updateUrl = $supabaseUrl . "/rest/v1/access_tokens?id=eq." . $row['id'];
        $updatePayload = ['status' => 'used'];
        $chUpdate = curl_init($updateUrl);
        curl_setopt($chUpdate, CURLOPT_CUSTOMREQUEST, "PATCH");
        curl_setopt($chUpdate, CURLOPT_POSTFIELDS, json_encode($updatePayload));
        curl_setopt($chUpdate, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($chUpdate, CURLOPT_HTTPHEADER, [
            "apikey: $supabaseServiceRoleKey",
            "Authorization: Bearer $supabaseServiceRoleKey",
            "Content-Type: application/json",
            "Accept: application/json"
        ]);
        curl_exec($chUpdate); // Execute to mark as used
        curl_close($chUpdate);

        // Also update sent_emails table
        updateSentEmailsStatus($email, $type, 'used', null, $token);


        if ($type === 'login_link') {
            // Get user ID from public.users table
            $user = get_user_by_email($email);
            if ($user) {
                // Create secure session using cookie manager and Supabase
                $sessionResult = createUserSession($user);
                if ($sessionResult['success']) {
                    error_log("User {$email} (ID: {$user['id']}) logged in via magic link. Secure session created.");
                    return ['success' => true, 'userId' => $user['id'], 'session_id' => $sessionResult['session_id']];
                } else {
                    error_log("Failed to create session for user {$email}: " . $sessionResult['message']);
                    return ['success' => false, 'userId' => null, 'message' => 'Failed to create session'];
                }
            } else {
                error_log("User not found in public.users for email {$email} after login_link token validation. Cannot log in.");
                return ['success' => false, 'userId' => null, 'message' => 'User not found after token validation.'];
            }
        } elseif ($type === 'password_reset') {
            return ['success' => true, 'userId' => null, 'message' => 'Password reset token is valid.'];
        }

        return ['success' => false, 'userId' => null, 'message' => 'Invalid token type.'];
    } catch (Exception $e) {
        error_log("Error validating token: " . $e->getMessage());
        return ['success' => false, 'userId' => null, 'message' => 'Failed to validate token.'];
    }
}

/**
 * Validates an OTP against the public.access_tokens table in Supabase.
 * Marks the OTP as 'used' if valid and not expired.
 *
 * @param string $email The user's email associated with the OTP.
 * @param string $otp The OTP to validate.
 * @return bool True on successful validation, false otherwise.
 */
function validateOtp($email, $otp) {
    global $supabaseUrl, $supabaseServiceRoleKey;

    // Normalize email to lowercase
    $email = normalizeEmail($email);

    try {
        // Select OTP from Supabase
        $selectUrl = $supabaseUrl . "/rest/v1/access_tokens?user_email=eq." . urlencode($email) . "&otp=eq." . urlencode($otp) . "&type=eq.otp_verification&status=eq.pending";
        $chSelect = curl_init($selectUrl);
        curl_setopt($chSelect, CURLOPT_HTTPHEADER, [
            "apikey: $supabaseServiceRoleKey",
            "Authorization: Bearer $supabaseServiceRoleKey",
            "Content-Type: application/json",
            "Accept: application/json"
        ]);
        curl_setopt($chSelect, CURLOPT_RETURNTRANSFER, true);
        $selectResponse = curl_exec($chSelect);
        $httpCodeSelect = curl_getinfo($chSelect, CURLINFO_HTTP_CODE);
        curl_close($chSelect);

        if ($httpCodeSelect !== 200) {
            error_log("Failed to select OTP for $email from Supabase. Response: $selectResponse");
            return false;
        }

        $rows = json_decode($selectResponse, true);
        if (empty($rows)) {
            error_log("OTP for $email not found or already used/invalid in Supabase.");
            return false;
        }

        $row = $rows[0];
        if ($row['expires_at'] <= time()) {
            // Mark OTP as expired in Supabase
            $updateUrl = $supabaseUrl . "/rest/v1/access_tokens?id=eq." . $row['id'];
            $updatePayload = ['status' => 'expired'];
            $chUpdate = curl_init($updateUrl);
            curl_setopt($chUpdate, CURLOPT_CUSTOMREQUEST, "PATCH");
            curl_setopt($chUpdate, CURLOPT_POSTFIELDS, json_encode($updatePayload));
            curl_setopt($chUpdate, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($chUpdate, CURLOPT_HTTPHEADER, [
                "apikey: $supabaseServiceRoleKey",
                "Authorization: Bearer $supabaseServiceRoleKey",
                "Content-Type: application/json",
                "Accept: application/json"
            ]);
            curl_exec($chUpdate);
            curl_close($chUpdate);

            // Also update sent_emails table
            updateSentEmailsStatus($email, 'otp_verification', 'expired', $otp, null);

            error_log("OTP for $email expired in Supabase.");
            return false;
        }

        // Mark OTP as used in Supabase
        $updateUrl = $supabaseUrl . "/rest/v1/access_tokens?id=eq." . $row['id'];
        $updatePayload = ['status' => 'used'];
        $chUpdate = curl_init($updateUrl);
        curl_setopt($chUpdate, CURLOPT_CUSTOMREQUEST, "PATCH");
        curl_setopt($chUpdate, CURLOPT_POSTFIELDS, json_encode($updatePayload));
        curl_setopt($chUpdate, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($chUpdate, CURLOPT_HTTPHEADER, [
            "apikey: $supabaseServiceRoleKey",
            "Authorization: Bearer $supabaseServiceRoleKey",
            "Content-Type: application/json",
            "Accept: application/json"
        ]);
        curl_exec($chUpdate);
        curl_close($chUpdate);

        // Also update sent_emails table
        updateSentEmailsStatus($email, 'otp_verification', 'used', $otp, null);

        return true;
    } catch (Exception $e) {
        error_log("Error validating OTP: " . $e->getMessage());
        return false;
    }
}

function get_email_template($templateName, $placeholders = []) {
    $templatePath = __DIR__ . '/email_templates/' . $templateName;
    if (!file_exists($templatePath)) {
        error_log("Email template not found: " . $templatePath);
        throw new Exception("Email template not found: " . $templateName);
    }
    $htmlContent = file_get_contents($templatePath);
    if ($htmlContent === false) {
        error_log("Failed to read email template: " . $templatePath);
        throw new Exception("Failed to read email template: " . $templateName);
    }
    foreach ($placeholders as $placeholder => $value) {
        $htmlContent = str_replace($placeholder, $value, $htmlContent);
    }
    return $htmlContent;
}

function generateQrCodeUrl($dataToEncode) {
    $qrCodeApiUrl = "https://chart.googleapis.com/chart?chs=200x200&cht=qr&chl=" . urlencode($dataToEncode);
    return $qrCodeApiUrl;
}

function sendEmailViaBrevo($recipientEmail, $subject, $htmlContent) {
    global $brevoConfig, $brevoApiUrl;
    if (empty($brevoConfig["api_key"])) {
        error_log("Brevo API Key is not configured.");
        return ["success" => false, "message" => "Brevo API key missing. Please configure it in server.php."];
    }
    $headers = [
        "accept: application/json",
        "api-key: " . $brevoConfig["api_key"],
        "content-type: application/json"
    ];
    $payload = [
        "sender" => [
            "name" => $brevoConfig["sender_name"],
            "email" => $brevoConfig["sender_email"]
        ],
        "to" => [
            [
                "email" => $recipientEmail
            ]
        ],
        "subject" => $subject,
        "htmlContent" => $htmlContent
    ];
    $ch = curl_init($brevoApiUrl);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    if ($curlError) {
        error_log("Network or Brevo API connection error: " . $curlError);
        return ["success" => false, "message" => "Network error or Brevo API connection issue: " . $curlError];
    }
    $responseData = json_decode($response, true);
    if ($httpCode == 201 && isset($responseData["messageId"])) {
        error_log("Brevo: Email successfully sent to " . $recipientEmail . " with subject '" . $subject . "'. Message ID: " . $responseData['messageId']);
        return ["success" => true, "message" => "Email sent successfully via Brevo."];
    } else {
        $errorMessage = isset($responseData['message']) ? $responseData['message'] : 'Unknown Brevo error';
        error_log("Brevo API error sending email to " . $recipientEmail . ": " . $errorMessage . ". Response: " . $response);
        return ["success" => false, "message" => "Brevo API error: " . $errorMessage];
    }
}

$input = json_decode(file_get_contents('php://input'), true);
$email = normalizeEmail($input['email'] ?? '');
$path = $_GET['path'] ?? '';
error_log("Received path parameter: '" . $path . "'");

// Endpoints that don't require email
$noEmailRequired = [
    '/api/skip-authentication',
    '/api/check-skip-status',
    '/api/clear-skip',
    '/api/check-session',
    '/api/logout',
    '/api/get-user-info'
];

// Set default response based on whether email is required
if (in_array($path, $noEmailRequired)) {
    $response = ["success" => false, "message" => "Invalid API endpoint."];
} else {
    $response = ["success" => false, "message" => "Invalid API endpoint or missing email."];
}

// No local database initialization needed anymore

$secureFlag = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off');

// Rate limiting configuration
$rateLimits = [
    'otp' => ['cooldown' => 60, 'cookie' => 'miko_otp_limit'],
    'login_link' => ['cooldown' => 120, 'cookie' => 'miko_login_limit'],
    'password_reset' => ['cooldown' => 180, 'cookie' => 'miko_reset_limit']
];

/**
 * Verify user password against database
 * @param string $email User email
 * @param string $password Plain text password
 * @return array|false User data if valid, false if invalid
 */
function verifyUserPassword($email, $password) {
    $user = get_user_by_email($email);

    if (!$user) {
        error_log("Password verification failed: User not found for email $email");
        return false;
    }

    if (!isset($user['password']) || empty($user['password'])) {
        error_log("Password verification failed: No password set for user $email");
        return false;
    }

    // Verify password using PHP's password_verify function
    if (password_verify($password, $user['password'])) {
        error_log("Password verification successful for user $email");
        return $user;
    } else {
        error_log("Password verification failed: Invalid password for user $email");
        return false;
    }
}

/**
 * Generate a secure session token
 * @return string
 */
function generateSessionToken() {
    return bin2hex(random_bytes(32));
}

/**
 * Create a guest session and store in database
 * @return array
 */
function createGuestSession() {
    global $supabaseUrl, $supabaseServiceRoleKey;

    try {
        $guestToken = generateSessionToken();
        $expiresAt = date('c', strtotime('+30 days'));

        // Store guest session in Supabase guests table
        $guestData = [
            'guest_token' => $guestToken,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'location_granted' => false,
            'setup_completed' => false,
            'expires_at' => $expiresAt
        ];

        $url = $supabaseUrl . '/rest/v1/guests';
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($guestData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "apikey: $supabaseServiceRoleKey",
            "Authorization: Bearer $supabaseServiceRoleKey",
            "Content-Type: application/json",
            "Prefer: return=representation"
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode >= 200 && $httpCode < 300) {
            // Store guest session in secure cookie
            $cookieData = [
                'guest_token' => $guestToken,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'expires_at' => $expiresAt,
                'is_guest' => true
            ];

            $cookieSet = SecureCookieManager::setCookie('guest_session', $cookieData, 30 * 24 * 60); // 30 days

            if ($cookieSet) {
                error_log("Guest session created with token: $guestToken");
                return ['success' => true, 'guest_token' => $guestToken, 'expires_at' => $expiresAt];
            } else {
                error_log("Failed to set guest session cookie");
                return ['success' => false, 'message' => 'Failed to set guest session cookie'];
            }
        } else {
            error_log("Failed to create guest session in database: HTTP $httpCode - $response");
            return ['success' => false, 'message' => 'Failed to create guest session'];
        }
    } catch (Exception $e) {
        error_log("Error creating guest session: " . $e->getMessage());
        return ['success' => false, 'message' => 'Guest session creation failed'];
    }
}

/**
 * Get guest session from database
 * @param string $guestToken
 * @return array|null
 */
function getGuestSession($guestToken) {
    global $supabaseUrl, $supabaseServiceRoleKey;

    $url = $supabaseUrl . "/rest/v1/guests?guest_token=eq." . urlencode($guestToken);
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "apikey: $supabaseServiceRoleKey",
        "Authorization: Bearer $supabaseServiceRoleKey",
        "Content-Type: application/json",
        "Accept: application/json"
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode === 200) {
        $guests = json_decode($response, true);
        return !empty($guests) ? $guests[0] : null;
    } else {
        error_log("Error fetching guest session: HTTP $httpCode - $response");
        return null;
    }
}

/**
 * Update guest session data
 * @param string $guestToken
 * @param array $updateData
 * @return bool
 */
function updateGuestSession($guestToken, $updateData) {
    global $supabaseUrl, $supabaseServiceRoleKey;

    $updateData['updated_at'] = date('c');

    $url = $supabaseUrl . "/rest/v1/guests?guest_token=eq." . urlencode($guestToken);
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PATCH");
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($updateData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "apikey: $supabaseServiceRoleKey",
        "Authorization: Bearer $supabaseServiceRoleKey",
        "Content-Type: application/json"
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode >= 200 && $httpCode < 300) {
        return true;
    } else {
        error_log("Error updating guest session: HTTP $httpCode - $response");
        return false;
    }
}

/**
 * Create a new session in Supabase and secure cookie
 * @param array $user User data
 * @return array Session result
 */
function createUserSession($user) {
    global $supabaseUrl, $supabaseServiceRoleKey;

    try {
        // Check if user has auth_uuid
        if (!isset($user['auth_uuid']) || empty($user['auth_uuid'])) {
            error_log("User missing auth_uuid field for email: " . ($user['email'] ?? 'unknown'));
            return ['success' => false, 'message' => 'User missing auth_uuid field'];
        }

        $sessionId = generateSessionToken();
        $expiresAt = date('c', strtotime('+14 days')); // ISO 8601 format

        // Store session in Supabase sessions table
        $sessionData = [
            'session_id' => $sessionId,
            'user_auth_uuid' => $user['auth_uuid'],
            'expires_at' => $expiresAt
        ];

        $url = $supabaseUrl . '/rest/v1/sessions';
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($sessionData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "apikey: $supabaseServiceRoleKey",
            "Authorization: Bearer $supabaseServiceRoleKey",
            "Content-Type: application/json",
            "Prefer: return=representation"
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode >= 200 && $httpCode < 300) {
            // Store session in secure cookie
            $cookieData = [
                'session_id' => $sessionId,
                'user_id' => $user['id'],
                'auth_uuid' => $user['auth_uuid'],
                'email' => $user['email'],
                'name' => $user['name'] ?? '',
                'expires_at' => $expiresAt
            ];

            $cookieSet = SecureCookieManager::setCookie('user_session', $cookieData, 14 * 24 * 60); // 14 days

            if ($cookieSet) {
                error_log("Session created for user {$user['email']} (ID: {$user['id']})");
                return ['success' => true, 'session_id' => $sessionId, 'user' => $user];
            } else {
                error_log("Failed to set session cookie for user {$user['email']}. Headers sent: " . (headers_sent() ? 'yes' : 'no'));
                // Even if cookie setting fails, the session is created in Supabase, so we can still consider it a success
                // The user can still be authenticated via the session validation
                error_log("Session created in Supabase but cookie failed for user {$user['email']} (ID: {$user['id']})");
                return ['success' => true, 'session_id' => $sessionId, 'user' => $user, 'cookie_warning' => 'Session created but cookie setting failed'];
            }
        } else {
            error_log("Failed to create session in Supabase: HTTP $httpCode - $response");
            return ['success' => false, 'message' => 'Failed to create session'];
        }
    } catch (Exception $e) {
        error_log("Error creating session: " . $e->getMessage());
        return ['success' => false, 'message' => 'Session creation failed'];
    }
}

/**
 * Validate and refresh session
 * @return array Session validation result
 */
function validateAndRefreshSession() {
    global $supabaseUrl, $supabaseServiceRoleKey;

    try {
        $sessionData = SecureCookieManager::getCookie('user_session');

        if (!$sessionData || !isset($sessionData['session_id'])) {
            return ['success' => false, 'authenticated' => false, 'message' => 'No session found'];
        }

        // Check session in Supabase
        $url = $supabaseUrl . '/rest/v1/sessions?session_id=eq.' . urlencode($sessionData['session_id']);
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "apikey: $supabaseServiceRoleKey",
            "Authorization: Bearer $supabaseServiceRoleKey",
            "Content-Type: application/json"
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            SecureCookieManager::deleteCookie('user_session');
            return ['success' => false, 'authenticated' => false, 'message' => 'Session not found'];
        }

        $sessions = json_decode($response, true);
        if (empty($sessions)) {
            SecureCookieManager::deleteCookie('user_session');
            return ['success' => false, 'authenticated' => false, 'message' => 'Session not found'];
        }

        $session = $sessions[0];
        $expiresAt = strtotime($session['expires_at']);
        $now = time();

        if ($expiresAt <= $now) {
            // Session expired, clean up
            destroyUserSession($sessionData['session_id']);
            return ['success' => false, 'authenticated' => false, 'message' => 'Session expired'];
        }

        // Session is valid, extend it to 14 more days
        $newExpiresAt = date('c', strtotime('+14 days'));
        $updateData = ['expires_at' => $newExpiresAt];

        $updateUrl = $supabaseUrl . '/rest/v1/sessions?session_id=eq.' . urlencode($sessionData['session_id']);
        $chUpdate = curl_init($updateUrl);
        curl_setopt($chUpdate, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($chUpdate, CURLOPT_CUSTOMREQUEST, 'PATCH');
        curl_setopt($chUpdate, CURLOPT_POSTFIELDS, json_encode($updateData));
        curl_setopt($chUpdate, CURLOPT_HTTPHEADER, [
            "apikey: $supabaseServiceRoleKey",
            "Authorization: Bearer $supabaseServiceRoleKey",
            "Content-Type: application/json"
        ]);

        curl_exec($chUpdate);
        curl_close($chUpdate);

        // Update cookie with new expiration
        $sessionData['expires_at'] = $newExpiresAt;
        SecureCookieManager::setCookie('user_session', $sessionData, 14 * 24 * 60);

        return [
            'success' => true,
            'authenticated' => true,
            'user' => [
                'id' => $sessionData['user_id'],
                'auth_uuid' => $sessionData['auth_uuid'],
                'email' => $sessionData['email'],
                'name' => $sessionData['name']
            ],
            'expires_at' => $newExpiresAt
        ];

    } catch (Exception $e) {
        error_log("Error validating session: " . $e->getMessage());
        return ['success' => false, 'authenticated' => false, 'message' => 'Session validation failed'];
    }
}

/**
 * Destroy user session
 * @param string $sessionId
 */
function destroyUserSession($sessionId = null) {
    global $supabaseUrl, $supabaseServiceRoleKey;

    try {
        if (!$sessionId) {
            $sessionData = SecureCookieManager::getCookie('user_session');
            $sessionId = $sessionData['session_id'] ?? null;
        }

        if ($sessionId) {
            // Remove session from Supabase
            $url = $supabaseUrl . '/rest/v1/sessions?session_id=eq.' . urlencode($sessionId);
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                "apikey: $supabaseServiceRoleKey",
                "Authorization: Bearer $supabaseServiceRoleKey",
                "Content-Type: application/json"
            ]);

            curl_exec($ch);
            curl_close($ch);
        }

        // Clear session cookie
        SecureCookieManager::deleteCookie('user_session');

    } catch (Exception $e) {
        error_log("Error destroying session: " . $e->getMessage());
    }
}

/**
 * Check if user can make a request based on secure cookie rate limiting
 * @param string $type The type of request (otp, login_link, password_reset)
 * @return array ['allowed' => bool, 'remaining_time' => int]
 */
function checkRateLimit($type) {
    global $rateLimits;

    if (!isset($rateLimits[$type])) {
        return ['allowed' => false, 'remaining_time' => 0];
    }

    $config = $rateLimits[$type];
    $cookieName = $config['cookie'];
    $cooldown = $config['cooldown'];

    if (!isset($_COOKIE[$cookieName])) {
        return ['allowed' => true, 'remaining_time' => 0];
    }

    $lastRequestTime = intval($_COOKIE[$cookieName]);
    $currentTime = time();
    $elapsedTime = $currentTime - $lastRequestTime;

    if ($elapsedTime >= $cooldown) {
        return ['allowed' => true, 'remaining_time' => 0];
    }

    $remainingTime = $cooldown - $elapsedTime;
    return ['allowed' => false, 'remaining_time' => $remainingTime];
}

/**
 * Set rate limit cookie after successful request
 * @param string $type The type of request
 */
function setRateLimit($type) {
    global $rateLimits, $secureFlag;

    if (!isset($rateLimits[$type])) {
        return;
    }

    $config = $rateLimits[$type];
    $cookieName = $config['cookie'];
    $cooldown = $config['cooldown'];

    // Set secure HTTP-only cookie
    setcookie(
        $cookieName,
        time(),
        time() + $cooldown + 60, // Cookie expires slightly after cooldown
        "/",
        "",
        $secureFlag,  // HTTPS only if connection is secure
        true   // HTTP-only (blocks JS access)
    );
}

try {
    if (isset($_GET['action']) && $_GET['action'] === 'skip_login') {
        // Set secure HTTP-only cookie (30 days)
        setcookie(
            'skipped_login',
            '1',
            time() + 2592000, // 30 days in seconds
            "/",
            "",
            $secureFlag,  // HTTPS only if connection is secure
            true   // HTTP-only (blocks JS access)
        );

        echo json_encode(['success' => true]);
        exit;
    }

    // Handle endpoints that don't require email first
    switch ($path) {
        case '/api/skip-authentication':
            // Skip authentication and create guest session
            try {
                $guestSessionResult = createGuestSession();

                if ($guestSessionResult['success']) {
                    // Also set the old skip cookie for backward compatibility
                    $skipData = [
                        'skipped' => true,
                        'timestamp' => time(),
                        'expires_at' => date('c', strtotime('+30 days')),
                        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? ''
                    ];
                    SecureCookieManager::setCookie('skip_auth', $skipData, 30 * 24 * 60);

                    $response = [
                        "success" => true,
                        "message" => "Authentication skipped successfully.",
                        "skipped" => true,
                        "guest_token" => $guestSessionResult['guest_token'],
                        "is_new_user" => true // New guest is always a new user
                    ];
                    error_log("Authentication skipped and guest session created for IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
                } else {
                    $response = ["success" => false, "message" => $guestSessionResult['message']];
                }
            } catch (Exception $e) {
                error_log("Error during skip authentication: " . $e->getMessage());
                $response = ["success" => false, "message" => "Skip authentication failed."];
            }
            break;
        case '/api/check-skip-status':
            // Check if authentication was skipped
            $skipData = SecureCookieManager::getCookie('skip_auth');

            if ($skipData && isset($skipData['skipped']) && $skipData['skipped'] === true) {
                // Check if skip is still valid (within 30 days)
                $skipTime = $skipData['timestamp'] ?? 0;
                $now = time();
                $thirtyDays = 30 * 24 * 60 * 60;

                if (($now - $skipTime) <= $thirtyDays) {
                    $response = [
                        "success" => true,
                        "skipped" => true,
                        "message" => "Authentication was skipped",
                        "expires_at" => $skipData['expires_at'] ?? null
                    ];
                } else {
                    // Skip expired, clear cookie
                    SecureCookieManager::deleteCookie('skip_auth');
                    $response = [
                        "success" => false,
                        "skipped" => false,
                        "message" => "Skip authentication expired"
                    ];
                }
            } else {
                $response = [
                    "success" => false,
                    "skipped" => false,
                    "message" => "Authentication not skipped"
                ];
            }
            break;
        case '/api/clear-skip':
            // Clear skip authentication
            SecureCookieManager::deleteCookie('skip_auth');
            SecureCookieManager::deleteCookie('guest_session');
            $response = ["success" => true, "message" => "Skip authentication cleared"];
            break;
        case '/api/check-user-status':
            // Check if user is new or returning
            try {
                $isNewUser = true;
                $userType = 'new';
                $redirectTo = 'login.html';

                // Check if user is authenticated
                $sessionResult = validateAndRefreshSession();
                if ($sessionResult['authenticated']) {
                    $isNewUser = false;
                    $userType = 'authenticated';
                    $redirectTo = 'home.html';
                    $response = [
                        "success" => true,
                        "is_new_user" => $isNewUser,
                        "user_type" => $userType,
                        "redirect_to" => $redirectTo,
                        "user" => $sessionResult['user']
                    ];
                } else {
                    // Check if user has skipped authentication
                    $skipData = SecureCookieManager::getCookie('skip_auth');
                    $guestData = SecureCookieManager::getCookie('guest_session');

                    if ($skipData || $guestData) {
                        // User has skipped before, check if setup was completed
                        if ($guestData && isset($guestData['guest_token'])) {
                            $guestSession = getGuestSession($guestData['guest_token']);
                            if ($guestSession) {
                                $isNewUser = !$guestSession['setup_completed'];
                                $userType = 'guest';
                                $redirectTo = $guestSession['setup_completed'] ? 'home.html' : 'login.html';
                            }
                        } else {
                            // Old skip format, treat as returning user
                            $isNewUser = false;
                            $userType = 'guest';
                            $redirectTo = 'home.html';
                        }

                        $response = [
                            "success" => true,
                            "is_new_user" => $isNewUser,
                            "user_type" => $userType,
                            "redirect_to" => $redirectTo
                        ];
                    } else {
                        // Completely new user - send to login.html
                        $response = [
                            "success" => true,
                            "is_new_user" => true,
                            "user_type" => 'new',
                            "redirect_to" => 'login.html'
                        ];
                    }
                }
            } catch (Exception $e) {
                error_log("Error checking user status: " . $e->getMessage());
                $response = [
                    "success" => false,
                    "message" => "Failed to check user status",
                    "is_new_user" => true,
                    "redirect_to" => 'login.html'
                ];
            }
            break;
        case '/api/update-guest-setup':
            // Update guest session when setup is completed
            try {
                $guestData = SecureCookieManager::getCookie('guest_session');

                if ($guestData && isset($guestData['guest_token'])) {
                    $locationGranted = $input['location_granted'] ?? false;
                    $setupCompleted = $input['setup_completed'] ?? true;

                    $updateData = [
                        'location_granted' => $locationGranted,
                        'setup_completed' => $setupCompleted
                    ];

                    $updated = updateGuestSession($guestData['guest_token'], $updateData);

                    if ($updated) {
                        $response = [
                            "success" => true,
                            "message" => "Guest setup updated successfully"
                        ];
                    } else {
                        $response = ["success" => false, "message" => "Failed to update guest setup"];
                    }
                } else {
                    $response = ["success" => false, "message" => "No guest session found"];
                }
            } catch (Exception $e) {
                error_log("Error updating guest setup: " . $e->getMessage());
                $response = ["success" => false, "message" => "Failed to update guest setup"];
            }
            break;
        case '/api/check-session':
            $sessionResult = validateAndRefreshSession();
            $response = $sessionResult;
            break;
        case '/api/logout':
            $sessionResult = validateAndRefreshSession();
            if ($sessionResult['authenticated']) {
                $logoutResult = destroyUserSession($sessionResult['session_id']);
                $response = $logoutResult;
            } else {
                $response = ["success" => false, "message" => "No active session to logout"];
            }
            break;
        case '/api/get-user-info':
            $sessionResult = validateAndRefreshSession();
            if ($sessionResult['authenticated']) {
                $response = [
                    "success" => true,
                    "authenticated" => true,
                    "user" => $sessionResult['user']
                ];
            } else {
                $response = [
                    "success" => false,
                    "authenticated" => false,
                    "message" => $sessionResult['message']
                ];
            }
            break;
        default:
            // For all other endpoints, check if email is required
            if (!empty($email)) {
                switch ($path) {
            case '/api/send-login-link':
                // Check rate limit for login link requests
                $rateLimitCheck = checkRateLimit('login_link');
                if (!$rateLimitCheck['allowed']) {
                    $response = [
                        "success" => false,
                        "message" => "Please wait {$rateLimitCheck['remaining_time']} seconds before requesting another login link.",
                        "remaining_time" => $rateLimitCheck['remaining_time']
                    ];
                    break;
                }

                try {
                    // Check if user exists in public.users table
                    $user = get_user_by_email($email);
                    if (!$user) {
                        $response = ["success" => false, "message" => "No account found with this email address. Please sign up first."];
                        break;
                    }

                    $token = generateAndStoreToken($email, 'login_link');
                    $currentOrigin = $_SERVER['HTTP_ORIGIN'] ?? $allowedOrigins[0];
                    $loginLink = "{$currentOrigin}/login.html?token={$token}&email={$email}";
                    $qrCodeUrl = generateQrCodeUrl($loginLink);
                    $subject = "Your MIKO Login Link";
                    $htmlContent = get_email_template('login_link.html', [
                        '{{login_link}}' => $loginLink,
                        '{{qr_code_url}}' => $qrCodeUrl,
                        '{{year}}' => date("Y")
                    ]);
                    $response = sendEmailViaBrevo($email, $subject, $htmlContent);

                    // Set rate limit cookie and update sent_emails status if email was sent successfully
                    if ($response['success']) {
                        setRateLimit('login_link');
                        updateSentEmailsStatus($email, 'login_link', 'sent', null, $token);
                    } else {
                        updateSentEmailsStatus($email, 'login_link', 'failed', null, $token);
                    }
                } catch (Exception $e) {
                    error_log("Error sending login link: " . $e->getMessage());
                    $response = ["success" => false, "message" => "Failed to send login link: " . $e->getMessage()];
                }
                break;
            case '/api/send-password-reset':
                // Check rate limit for password reset requests
                $rateLimitCheck = checkRateLimit('password_reset');
                if (!$rateLimitCheck['allowed']) {
                    $response = [
                        "success" => false,
                        "message" => "Please wait {$rateLimitCheck['remaining_time']} seconds before requesting another password reset.",
                        "remaining_time" => $rateLimitCheck['remaining_time']
                    ];
                    break;
                }

                try {
                    // Check if user exists in public.users table
                    $user = get_user_by_email($email);
                    if (!$user) {
                        $response = ["success" => false, "message" => "No user found with that email address."];
                        break;
                    }

                    $token = generateAndStoreToken($email, 'password_reset');
                    $currentOrigin = $_SERVER['HTTP_ORIGIN'] ?? $allowedOrigins[0];
                    $resetLink = "{$currentOrigin}/reset-password-form.html?token={$token}&email={$email}";
                    $qrCodeUrl = generateQrCodeUrl($resetLink);
                    $subject = "MIKO Password Reset Request";
                    $htmlContent = get_email_template('password_reset.html', [
                        '{{reset_link}}' => $resetLink,
                        '{{qr_code_url}}' => $qrCodeUrl,
                        '{{year}}' => date("Y")
                    ]);
                    $response = sendEmailViaBrevo($email, $subject, $htmlContent);

                    // Set rate limit cookie and update sent_emails status if email was sent successfully
                    if ($response['success']) {
                        setRateLimit('password_reset');
                        updateSentEmailsStatus($email, 'password_reset', 'sent', null, $token);
                    } else {
                        updateSentEmailsStatus($email, 'password_reset', 'failed', null, $token);
                    }
                } catch (Exception $e) {
                    error_log("Error sending password reset link: " . $e->getMessage());
                    $response = ["success" => false, "message" => "Failed to send password reset link: " . $e->getMessage()];
                }
                break;
            case '/api/send-verification':
                // Check rate limit for OTP requests
                $rateLimitCheck = checkRateLimit('otp');
                if (!$rateLimitCheck['allowed']) {
                    $response = [
                        "success" => false,
                        "message" => "Please wait {$rateLimitCheck['remaining_time']} seconds before requesting another OTP.",
                        "remaining_time" => $rateLimitCheck['remaining_time']
                    ];
                    break;
                }

                try {
                    // For OTP verification, we don't need to create the user yet
                    // The user will be created later during the signup process after OTP verification
                    // We only need to generate and store the OTP for email verification

                    $otp = generateAndStoreOtp($email);
                    $qrCodeUrl = generateQrCodeUrl("MIKO OTP: {$otp}");
                    $subject = "Verify Your MIKO Account Email - Your OTP";
                    $htmlContent = get_email_template('otp_verification.html', [
                        '{{otp}}' => $otp,
                        '{{qr_code_url}}' => $qrCodeUrl,
                        '{{year}}' => date("Y")
                    ]);
                    $response = sendEmailViaBrevo($email, $subject, $htmlContent);

                    // Set rate limit cookie and update sent_emails status if email was sent successfully
                    if ($response['success']) {
                        setRateLimit('otp');
                        updateSentEmailsStatus($email, 'otp_verification', 'sent', $otp, null);
                    } else {
                        updateSentEmailsStatus($email, 'otp_verification', 'failed', $otp, null);
                    }
                } catch (Exception $e) {
                    error_log("Error sending verification OTP: " . $e->getMessage());
                    $response = ["success" => false, "message" => "Failed to send verification OTP: " . $e->getMessage()];
                }
                break;
            case '/api/verify-token':
                $token = $input['token'] ?? '';
                $type = $input['type'] ?? '';
                if (empty($token) || empty($type)) {
                    $response = ["success" => false, "message" => "Token and type are required for verification."];
                } else {
                    try {
                        $validationResult = validateToken($email, $token, $type);
                        $response = $validationResult;
                    } catch (Exception $e) {
                        error_log("Error validating token: " . $e->getMessage());
                        $response = ["success" => false, "message" => "Failed to validate token: " . $e->getMessage()];
                    }
                }
                break;
            case '/api/verify-otp':
                $otp = $input['otp'] ?? '';
                if (empty($otp)) {
                    $response = ["success" => false, "message" => "OTP is required for verification."];
                } else {
                    try {
                        if (validateOtp($email, $otp)) {
                            // OTP validation successful - email is verified
                            // We don't need to check for user existence here since the user will be created during signup
                            // Just mark the email as verified for this session
                            $_SESSION['verified_email'] = $email;
                            $response = ["success" => true, "message" => "OTP validated successfully. Email verified."];
                        } else {
                            $response = ["success" => false, "message" => "Invalid or expired OTP."];
                        }
                    } catch (Exception $e) {
                        error_log("Error validating OTP: " . $e->getMessage());
                        $response = ["success" => false, "message" => "Failed to validate OTP: " . $e->getMessage()];
                    }
                }
                break;
            case '/api/reset-password':
                $token = $input['token'] ?? '';
                $newPassword = $input['new_password'] ?? '';
                if (empty($email) || empty($token) || empty($newPassword)) {
                    $response = ["success" => false, "message" => "Email, token, and new password are required."];
                    break;
                }
                if (strlen($newPassword) < 6) {
                    $response = ["success" => false, "message" => "New password must be at least 6 characters long."];
                    break;
                }
                try {
                    $validationResult = validateToken($email, $token, 'password_reset');
                    if ($validationResult['success']) {
                        // Update password in public.users table
                        if (update_user_password_in_supabase($email, $newPassword)) {
                            $response = ["success" => true, "message" => "Password reset successfully."];
                            error_log("Password for {$email} reset successfully in Supabase public.users.");
                        } else {
                            $response = ["success" => false, "message" => "Failed to update password in Supabase public.users."];
                        }
                    } else {
                        $response = ["success" => false, "message" => $validationResult['message'] ?? "Invalid or expired password reset token."];
                    }
                } catch (Exception $e) {
                    error_log("Error during password reset: " . $e->getMessage());
                    $response = ["success" => false, "message" => "An error occurred during password reset: " . $e->getMessage()];
                }
                break;
            case '/api/signup':
                $password = $input['password'] ?? '';
                if (empty($password)) {
                    $response = ["success" => false, "message" => "Password is required for signup."];
                } else {
                    try {
                        // Check if email was verified (should be in session after OTP verification)
                        if (!isset($_SESSION['verified_email']) || $_SESSION['verified_email'] !== $email) {
                            $response = ["success" => false, "message" => "Email verification required. Please verify your email first."];
                            break;
                        }

                        // Create user in public.users table
                        $createResult = create_user($email, $password);
                        if ($createResult === "User already exists") {
                            $response = ["success" => false, "message" => "An account with this email already exists."];
                        } elseif (!$createResult) {
                            $response = ["success" => false, "message" => "Failed to create user account."];
                        } else {
                            // User created successfully
                            $user = get_user_by_email($email);
                            if ($user) {
                                // Create secure session using cookie manager and Supabase
                                $sessionResult = createUserSession($user);
                                if ($sessionResult['success']) {
                                    // Clear the verified email session since user is now created
                                    unset($_SESSION['verified_email']);
                                    $response = ["success" => true, "message" => "Account created successfully.", "userId" => $user['id'], "session_id" => $sessionResult['session_id']];
                                    error_log("User {$email} successfully signed up and logged in with secure session.");
                                } else {
                                    $response = ["success" => false, "message" => "Account created but session creation failed."];
                                }
                            } else {
                                $response = ["success" => false, "message" => "Account created but login failed."];
                            }
                        }
                    } catch (Exception $e) {
                        error_log("Error during signup: " . $e->getMessage());
                        $response = ["success" => false, "message" => "An error occurred during signup: " . $e->getMessage()];
                    }
                }
                break;
            case '/api/check-user-existence':
                try {
                    $user = get_user_by_email($email);
                    $response = ["success" => true, "userExists" => $user !== null];
                } catch (Exception $e) {
                    error_log("Error checking user existence: " . $e->getMessage());
                    $response = ["success" => false, "message" => "Failed to check user existence: " . $e->getMessage()];
                }
                break;
            case '/api/test-email':
                try {
                    $subject = "MIKO Test Email - " . date('Y-m-d H:i:s');
                    $htmlContent = "
                    <h2>Test Email from MIKO</h2>
                    <p>This is a test email sent at " . date('Y-m-d H:i:s') . "</p>
                    <p>If you receive this, email sending is working correctly.</p>
                    <p>Email sent to: $email</p>
                    ";
                    $response = sendEmailViaBrevo($email, $subject, $htmlContent);
                    error_log("Test email result for $email: " . json_encode($response));
                } catch (Exception $e) {
                    error_log("Error sending test email: " . $e->getMessage());
                    $response = ["success" => false, "message" => "Failed to send test email: " . $e->getMessage()];
                }
                break;
            case '/api/check-rate-limit':
                // Test endpoint to check rate limiting status
                $type = $input['type'] ?? 'otp'; // Default to OTP
                $rateLimitCheck = checkRateLimit($type);
                $response = [
                    "success" => true,
                    "type" => $type,
                    "allowed" => $rateLimitCheck['allowed'],
                    "remaining_time" => $rateLimitCheck['remaining_time'],
                    "message" => $rateLimitCheck['allowed'] ? "Request allowed" : "Rate limited for {$rateLimitCheck['remaining_time']} seconds"
                ];
                break;
            case '/api/check-session':
                $sessionResult = validateAndRefreshSession();
                $response = $sessionResult;
                break;
            case '/api/logout':
                destroyUserSession();
                $response = ["success" => true, "message" => "Logged out successfully"];
                break;
            case '/api/get-user-info':
                $sessionResult = validateAndRefreshSession();
                if ($sessionResult['authenticated']) {
                    $response = [
                        "success" => true,
                        "authenticated" => true,
                        "user" => $sessionResult['user']
                    ];
                } else {
                    $response = [
                        "success" => false,
                        "authenticated" => false,
                        "message" => $sessionResult['message']
                    ];
                }
                break;

            case '/api/login-with-password':
                // Password-based login
                if (empty($email)) {
                    $response = ["success" => false, "message" => "Email is required."];
                    break;
                }

                $password = $input['password'] ?? '';
                if (empty($password)) {
                    $response = ["success" => false, "message" => "Password is required."];
                    break;
                }

                try {
                    // Verify user credentials
                    $user = verifyUserPassword($email, $password);

                    if ($user) {
                        // Create secure session
                        $sessionResult = createUserSession($user);

                        if ($sessionResult['success']) {
                            $response = [
                                "success" => true,
                                "loggedIn" => true,
                                "message" => "Login successful.",
                                "userId" => $user['id'],
                                "session_id" => $sessionResult['session_id'],
                                "user" => [
                                    'id' => $user['id'],
                                    'email' => $user['email'],
                                    'name' => $user['name']
                                ]
                            ];
                            error_log("User {$email} successfully logged in with password.");
                        } else {
                            $response = ["success" => false, "loggedIn" => false, "message" => "Login successful but session creation failed."];
                        }
                    } else {
                        $response = ["success" => false, "loggedIn" => false, "message" => "Invalid email or password."];
                    }
                } catch (Exception $e) {
                    error_log("Error during password login: " . $e->getMessage());
                    $response = ["success" => false, "loggedIn" => false, "message" => "Login failed due to server error."];
                }
                break;
                default:
                    $response = ["success" => false, "message" => "Unknown API endpoint."];
                    break;
                }
            } else {
                // Email is required but not provided
                $response = ["success" => false, "message" => "Email is required for this endpoint."];
            }
            break;
    }
} catch (Exception $e) {
    error_log("Unhandled exception in server.php: " . $e->getMessage());
    $response = ["success" => false, "message" => "An unexpected server error occurred: " . $e->getMessage()];
}
ob_end_clean();
echo json_encode($response);
ob_end_flush();
exit();
