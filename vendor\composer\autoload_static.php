<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit51535c02de4759f571e4091a5b35e356
{
    public static $files = array (
        '7b11c4dc42b3b3023073cb14e519683c' => __DIR__ . '/..' . '/ralouphie/getallheaders/src/getallheaders.php',
        '6e3fae29631ef280660b3cdad06f25a8' => __DIR__ . '/..' . '/symfony/deprecation-contracts/function.php',
        '37a3dc5111fe8f707ab4c132ef1dbc62' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/functions_include.php',
        '320cde22f66dd4f5d3fd621d3e88b98f' => __DIR__ . '/..' . '/symfony/polyfill-ctype/bootstrap.php',
        '0e6d7bf4a5811bfa5cf40c5ccd6fae6a' => __DIR__ . '/..' . '/symfony/polyfill-mbstring/bootstrap.php',
        'a4a119a56e50fbb293281d9a48007e0e' => __DIR__ . '/..' . '/symfony/polyfill-php80/bootstrap.php',
    );

    public static $prefixLengthsPsr4 = array (
        'S' => 
        array (
            'Symfony\\Polyfill\\Php80\\' => 23,
            'Symfony\\Polyfill\\Mbstring\\' => 26,
            'Symfony\\Polyfill\\Ctype\\' => 23,
        ),
        'P' => 
        array (
            'Psr\\Log\\' => 8,
            'Psr\\Http\\Message\\' => 17,
            'Psr\\Http\\Client\\' => 16,
            'PhpOption\\' => 10,
            'PHPMailer\\PHPMailer\\' => 20,
        ),
        'M' => 
        array (
            'Monolog\\' => 8,
        ),
        'G' => 
        array (
            'GuzzleHttp\\Psr7\\' => 16,
            'GuzzleHttp\\Promise\\' => 19,
            'GuzzleHttp\\' => 11,
            'GrahamCampbell\\ResultType\\' => 26,
        ),
        'D' => 
        array (
            'Dotenv\\' => 7,
        ),
        'B' => 
        array (
            'Brevo\\Client\\' => 13,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Symfony\\Polyfill\\Php80\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-php80',
        ),
        'Symfony\\Polyfill\\Mbstring\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-mbstring',
        ),
        'Symfony\\Polyfill\\Ctype\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-ctype',
        ),
        'Psr\\Log\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/log/src',
        ),
        'Psr\\Http\\Message\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/http-factory/src',
            1 => __DIR__ . '/..' . '/psr/http-message/src',
        ),
        'Psr\\Http\\Client\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/http-client/src',
        ),
        'PhpOption\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpoption/phpoption/src/PhpOption',
        ),
        'PHPMailer\\PHPMailer\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpmailer/phpmailer/src',
        ),
        'Monolog\\' => 
        array (
            0 => __DIR__ . '/..' . '/monolog/monolog/src/Monolog',
        ),
        'GuzzleHttp\\Psr7\\' => 
        array (
            0 => __DIR__ . '/..' . '/guzzlehttp/psr7/src',
        ),
        'GuzzleHttp\\Promise\\' => 
        array (
            0 => __DIR__ . '/..' . '/guzzlehttp/promises/src',
        ),
        'GuzzleHttp\\' => 
        array (
            0 => __DIR__ . '/..' . '/guzzlehttp/guzzle/src',
        ),
        'GrahamCampbell\\ResultType\\' => 
        array (
            0 => __DIR__ . '/..' . '/graham-campbell/result-type/src',
        ),
        'Dotenv\\' => 
        array (
            0 => __DIR__ . '/..' . '/vlucas/phpdotenv/src',
        ),
        'Brevo\\Client\\' => 
        array (
            0 => __DIR__ . '/..' . '/getbrevo/brevo-php/lib',
        ),
    );

    public static $classMap = array (
        'Attribute' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/Attribute.php',
        'Cloudinary\\Api\\Admin\\AdminApi' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Admin/AdminApi.php',
        'Cloudinary\\Api\\Admin\\AnalysisTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Admin/AnalysisTrait.php',
        'Cloudinary\\Api\\Admin\\ApiEndPoint' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Admin/ApiEndPoint.php',
        'Cloudinary\\Api\\Admin\\AssetsTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Admin/AssetsTrait.php',
        'Cloudinary\\Api\\Admin\\FoldersTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Admin/FoldersTrait.php',
        'Cloudinary\\Api\\Admin\\MetadataFieldsTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Admin/MetadataFieldsTrait.php',
        'Cloudinary\\Api\\Admin\\MiscTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Admin/MiscTrait.php',
        'Cloudinary\\Api\\Admin\\StreamingProfilesTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Admin/StreamingProfilesTrait.php',
        'Cloudinary\\Api\\Admin\\TransformationsTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Admin/TransformationsTrait.php',
        'Cloudinary\\Api\\Admin\\UploadMappingsTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Admin/UploadMappingsTrait.php',
        'Cloudinary\\Api\\Admin\\UploadPresetsTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Admin/UploadPresetsTrait.php',
        'Cloudinary\\Api\\ApiClient' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/ApiClient.php',
        'Cloudinary\\Api\\ApiResponse' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/ApiResponse.php',
        'Cloudinary\\Api\\ApiUtils' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Utils/ApiUtils.php',
        'Cloudinary\\Api\\BaseApiClient' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/BaseApiClient.php',
        'Cloudinary\\Api\\Exception\\AlreadyExists' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Exception/AlreadyExists.php',
        'Cloudinary\\Api\\Exception\\ApiError' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Exception/ApiError.php',
        'Cloudinary\\Api\\Exception\\AuthorizationRequired' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Exception/AuthorizationRequired.php',
        'Cloudinary\\Api\\Exception\\BadRequest' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Exception/BadRequest.php',
        'Cloudinary\\Api\\Exception\\GeneralError' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Exception/GeneralError.php',
        'Cloudinary\\Api\\Exception\\NotAllowed' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Exception/NotAllowed.php',
        'Cloudinary\\Api\\Exception\\NotFound' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Exception/NotFound.php',
        'Cloudinary\\Api\\Exception\\RateLimited' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Exception/RateLimited.php',
        'Cloudinary\\Api\\HttpMethod' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Utils/HttpMethod.php',
        'Cloudinary\\Api\\HttpStatusCode' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Utils/HttpStatusCode.php',
        'Cloudinary\\Api\\Metadata\\DateMetadataField' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Metadata/DateMetadataField.php',
        'Cloudinary\\Api\\Metadata\\EnumMetadataField' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Metadata/EnumMetadataField.php',
        'Cloudinary\\Api\\Metadata\\IntMetadataField' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Metadata/IntMetadataField.php',
        'Cloudinary\\Api\\Metadata\\Metadata' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Metadata/Metadata.php',
        'Cloudinary\\Api\\Metadata\\MetadataDataEntry' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Metadata/MetadataDataEntry.php',
        'Cloudinary\\Api\\Metadata\\MetadataDataSource' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Metadata/MetadataDataSource.php',
        'Cloudinary\\Api\\Metadata\\MetadataField' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Metadata/MetadataField.php',
        'Cloudinary\\Api\\Metadata\\MetadataFieldList' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Metadata/MetadataFieldList.php',
        'Cloudinary\\Api\\Metadata\\MetadataFieldType' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Metadata/MetadataFieldType.php',
        'Cloudinary\\Api\\Metadata\\SetMetadataField' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Metadata/SetMetadataField.php',
        'Cloudinary\\Api\\Metadata\\StringMetadataField' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Metadata/StringMetadataField.php',
        'Cloudinary\\Api\\Metadata\\Validators\\AndValidator' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Metadata/Validators/AndValidator.php',
        'Cloudinary\\Api\\Metadata\\Validators\\ComparisonRule' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Metadata/Validators/ComparisonRule.php',
        'Cloudinary\\Api\\Metadata\\Validators\\DateGreaterThan' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Metadata/Validators/DateGreaterThan.php',
        'Cloudinary\\Api\\Metadata\\Validators\\DateLessThan' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Metadata/Validators/DateLessThan.php',
        'Cloudinary\\Api\\Metadata\\Validators\\IntGreaterThan' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Metadata/Validators/IntGreaterThan.php',
        'Cloudinary\\Api\\Metadata\\Validators\\IntLessThan' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Metadata/Validators/IntLessThan.php',
        'Cloudinary\\Api\\Metadata\\Validators\\MetadataValidation' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Metadata/Validators/MetadataValidation.php',
        'Cloudinary\\Api\\Metadata\\Validators\\StringLength' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Metadata/Validators/StringLength.php',
        'Cloudinary\\Api\\Provisioning\\AccountApi' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Provisioning/AccountApi.php',
        'Cloudinary\\Api\\Provisioning\\AccountApiClient' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Provisioning/AccountApiClient.php',
        'Cloudinary\\Api\\Provisioning\\AccountEndPoint' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Provisioning/AccountEndPoint.php',
        'Cloudinary\\Api\\Provisioning\\UserRole' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Provisioning/UserRole.php',
        'Cloudinary\\Api\\Search\\SearchApi' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Search/SearchApi.php',
        'Cloudinary\\Api\\Search\\SearchFoldersApi' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Search/SearchFoldersApi.php',
        'Cloudinary\\Api\\Search\\SearchQueryInterface' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Search/SearchQueryInterface.php',
        'Cloudinary\\Api\\Search\\SearchQueryTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Search/SearchQueryTrait.php',
        'Cloudinary\\Api\\UploadApiClient' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/UploadApiClient.php',
        'Cloudinary\\Api\\Upload\\ArchiveTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Upload/ArchiveTrait.php',
        'Cloudinary\\Api\\Upload\\ContextCommand' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Upload/ContextCommand.php',
        'Cloudinary\\Api\\Upload\\ContextTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Upload/ContextTrait.php',
        'Cloudinary\\Api\\Upload\\CreativeTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Upload/CreativeTrait.php',
        'Cloudinary\\Api\\Upload\\EditTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Upload/EditTrait.php',
        'Cloudinary\\Api\\Upload\\TagCommand' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Upload/TagCommand.php',
        'Cloudinary\\Api\\Upload\\TagTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Upload/TagTrait.php',
        'Cloudinary\\Api\\Upload\\UploadApi' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Upload/UploadApi.php',
        'Cloudinary\\Api\\Upload\\UploadEndPoint' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Upload/UploadEndPoint.php',
        'Cloudinary\\Api\\Upload\\UploadTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Api/Upload/UploadTrait.php',
        'Cloudinary\\ArrayUtils' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Utils/ArrayUtils.php',
        'Cloudinary\\Asset\\AccessControl\\AccessControlRule' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Asset/AccessControl/AccessControlRule.php',
        'Cloudinary\\Asset\\AccessControl\\AccessType' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Asset/AccessControl/AccessType.php',
        'Cloudinary\\Asset\\Analytics' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Asset/Analytics/Analytics.php',
        'Cloudinary\\Asset\\AssetDescriptor' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Asset/Descriptor/AssetDescriptor.php',
        'Cloudinary\\Asset\\AssetDescriptorTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Asset/Descriptor/AssetDescriptorTrait.php',
        'Cloudinary\\Asset\\AssetFinalizerTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Asset/AssetFinalizerTrait.php',
        'Cloudinary\\Asset\\AssetInterface' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Asset/AssetInterface.php',
        'Cloudinary\\Asset\\AssetQualifiers' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Asset/AssetQualifiers.php',
        'Cloudinary\\Asset\\AssetTransformation' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Asset/Descriptor/AssetTransformation.php',
        'Cloudinary\\Asset\\AssetType' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Asset/Descriptor/AssetType.php',
        'Cloudinary\\Asset\\AuthToken' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Asset/AuthToken.php',
        'Cloudinary\\Asset\\BaseAsset' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Asset/BaseAsset.php',
        'Cloudinary\\Asset\\BaseMediaAsset' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Asset/BaseMediaAsset.php',
        'Cloudinary\\Asset\\DeliveryType' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Asset/Descriptor/DeliveryType.php',
        'Cloudinary\\Asset\\DeliveryTypeTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Asset/DeliveryTypeTrait.php',
        'Cloudinary\\Asset\\File' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Asset/File.php',
        'Cloudinary\\Asset\\Image' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Asset/Image.php',
        'Cloudinary\\Asset\\Media' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Asset/Media.php',
        'Cloudinary\\Asset\\MediaAssetFinalizerTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Asset/MediaAssetFinalizerTrait.php',
        'Cloudinary\\Asset\\ModerationStatus' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Asset/Moderation/ModerationStatus.php',
        'Cloudinary\\Asset\\ModerationType' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Asset/Moderation/ModerationType.php',
        'Cloudinary\\Asset\\SearchAsset' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Asset/SearchAsset.php',
        'Cloudinary\\Asset\\SearchAssetTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Asset/SearchAssetTrait.php',
        'Cloudinary\\Asset\\Video' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Asset/Video.php',
        'Cloudinary\\ClassUtils' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Utils/ClassUtils.php',
        'Cloudinary\\Cloudinary' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Cloudinary.php',
        'Cloudinary\\Configuration\\ApiConfig' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Configuration/ApiConfig.php',
        'Cloudinary\\Configuration\\AssetConfigTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Configuration/AssetConfigTrait.php',
        'Cloudinary\\Configuration\\AuthTokenConfig' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Configuration/AuthTokenConfig.php',
        'Cloudinary\\Configuration\\BaseConfigSection' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Configuration/BaseConfigSection.php',
        'Cloudinary\\Configuration\\CloudConfig' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Configuration/CloudConfig.php',
        'Cloudinary\\Configuration\\CloudConfigTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Configuration/CloudConfigTrait.php',
        'Cloudinary\\Configuration\\ConfigUtils' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Configuration/ConfigUtils.php',
        'Cloudinary\\Configuration\\ConfigurableInterface' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Configuration/ConfigurableInterface.php',
        'Cloudinary\\Configuration\\Configuration' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Configuration/Configuration.php',
        'Cloudinary\\Configuration\\LoggingConfig' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Configuration/LoggingConfig.php',
        'Cloudinary\\Configuration\\Provisioning\\ProvisioningAccountConfig' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Configuration/Provisioning/ProvisioningAccountConfig.php',
        'Cloudinary\\Configuration\\Provisioning\\ProvisioningConfigUtils' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Configuration/Provisioning/ProvisioningConfigUtils.php',
        'Cloudinary\\Configuration\\Provisioning\\ProvisioningConfiguration' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Configuration/Provisioning/ProvisioningConfiguration.php',
        'Cloudinary\\Configuration\\ResponsiveBreakpointsConfig' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Configuration/ResponsiveBreakpointsConfig.php',
        'Cloudinary\\Configuration\\TagConfig' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Configuration/TagConfig.php',
        'Cloudinary\\Configuration\\TagConfigTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Configuration/TagConfigTrait.php',
        'Cloudinary\\Configuration\\UrlConfig' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Configuration/UrlConfig.php',
        'Cloudinary\\Configuration\\UrlConfigTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Configuration/UrlConfigTrait.php',
        'Cloudinary\\Exception\\ConfigurationException' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Exception/ConfigurationException.php',
        'Cloudinary\\Exception\\Error' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Exception/Error.php',
        'Cloudinary\\FileUtils' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Utils/FileUtils.php',
        'Cloudinary\\HttpClient' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/HttpClient/HttpClient.php',
        'Cloudinary\\JsonUtils' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Utils/JsonUtils.php',
        'Cloudinary\\Log\\Logger' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Log/Logger.php',
        'Cloudinary\\Log\\LoggerDecorator' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Log/LoggerDecorator.php',
        'Cloudinary\\Log\\LoggerDecoratorTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Log/LoggerDecoratorTrait.php',
        'Cloudinary\\Log\\LoggerTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Log/LoggerTrait.php',
        'Cloudinary\\Log\\LoggersList' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Log/LoggersList.php',
        'Cloudinary\\StringUtils' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Utils/StringUtils.php',
        'Cloudinary\\Tag\\AudioSourceType' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/Attribute/AudioSourceType.php',
        'Cloudinary\\Tag\\BaseConfigurableApiTag' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/BaseConfigurableApiTag.php',
        'Cloudinary\\Tag\\BaseImageTag' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/BaseImageTag.php',
        'Cloudinary\\Tag\\BaseTag' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/BaseTag.php',
        'Cloudinary\\Tag\\ClientHintsMetaTag' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/ClientHintsMetaTag.php',
        'Cloudinary\\Tag\\FormInputTag' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/FormInputTag.php',
        'Cloudinary\\Tag\\FormTag' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/FormTag.php',
        'Cloudinary\\Tag\\ImageTag' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/ImageTag.php',
        'Cloudinary\\Tag\\ImageTagDeliveryTypeTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/ImageTagDeliveryTypeTrait.php',
        'Cloudinary\\Tag\\JsConfigTag' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/JsConfigTag.php',
        'Cloudinary\\Tag\\Media' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/Attribute/Media.php',
        'Cloudinary\\Tag\\PictureSourceTag' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/PictureSourceTag.php',
        'Cloudinary\\Tag\\PictureTag' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/PictureTag.php',
        'Cloudinary\\Tag\\Sizes' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/Attribute/Sizes.php',
        'Cloudinary\\Tag\\SourceType' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/Attribute/SourceType.php',
        'Cloudinary\\Tag\\SpriteTag' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/SpriteTag.php',
        'Cloudinary\\Tag\\SrcSet' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/Attribute/SrcSet.php',
        'Cloudinary\\Tag\\Tag' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/Tag.php',
        'Cloudinary\\Tag\\TagBuilder' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/TagBuilder.php',
        'Cloudinary\\Tag\\TagUtils' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/TagUtils.php',
        'Cloudinary\\Tag\\UploadTag' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/UploadTag.php',
        'Cloudinary\\Tag\\VideoSourceTag' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/VideoSourceTag.php',
        'Cloudinary\\Tag\\VideoSourceType' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/Attribute/VideoSourceType.php',
        'Cloudinary\\Tag\\VideoTag' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/VideoTag.php',
        'Cloudinary\\Tag\\VideoTagDeliveryTypeTrait' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/VideoTagDeliveryTypeTrait.php',
        'Cloudinary\\Tag\\VideoThumbnailTag' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Tag/VideoThumbnailTag.php',
        'Cloudinary\\TransformationUtils' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Utils/TransformationUtils.php',
        'Cloudinary\\Transformation\\AbsolutePosition' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/AbsolutePosition.php',
        'Cloudinary\\Transformation\\AbsolutePositionTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/AbsolutePositionTrait.php',
        'Cloudinary\\Transformation\\Accelerate' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Playback/Accelerate.php',
        'Cloudinary\\Transformation\\AccessoryObjectGravityBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/AccessoryObjectGravityBuilderTrait.php',
        'Cloudinary\\Transformation\\AccessoryObjectGravityInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/AccessoryObjectGravityInterface.php',
        'Cloudinary\\Transformation\\Action' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Action.php',
        'Cloudinary\\Transformation\\AddonEffectTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Addon/AddonEffectTrait.php',
        'Cloudinary\\Transformation\\Adjust' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Adjustment/Adjust.php',
        'Cloudinary\\Transformation\\AdjustmentInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Adjustment/AdjustmentInterface.php',
        'Cloudinary\\Transformation\\Angle' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Rotation/Angle.php',
        'Cloudinary\\Transformation\\AngleQualifierTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Rotation/AngleQualifierTrait.php',
        'Cloudinary\\Transformation\\AnimalObjectGravityBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/AnimalObjectGravityBuilderTrait.php',
        'Cloudinary\\Transformation\\AnimalObjectGravityInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/AnimalObjectGravityInterface.php',
        'Cloudinary\\Transformation\\Animated' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Animated/Animated.php',
        'Cloudinary\\Transformation\\AnimatedEdit' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Animated/AnimatedEdit.php',
        'Cloudinary\\Transformation\\AnimatedEditTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Animated/AnimatedEditTrait.php',
        'Cloudinary\\Transformation\\AnimatedFormat' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/AnimatedFormat.php',
        'Cloudinary\\Transformation\\AnimatedFormatTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/AnimatedFormatTrait.php',
        'Cloudinary\\Transformation\\AppearanceEffect' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Appearance/AppearanceEffect.php',
        'Cloudinary\\Transformation\\ApplianceObjectGravityBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/ApplianceObjectGravityBuilderTrait.php',
        'Cloudinary\\Transformation\\ApplianceObjectGravityInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/ApplianceObjectGravityInterface.php',
        'Cloudinary\\Transformation\\Argument\\AngleTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Rotation/AngleTrait.php',
        'Cloudinary\\Transformation\\Argument\\BaseNamedArgument' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/BaseNamedArgument.php',
        'Cloudinary\\Transformation\\Argument\\Color' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Color/Color.php',
        'Cloudinary\\Transformation\\Argument\\ColorTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Color/ColorTrait.php',
        'Cloudinary\\Transformation\\Argument\\ColorValue' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Color/ColorValue.php',
        'Cloudinary\\Transformation\\Argument\\Degree' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Rotation/Degree.php',
        'Cloudinary\\Transformation\\Argument\\GenericArgument' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/GenericArgument.php',
        'Cloudinary\\Transformation\\Argument\\GenericNamedArgument' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/GenericNamedArgument.php',
        'Cloudinary\\Transformation\\Argument\\Gradient' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Color/Gradient.php',
        'Cloudinary\\Transformation\\Argument\\GradientDirection' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Color/GradientDirection.php',
        'Cloudinary\\Transformation\\Argument\\IndexedArgument' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Misc/IndexedArgument.php',
        'Cloudinary\\Transformation\\Argument\\LimitedGenericNamedArgument' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/LimitedGenericNamedArgument.php',
        'Cloudinary\\Transformation\\Argument\\PointValue' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Canvas/PointValue.php',
        'Cloudinary\\Transformation\\Argument\\RangeArgument' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Misc/RangeArgument.php',
        'Cloudinary\\Transformation\\Argument\\Range\\PreviewDuration' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Argument/Range/PreviewDuration.php',
        'Cloudinary\\Transformation\\Argument\\Range\\Range' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Argument/Range/Range.php',
        'Cloudinary\\Transformation\\Argument\\RotationMode' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Rotation/RotationMode.php',
        'Cloudinary\\Transformation\\Argument\\RotationModeTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Rotation/RotationModeTrait.php',
        'Cloudinary\\Transformation\\Argument\\Text\\FontAntialias' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Text/FontAntialias.php',
        'Cloudinary\\Transformation\\Argument\\Text\\FontFamily' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Text/FontFamily.php',
        'Cloudinary\\Transformation\\Argument\\Text\\FontHinting' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Text/FontHinting.php',
        'Cloudinary\\Transformation\\Argument\\Text\\FontStyle' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Text/FontStyle.php',
        'Cloudinary\\Transformation\\Argument\\Text\\FontWeight' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Text/FontWeight.php',
        'Cloudinary\\Transformation\\Argument\\Text\\Stroke' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Text/Stroke.php',
        'Cloudinary\\Transformation\\Argument\\Text\\Text' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Text/Text.php',
        'Cloudinary\\Transformation\\Argument\\Text\\TextAlignment' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Text/TextAlignment.php',
        'Cloudinary\\Transformation\\Argument\\Text\\TextDecoration' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Text/TextDecoration.php',
        'Cloudinary\\Transformation\\Argument\\Text\\TextStyleTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Text/TextStyleTrait.php',
        'Cloudinary\\Transformation\\Argument\\Text\\TextTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Text/TextTrait.php',
        'Cloudinary\\Transformation\\ArtisticFilter' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/ArtisticFilter.php',
        'Cloudinary\\Transformation\\AspectRatio' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Dimensions/AspectRatio.php',
        'Cloudinary\\Transformation\\AssetBasedSource' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/AssetBasedSource.php',
        'Cloudinary\\Transformation\\AssistColorBlind' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Color/AssistColorBlind.php',
        'Cloudinary\\Transformation\\AudioCodec' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/Audio/AudioCodec.php',
        'Cloudinary\\Transformation\\AudioFormatInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/AudioFormatInterface.php',
        'Cloudinary\\Transformation\\AudioFormatTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/AudioFormatTrait.php',
        'Cloudinary\\Transformation\\AudioFrequency' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/Audio/AudioFrequency.php',
        'Cloudinary\\Transformation\\AudioOverlay' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/AudioOverlay.php',
        'Cloudinary\\Transformation\\AudioQualifierTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/Audio/AudioQualifierTrait.php',
        'Cloudinary\\Transformation\\AudioSource' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/AudioSource.php',
        'Cloudinary\\Transformation\\AudioSourceQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/AudioSourceQualifier.php',
        'Cloudinary\\Transformation\\AudioSourceTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/AudioSourceTrait.php',
        'Cloudinary\\Transformation\\AutoBackground' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Background/AutoBackground.php',
        'Cloudinary\\Transformation\\AutoBackgroundMode' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Background/AutoBackgroundMode.php',
        'Cloudinary\\Transformation\\AutoBackgroundTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Background/AutoBackgroundTrait.php',
        'Cloudinary\\Transformation\\AutoFocus' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/AutoFocus.php',
        'Cloudinary\\Transformation\\AutoGradientBackground' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Background/AutoGradientBackground.php',
        'Cloudinary\\Transformation\\AutoGradientBackgroundTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Background/AutoGradientBackgroundTrait.php',
        'Cloudinary\\Transformation\\AutoGravity' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/AutoGravity.php',
        'Cloudinary\\Transformation\\AutoGravityBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/AutoGravityBuilderTrait.php',
        'Cloudinary\\Transformation\\AutoGravityObject' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/AutoGravityObject.php',
        'Cloudinary\\Transformation\\Background' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Background/Background.php',
        'Cloudinary\\Transformation\\BackgroundColorTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Background/BackgroundColorTrait.php',
        'Cloudinary\\Transformation\\BackgroundQualifierTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Background/BackgroundQualifierTrait.php',
        'Cloudinary\\Transformation\\BackgroundRemoval' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/BackgroundRemoval.php',
        'Cloudinary\\Transformation\\BackgroundTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Background/BackgroundTrait.php',
        'Cloudinary\\Transformation\\BaseAction' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/BaseAction.php',
        'Cloudinary\\Transformation\\BaseArgument' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/BaseArgument.php',
        'Cloudinary\\Transformation\\BaseComponent' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/BaseComponent.php',
        'Cloudinary\\Transformation\\BaseOffsetQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Timeline/BaseOffsetQualifier.php',
        'Cloudinary\\Transformation\\BasePageAction' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/BasePageAction.php',
        'Cloudinary\\Transformation\\BasePosition' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/BasePosition.php',
        'Cloudinary\\Transformation\\BasePositionalSourceContainer' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/BasePositionalSourceContainer.php',
        'Cloudinary\\Transformation\\BaseResizeAction' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/BaseResizeAction.php',
        'Cloudinary\\Transformation\\BaseSource' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/BaseSource.php',
        'Cloudinary\\Transformation\\BaseSourceContainer' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/BaseSourceContainer.php',
        'Cloudinary\\Transformation\\BaseSourceQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/BaseSourceQualifier.php',
        'Cloudinary\\Transformation\\BitRate' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/BitRate.php',
        'Cloudinary\\Transformation\\BlendEffectAction' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/BlendEffectAction.php',
        'Cloudinary\\Transformation\\BlendEffectQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/BlendEffectQualifier.php',
        'Cloudinary\\Transformation\\BlendMode' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/BlendMode.php',
        'Cloudinary\\Transformation\\Blur' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/Blur.php',
        'Cloudinary\\Transformation\\BlurredBackground' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Background/BlurredBackground.php',
        'Cloudinary\\Transformation\\Border' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Border/Border.php',
        'Cloudinary\\Transformation\\BorderQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Border/BorderQualifier.php',
        'Cloudinary\\Transformation\\BorderQualifierTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Border/BorderQualifierTrait.php',
        'Cloudinary\\Transformation\\BorderStyleTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Border/BorderStyleTrait.php',
        'Cloudinary\\Transformation\\BorderValue' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Border/BorderValue.php',
        'Cloudinary\\Transformation\\Cartoonify' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/Cartoonify.php',
        'Cloudinary\\Transformation\\ChromaSubSampling' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Quality/ChromaSubSampling.php',
        'Cloudinary\\Transformation\\ClippingPath' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/ClippingPath.php',
        'Cloudinary\\Transformation\\Codec\\VideoCodecLevel' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/Codec/VideoCodecLevel.php',
        'Cloudinary\\Transformation\\Codec\\VideoCodecProfile' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/Codec/VideoCodecProfile.php',
        'Cloudinary\\Transformation\\ColorEffect' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Color/ColorEffect.php',
        'Cloudinary\\Transformation\\ColorQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Color/ColorQualifier.php',
        'Cloudinary\\Transformation\\ColorQualifierTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Color/ColorQualifierTrait.php',
        'Cloudinary\\Transformation\\ColorSpace' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/ColorSpace/ColorSpace.php',
        'Cloudinary\\Transformation\\ColorSpaceQualifierTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/ColorSpace/ColorSpaceQualifierTrait.php',
        'Cloudinary\\Transformation\\ColorToReplaceTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Color/ColorToReplaceTrait.php',
        'Cloudinary\\Transformation\\ColorTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Color/ColorTrait.php',
        'Cloudinary\\Transformation\\ColoredEffectAction' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Color/ColoredEffectAction.php',
        'Cloudinary\\Transformation\\Colorize' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Color/Colorize.php',
        'Cloudinary\\Transformation\\CommonAdjustmentTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Adjustment/CommonAdjustmentTrait.php',
        'Cloudinary\\Transformation\\CommonEffectTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/CommonEffectTrait.php',
        'Cloudinary\\Transformation\\CommonFlag' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/CommonFlag.php',
        'Cloudinary\\Transformation\\CommonFlagInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/CommonFlagInterface.php',
        'Cloudinary\\Transformation\\CommonFlagTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/CommonFlagTrait.php',
        'Cloudinary\\Transformation\\CommonTransformation' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/CommonTransformation.php',
        'Cloudinary\\Transformation\\CommonTransformationFlagTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/CommonTransformationFlagTrait.php',
        'Cloudinary\\Transformation\\CommonTransformationInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/CommonTransformationInterface.php',
        'Cloudinary\\Transformation\\CommonTransformationTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/CommonTransformationTrait.php',
        'Cloudinary\\Transformation\\Compass' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/Compass.php',
        'Cloudinary\\Transformation\\CompassGravity' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/CompassGravity.php',
        'Cloudinary\\Transformation\\CompassGravityBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/CompassGravityBuilderTrait.php',
        'Cloudinary\\Transformation\\CompassGravityTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/CompassGravityTrait.php',
        'Cloudinary\\Transformation\\CompassPosition' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/CompassPosition.php',
        'Cloudinary\\Transformation\\CompassPositionTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/CompassPositionTrait.php',
        'Cloudinary\\Transformation\\ComponentInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/ComponentInterface.php',
        'Cloudinary\\Transformation\\Concatenate' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Edit/Concatenate.php',
        'Cloudinary\\Transformation\\ConditionQualifierTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Condition/ConditionQualifierTrait.php',
        'Cloudinary\\Transformation\\Conditional' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Condition/Conditional.php',
        'Cloudinary\\Transformation\\CornerRadius' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/CornerRadius.php',
        'Cloudinary\\Transformation\\CornerRadiusQualifierTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/CornerRadiusQualifierTrait.php',
        'Cloudinary\\Transformation\\CornerRadiusTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/CornerRadiusTrait.php',
        'Cloudinary\\Transformation\\Corners' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/Corners.php',
        'Cloudinary\\Transformation\\CornersTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/CornersTrait.php',
        'Cloudinary\\Transformation\\Crop' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Crop/Crop.php',
        'Cloudinary\\Transformation\\CropMode' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Parameter/CropMode.php',
        'Cloudinary\\Transformation\\CropModeTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Parameter/CropModeTrait.php',
        'Cloudinary\\Transformation\\CropPad' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Crop/CropPad.php',
        'Cloudinary\\Transformation\\CropPadTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Crop/CropPadTrait.php',
        'Cloudinary\\Transformation\\CropTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Crop/CropTrait.php',
        'Cloudinary\\Transformation\\CustomFunction' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/CustomFunction/CustomFunction.php',
        'Cloudinary\\Transformation\\CustomFunctionQualifierTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/CustomFunction/CustomFunctionQualifierTrait.php',
        'Cloudinary\\Transformation\\CustomFunctionTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/CustomFunction/CustomFunctionTrait.php',
        'Cloudinary\\Transformation\\CustomFunctionValue' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/CustomFunction/CustomFunctionValue.php',
        'Cloudinary\\Transformation\\CutByImage' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/CutByImage.php',
        'Cloudinary\\Transformation\\CutOut' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/CutOut.php',
        'Cloudinary\\Transformation\\DefaultImage' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Image/Qualifier/DefaultImage.php',
        'Cloudinary\\Transformation\\Delay' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Image/Qualifier/Delay.php',
        'Cloudinary\\Transformation\\Delivery' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Delivery.php',
        'Cloudinary\\Transformation\\DeliveryBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/DeliveryBuilderTrait.php',
        'Cloudinary\\Transformation\\Density' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Image/Qualifier/Density.php',
        'Cloudinary\\Transformation\\Deshake' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Appearance/Deshake.php',
        'Cloudinary\\Transformation\\DetectMultipleTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Generative/DetectMultipleTrait.php',
        'Cloudinary\\Transformation\\DimensionsQualifierTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Dimensions/DimensionsQualifierTrait.php',
        'Cloudinary\\Transformation\\Distort' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/Distort.php',
        'Cloudinary\\Transformation\\Dither' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/Dither.php',
        'Cloudinary\\Transformation\\DropShadow' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/DropShadow.php',
        'Cloudinary\\Transformation\\DropShadowQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/DropShadowQualifier.php',
        'Cloudinary\\Transformation\\Duration' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Timeline/Duration.php',
        'Cloudinary\\Transformation\\DurationEffectAction' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/DurationEffectAction.php',
        'Cloudinary\\Transformation\\DurationEffectQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/DurationEffectQualifier.php',
        'Cloudinary\\Transformation\\Effect' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Effect.php',
        'Cloudinary\\Transformation\\EffectAction' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/EffectAction.php',
        'Cloudinary\\Transformation\\EffectActionTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/EffectActionTrait.php',
        'Cloudinary\\Transformation\\EffectActionTypeTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Argument/EffectActionTypeTrait.php',
        'Cloudinary\\Transformation\\EffectMode' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Argument/EffectMode.php',
        'Cloudinary\\Transformation\\EffectModeTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Argument/EffectModeTrait.php',
        'Cloudinary\\Transformation\\EffectName' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Argument/EffectName.php',
        'Cloudinary\\Transformation\\EffectQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/EffectQualifier.php',
        'Cloudinary\\Transformation\\EffectQualifierTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/EffectQualifierTrait.php',
        'Cloudinary\\Transformation\\EffectRange' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Argument/EffectRange.php',
        'Cloudinary\\Transformation\\EffectType' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Argument/EffectType.php',
        'Cloudinary\\Transformation\\EffectTypeTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Argument/EffectTypeTrait.php',
        'Cloudinary\\Transformation\\ElectronicObjectGravityBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/ElectronicObjectGravityBuilderTrait.php',
        'Cloudinary\\Transformation\\ElectronicObjectGravityInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/ElectronicObjectGravityInterface.php',
        'Cloudinary\\Transformation\\EndIfCondition' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Condition/EndIfCondition.php',
        'Cloudinary\\Transformation\\EndOffset' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Timeline/EndOffset.php',
        'Cloudinary\\Transformation\\ExpressionQualifierMultiValue' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/ExpressionQualifierMultiValue.php',
        'Cloudinary\\Transformation\\Expression\\ArithmeticOperator' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operator/ArithmeticOperator.php',
        'Cloudinary\\Transformation\\Expression\\ArithmeticOperatorBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operator/ArithmeticOperatorBuilderTrait.php',
        'Cloudinary\\Transformation\\Expression\\BaseExpression' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/BaseExpression.php',
        'Cloudinary\\Transformation\\Expression\\BaseExpressionComponent' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/BaseExpressionComponent.php',
        'Cloudinary\\Transformation\\Expression\\BaseOperator' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operator/BaseOperator.php',
        'Cloudinary\\Transformation\\Expression\\Expression' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Expression.php',
        'Cloudinary\\Transformation\\Expression\\ExpressionComponent' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/ExpressionComponent.php',
        'Cloudinary\\Transformation\\Expression\\ExpressionOperator' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/ExpressionOperator.php',
        'Cloudinary\\Transformation\\Expression\\ExpressionTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/ExpressionTrait.php',
        'Cloudinary\\Transformation\\Expression\\ExpressionUtils' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/ExpressionUtils.php',
        'Cloudinary\\Transformation\\Expression\\LogicalOperator' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operator/LogicalOperator.php',
        'Cloudinary\\Transformation\\Expression\\LogicalOperatorBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operator/LogicalOperatorBuilderTrait.php',
        'Cloudinary\\Transformation\\Expression\\Operator' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operator/Operator.php',
        'Cloudinary\\Transformation\\Expression\\PVar' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operand/Variable/Predefined/PVar.php',
        'Cloudinary\\Transformation\\Expression\\PredefinedVariableBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operand/Variable/Predefined/PredefinedVariableBuilderTrait.php',
        'Cloudinary\\Transformation\\Expression\\PredefinedVariableTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operand/Variable/Predefined/PredefinedVariableTrait.php',
        'Cloudinary\\Transformation\\Expression\\RelationalOperator' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operator/RelationalOperator.php',
        'Cloudinary\\Transformation\\Expression\\RelationalOperatorBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operator/RelationalOperatorBuilderTrait.php',
        'Cloudinary\\Transformation\\Expression\\StringRelationalOperator' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operator/StringRelationalOperator.php',
        'Cloudinary\\Transformation\\Expression\\StringRelationalOperatorBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operator/StringRelationalOperatorBuilderTrait.php',
        'Cloudinary\\Transformation\\Expression\\UVal' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operand/Value/UVal.php',
        'Cloudinary\\Transformation\\Expression\\UVar' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operand/Variable/User/UVar.php',
        'Cloudinary\\Transformation\\Expression\\UserVariableBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operand/Variable/User/UserVariableBuilderTrait.php',
        'Cloudinary\\Transformation\\Expression\\ValueBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operand/Value/ValueBuilderTrait.php',
        'Cloudinary\\Transformation\\Extract' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/Extract.php',
        'Cloudinary\\Transformation\\FetchImageSource' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/FetchImageSource.php',
        'Cloudinary\\Transformation\\FetchSourceQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/FetchSourceQualifier.php',
        'Cloudinary\\Transformation\\FetchVideoSource' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/FetchVideoSource.php',
        'Cloudinary\\Transformation\\Fill' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Fill/Fill.php',
        'Cloudinary\\Transformation\\FillLight' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Adjustment/FillLight.php',
        'Cloudinary\\Transformation\\FillPad' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Fill/FillPad.php',
        'Cloudinary\\Transformation\\FillPadTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Fill/FillPadTrait.php',
        'Cloudinary\\Transformation\\FillTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Fill/FillTrait.php',
        'Cloudinary\\Transformation\\Flag' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/Flag.php',
        'Cloudinary\\Transformation\\FlagQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/FlagQualifier.php',
        'Cloudinary\\Transformation\\FlagQualifierTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/FlagQualifierTrait.php',
        'Cloudinary\\Transformation\\FocalGravity' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/FocalGravity.php',
        'Cloudinary\\Transformation\\FocalGravityBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/FocalGravityBuilderTrait.php',
        'Cloudinary\\Transformation\\FocalGravityInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/FocalGravityInterface.php',
        'Cloudinary\\Transformation\\FocalGravityTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/FocalGravityTrait.php',
        'Cloudinary\\Transformation\\FocalPosition' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/FocalPosition.php',
        'Cloudinary\\Transformation\\FocusOn' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/FocusOn.php',
        'Cloudinary\\Transformation\\FoodObjectGravityBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/FoodObjectGravityBuilderTrait.php',
        'Cloudinary\\Transformation\\FoodObjectGravityInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/FoodObjectGravityInterface.php',
        'Cloudinary\\Transformation\\ForegroundObject' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Effect/ForegroundObject.php',
        'Cloudinary\\Transformation\\ForegroundObjectTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Effect/ForegroundObjectTrait.php',
        'Cloudinary\\Transformation\\Format' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/Format.php',
        'Cloudinary\\Transformation\\FormatInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/FormatInterface.php',
        'Cloudinary\\Transformation\\FormatQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/FormatQualifier.php',
        'Cloudinary\\Transformation\\FormatQualifierTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/FormatQualifierTrait.php',
        'Cloudinary\\Transformation\\FormatTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/FormatTrait.php',
        'Cloudinary\\Transformation\\Fps' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/Fps.php',
        'Cloudinary\\Transformation\\Frame' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/Frame.php',
        'Cloudinary\\Transformation\\FullListExpressionQualifierMultiValue' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/FullListExpressionQualifierMultiValue.php',
        'Cloudinary\\Transformation\\FullListQualifierMultiValue' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/FullListQualifierMultiValue.php',
        'Cloudinary\\Transformation\\FurnitureObjectGravityBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/FurnitureObjectGravityBuilderTrait.php',
        'Cloudinary\\Transformation\\FurnitureObjectGravityInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/FurnitureObjectGravityInterface.php',
        'Cloudinary\\Transformation\\GenerativeEffect' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Generative/GenerativeEffect.php',
        'Cloudinary\\Transformation\\GenerativeEffectAction' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Generative/GenerativeEffectAction.php',
        'Cloudinary\\Transformation\\GenerativeEffectTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Generative/GenerativeEffectTrait.php',
        'Cloudinary\\Transformation\\GenerativeFillBackground' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Background/GenerativeFillBackground.php',
        'Cloudinary\\Transformation\\GenerativeRecolor' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Generative/GenerativeRecolor.php',
        'Cloudinary\\Transformation\\GenerativeRemove' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Generative/GenerativeRemove.php',
        'Cloudinary\\Transformation\\GenerativeReplace' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Generative/GenerativeReplace.php',
        'Cloudinary\\Transformation\\GenericResize' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Generic/GenericResize.php',
        'Cloudinary\\Transformation\\GenericResizeTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Generic/GenericResizeTrait.php',
        'Cloudinary\\Transformation\\GradientFade' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/GradientFade.php',
        'Cloudinary\\Transformation\\GradientFadeQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/GradientFadeQualifier.php',
        'Cloudinary\\Transformation\\Gravity' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/Gravity.php',
        'Cloudinary\\Transformation\\GravityQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/GravityQualifier.php',
        'Cloudinary\\Transformation\\GravityQualifierTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/GravityQualifierTrait.php',
        'Cloudinary\\Transformation\\GravityTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/GravityTrait.php',
        'Cloudinary\\Transformation\\IfCondition' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Condition/IfCondition.php',
        'Cloudinary\\Transformation\\IfElse' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Condition/IfElse.php',
        'Cloudinary\\Transformation\\ImageAdjustmentTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Adjustment/ImageAdjustmentTrait.php',
        'Cloudinary\\Transformation\\ImageColorEffectTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Color/ImageColorEffectTrait.php',
        'Cloudinary\\Transformation\\ImageEffect' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/ImageEffect.php',
        'Cloudinary\\Transformation\\ImageEffectTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/ImageEffectTrait.php',
        'Cloudinary\\Transformation\\ImageFlag' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/ImageFlag.php',
        'Cloudinary\\Transformation\\ImageFlagInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/ImageFlagInterface.php',
        'Cloudinary\\Transformation\\ImageFlagTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/ImageFlagTrait.php',
        'Cloudinary\\Transformation\\ImageFormatInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/ImageFormatInterface.php',
        'Cloudinary\\Transformation\\ImageFormatTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/ImageFormatTrait.php',
        'Cloudinary\\Transformation\\ImageOverlay' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/ImageOverlay.php',
        'Cloudinary\\Transformation\\ImagePixelEffectTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/ImagePixelEffectTrait.php',
        'Cloudinary\\Transformation\\ImageQualifierTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Image/Qualifier/ImageQualifierTrait.php',
        'Cloudinary\\Transformation\\ImageQualifierTransformationTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Image/Qualifier/ImageQualifierTransformationTrait.php',
        'Cloudinary\\Transformation\\ImageSource' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/ImageSource.php',
        'Cloudinary\\Transformation\\ImageSourceQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/ImageSourceQualifier.php',
        'Cloudinary\\Transformation\\ImageSourceTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/ImageSourceTrait.php',
        'Cloudinary\\Transformation\\ImageSpecificTransformationTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Image/ImageSpecificTransformationTrait.php',
        'Cloudinary\\Transformation\\ImageTransformation' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Image/ImageTransformation.php',
        'Cloudinary\\Transformation\\ImageTransformationFlagTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/ImageTransformationFlagTrait.php',
        'Cloudinary\\Transformation\\ImageTransformationInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Image/ImageTransformationInterface.php',
        'Cloudinary\\Transformation\\ImageTransformationTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Image/ImageTransformationTrait.php',
        'Cloudinary\\Transformation\\Imagga' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Addon/Imagga.php',
        'Cloudinary\\Transformation\\ImaggaTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Addon/ImaggaTrait.php',
        'Cloudinary\\Transformation\\Improve' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Adjustment/Improve.php',
        'Cloudinary\\Transformation\\ImproveMode' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Adjustment/ImproveMode.php',
        'Cloudinary\\Transformation\\IndoorObjectGravityBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/IndoorObjectGravityBuilderTrait.php',
        'Cloudinary\\Transformation\\IndoorObjectGravityInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/IndoorObjectGravityInterface.php',
        'Cloudinary\\Transformation\\JpegMini' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Quality/JpegMini.php',
        'Cloudinary\\Transformation\\KeyframeInterval' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/KeyframeInterval.php',
        'Cloudinary\\Transformation\\KitchenObjectGravityBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/KitchenObjectGravityBuilderTrait.php',
        'Cloudinary\\Transformation\\KitchenObjectGravityInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/KitchenObjectGravityInterface.php',
        'Cloudinary\\Transformation\\LayerFlag' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/LayerFlag.php',
        'Cloudinary\\Transformation\\LayerFlagInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/LayerFlagInterface.php',
        'Cloudinary\\Transformation\\LayerFlagTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/LayerFlagTrait.php',
        'Cloudinary\\Transformation\\LayerName' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/LayerName.php',
        'Cloudinary\\Transformation\\LayerQualifierFactory' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/LayerQualifierFactory.php',
        'Cloudinary\\Transformation\\LayerQualifierTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/LayerQualifierTrait.php',
        'Cloudinary\\Transformation\\LayerSourceTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/LayerSourceTrait.php',
        'Cloudinary\\Transformation\\LayerStackPosition' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/LayerStackPosition.php',
        'Cloudinary\\Transformation\\LayeredImageTransformationTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Image/LayeredImageTransformationTrait.php',
        'Cloudinary\\Transformation\\LevelEffectAction' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/LevelEffectAction.php',
        'Cloudinary\\Transformation\\LevelEffectQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/LevelEffectQualifier.php',
        'Cloudinary\\Transformation\\LightroomEffect' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Addon/Lightroom/LightroomEffect.php',
        'Cloudinary\\Transformation\\LightroomEffectInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Addon/Lightroom/LightroomEffectInterface.php',
        'Cloudinary\\Transformation\\LightroomEffectQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Addon/Lightroom/LightroomEffectQualifier.php',
        'Cloudinary\\Transformation\\LightroomEffectTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Addon/Lightroom/LightroomEffectTrait.php',
        'Cloudinary\\Transformation\\LimitedEffectQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/LimitedEffectQualifier.php',
        'Cloudinary\\Transformation\\LiquidRescaling' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/LiquidRescaling.php',
        'Cloudinary\\Transformation\\ListEffectQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/ListEffectQualifier.php',
        'Cloudinary\\Transformation\\ListExpressionQualifierMultiValue' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/ListExpressionQualifierMultiValue.php',
        'Cloudinary\\Transformation\\ListQualifierMultiValue' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/ListQualifierMultiValue.php',
        'Cloudinary\\Transformation\\Loop' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Playback/Loop.php',
        'Cloudinary\\Transformation\\LutLayer' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/LutLayer.php',
        'Cloudinary\\Transformation\\LutSourceQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/LutSourceQualifier.php',
        'Cloudinary\\Transformation\\MakeTransparent' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/MakeTransparent.php',
        'Cloudinary\\Transformation\\MatrixValue' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/MatrixValue.php',
        'Cloudinary\\Transformation\\MediaOverlay' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/MediaOverlay.php',
        'Cloudinary\\Transformation\\MinMaxRange' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Argument/Range/MinMaxRange.php',
        'Cloudinary\\Transformation\\MiscEffect' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/MiscEffect.php',
        'Cloudinary\\Transformation\\MiscEffectTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/MiscEffectTrait.php',
        'Cloudinary\\Transformation\\NamedTransformation' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Named/NamedTransformation.php',
        'Cloudinary\\Transformation\\NamedTransformationQualifierTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Named/NamedTransformationQualifierTrait.php',
        'Cloudinary\\Transformation\\ObjectGravity' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/ObjectGravity.php',
        'Cloudinary\\Transformation\\ObjectGravityBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/ObjectGravityBuilderTrait.php',
        'Cloudinary\\Transformation\\ObjectGravityInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/ObjectGravityInterface.php',
        'Cloudinary\\Transformation\\ObjectGravityPriorityBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/ObjectGravityPriorityBuilderTrait.php',
        'Cloudinary\\Transformation\\ObjectGravityPriorityInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/ObjectGravityPriorityInterface.php',
        'Cloudinary\\Transformation\\ObjectGravityTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/ObjectGravityTrait.php',
        'Cloudinary\\Transformation\\Offset' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Offset.php',
        'Cloudinary\\Transformation\\OffsetTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/OffsetTrait.php',
        'Cloudinary\\Transformation\\Opacity' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Adjustment/Opacity.php',
        'Cloudinary\\Transformation\\OpacityQualifierTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Adjustment/OpacityQualifierTrait.php',
        'Cloudinary\\Transformation\\OutdoorObjectGravityBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/OutdoorObjectGravityBuilderTrait.php',
        'Cloudinary\\Transformation\\OutdoorObjectGravityInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/OutdoorObjectGravityInterface.php',
        'Cloudinary\\Transformation\\Outline' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/Outline.php',
        'Cloudinary\\Transformation\\OutlineMode' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/OutlineMode.php',
        'Cloudinary\\Transformation\\OutlineQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/OutlineQualifier.php',
        'Cloudinary\\Transformation\\Overlay' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/Overlay.php',
        'Cloudinary\\Transformation\\Pad' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Pad/Pad.php',
        'Cloudinary\\Transformation\\PadTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Pad/PadTrait.php',
        'Cloudinary\\Transformation\\Page' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/Page.php',
        'Cloudinary\\Transformation\\PageAllTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PageAllTrait.php',
        'Cloudinary\\Transformation\\PageIndexTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PageIndexTrait.php',
        'Cloudinary\\Transformation\\PageLayerNameTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PageLayerNameTrait.php',
        'Cloudinary\\Transformation\\PageLayerNamesTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PageLayerNamesTrait.php',
        'Cloudinary\\Transformation\\PageNameTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PageNameTrait.php',
        'Cloudinary\\Transformation\\PageNamesTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PageNamesTrait.php',
        'Cloudinary\\Transformation\\PageNumberTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PageNumberTrait.php',
        'Cloudinary\\Transformation\\PageQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PageQualifier.php',
        'Cloudinary\\Transformation\\PageQualifierTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PageQualifierTrait.php',
        'Cloudinary\\Transformation\\PageRangeTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PageRangeTrait.php',
        'Cloudinary\\Transformation\\PageValue' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PageValue.php',
        'Cloudinary\\Transformation\\Palette' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Color/Palette.php',
        'Cloudinary\\Transformation\\PersonObjectGravityBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/PersonObjectGravityBuilderTrait.php',
        'Cloudinary\\Transformation\\PersonObjectGravityInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/PersonObjectGravityInterface.php',
        'Cloudinary\\Transformation\\PixelEffect' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/PixelEffect.php',
        'Cloudinary\\Transformation\\PixelEffectRegionTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/PixelEffectRegionTrait.php',
        'Cloudinary\\Transformation\\PixelEffectTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/PixelEffectTrait.php',
        'Cloudinary\\Transformation\\Pixelate' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/Pixelate.php',
        'Cloudinary\\Transformation\\PlaybackEffect' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Playback/PlaybackEffect.php',
        'Cloudinary\\Transformation\\PlaybackEffectTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Playback/PlaybackEffectTrait.php',
        'Cloudinary\\Transformation\\Point' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Point.php',
        'Cloudinary\\Transformation\\PointTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/PointTrait.php',
        'Cloudinary\\Transformation\\Position' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Position.php',
        'Cloudinary\\Transformation\\PositionFlagTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/PositionFlagTrait.php',
        'Cloudinary\\Transformation\\PositioningQualifierTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/PositioningQualifierTrait.php',
        'Cloudinary\\Transformation\\PositiveFloatValue' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Misc/PositiveFloatValue.php',
        'Cloudinary\\Transformation\\Prefix' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Image/Qualifier/Prefix.php',
        'Cloudinary\\Transformation\\Preview' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Playback/Preview.php',
        'Cloudinary\\Transformation\\Progressive' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/Progressive.php',
        'Cloudinary\\Transformation\\PromptTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Generative/PromptTrait.php',
        'Cloudinary\\Transformation\\PsdLayer' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PsdLayer.php',
        'Cloudinary\\Transformation\\PsdTools' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PsdTools.php',
        'Cloudinary\\Transformation\\Qualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Qualifier.php',
        'Cloudinary\\Transformation\\QualifierMultiValue' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/QualifierMultiValue.php',
        'Cloudinary\\Transformation\\Qualifier\\BaseExpressionQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/BaseExpressionQualifier.php',
        'Cloudinary\\Transformation\\Qualifier\\BaseQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/BaseQualifier.php',
        'Cloudinary\\Transformation\\Qualifier\\Dimensions\\Dimensions' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Dimensions/Dimensions.php',
        'Cloudinary\\Transformation\\Qualifier\\Dimensions\\DimensionsTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Dimensions/DimensionsTrait.php',
        'Cloudinary\\Transformation\\Qualifier\\Dimensions\\Dpr' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Dpr.php',
        'Cloudinary\\Transformation\\Qualifier\\Dimensions\\Height' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Dimensions/Height.php',
        'Cloudinary\\Transformation\\Qualifier\\Dimensions\\Width' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Dimensions/Width.php',
        'Cloudinary\\Transformation\\Qualifier\\GenericQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/GenericQualifier.php',
        'Cloudinary\\Transformation\\Qualifier\\Misc\\BreakpointsJson' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Misc/BreakpointsJson.php',
        'Cloudinary\\Transformation\\Qualifier\\Value\\ColorValueTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Color/ColorValueTrait.php',
        'Cloudinary\\Transformation\\QualifiersAction' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifiersAction.php',
        'Cloudinary\\Transformation\\Quality' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Quality/Quality.php',
        'Cloudinary\\Transformation\\QualityBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Quality/QualityBuilderTrait.php',
        'Cloudinary\\Transformation\\QualityQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Quality/QualityQualifier.php',
        'Cloudinary\\Transformation\\QualityQualifierTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Quality/QualityQualifierTrait.php',
        'Cloudinary\\Transformation\\QualityTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Quality/QualityTrait.php',
        'Cloudinary\\Transformation\\RecolorQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Adjustment/RecolorQualifier.php',
        'Cloudinary\\Transformation\\RectangleRegion' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Canvas/RectangleRegion.php',
        'Cloudinary\\Transformation\\Region' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Region.php',
        'Cloudinary\\Transformation\\RegionEffectAction' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/RegionEffectAction.php',
        'Cloudinary\\Transformation\\RegionEffectTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/RegionEffectTrait.php',
        'Cloudinary\\Transformation\\RegionTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/RegionTrait.php',
        'Cloudinary\\Transformation\\RemoteSourceValue' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/RemoteSourceValue.php',
        'Cloudinary\\Transformation\\RemoveBackground' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/RemoveBackground.php',
        'Cloudinary\\Transformation\\ReplaceColor' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Adjustment/ReplaceColor.php',
        'Cloudinary\\Transformation\\Reshape' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/Reshape.php',
        'Cloudinary\\Transformation\\ReshapeQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/ReshapeQualifier.php',
        'Cloudinary\\Transformation\\ReshapeTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/ReshapeTrait.php',
        'Cloudinary\\Transformation\\Resize' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Resize.php',
        'Cloudinary\\Transformation\\ResizeFactory' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/ResizeFactory.php',
        'Cloudinary\\Transformation\\ResizeMode' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Parameter/ResizeMode.php',
        'Cloudinary\\Transformation\\ResizeModeTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Parameter/ResizeModeTrait.php',
        'Cloudinary\\Transformation\\ResizeQualifierTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Parameter/ResizeQualifierTrait.php',
        'Cloudinary\\Transformation\\ResizeTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/ResizeTrait.php',
        'Cloudinary\\Transformation\\Rotate' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Rotation/Rotate.php',
        'Cloudinary\\Transformation\\RotationDegreeInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Rotation/RotationDegreeInterface.php',
        'Cloudinary\\Transformation\\RotationModeInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Rotation/RotationModeInterface.php',
        'Cloudinary\\Transformation\\RoundCorners' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/RoundCorners.php',
        'Cloudinary\\Transformation\\Scale' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Scale/Scale.php',
        'Cloudinary\\Transformation\\ScaleTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Scale/ScaleTrait.php',
        'Cloudinary\\Transformation\\Shadow' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/Shadow.php',
        'Cloudinary\\Transformation\\ShakeStrength' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Appearance/ShakeStrength.php',
        'Cloudinary\\Transformation\\Shear' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/Shear.php',
        'Cloudinary\\Transformation\\SimulateColorBlind' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Color/SimulateColorBlind.php',
        'Cloudinary\\Transformation\\SmartObject' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/SmartObject.php',
        'Cloudinary\\Transformation\\SmartObjectName' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/SmartObjectName.php',
        'Cloudinary\\Transformation\\SmartObjectQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/SmartObjectQualifier.php',
        'Cloudinary\\Transformation\\Source' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/Source.php',
        'Cloudinary\\Transformation\\SourceBasedEffectAction' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/SourceBasedEffectAction.php',
        'Cloudinary\\Transformation\\SourceValue' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/SourceValue.php',
        'Cloudinary\\Transformation\\SquareSizeEffectAction' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/SquareSizeEffectAction.php',
        'Cloudinary\\Transformation\\SquareSizeEffectQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/SquareSizeEffectQualifier.php',
        'Cloudinary\\Transformation\\StartOffset' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Timeline/StartOffset.php',
        'Cloudinary\\Transformation\\StreamingProfile' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/StreamingProfile.php',
        'Cloudinary\\Transformation\\StrengthEffectAction' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/StrengthEffectAction.php',
        'Cloudinary\\Transformation\\StrengthEffectQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/StrengthEffectQualifier.php',
        'Cloudinary\\Transformation\\StyleTransfer' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/StyleTransfer/StyleTransfer.php',
        'Cloudinary\\Transformation\\StyleTransferQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/StyleTransfer/StyleTransferQualifier.php',
        'Cloudinary\\Transformation\\SubtitlesSource' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/SubtitlesSource.php',
        'Cloudinary\\Transformation\\SubtitlesSourceQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/SubtitlesSourceQualifier.php',
        'Cloudinary\\Transformation\\TextColorTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Color/TextColorTrait.php',
        'Cloudinary\\Transformation\\TextFit' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Dimensions/TextFit.php',
        'Cloudinary\\Transformation\\TextFitTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Dimensions/TextFitTrait.php',
        'Cloudinary\\Transformation\\TextSource' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/TextSource.php',
        'Cloudinary\\Transformation\\TextSourceQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/TextSourceQualifier.php',
        'Cloudinary\\Transformation\\TextStyle' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Text/TextStyle.php',
        'Cloudinary\\Transformation\\ThemeEffect' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Appearance/ThemeEffect.php',
        'Cloudinary\\Transformation\\ThemeEffectTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Appearance/ThemeEffectTrait.php',
        'Cloudinary\\Transformation\\ThemeQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Appearance/ThemeQualifier.php',
        'Cloudinary\\Transformation\\ThresholdEffectAction' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/ThresholdEffectAction.php',
        'Cloudinary\\Transformation\\ThresholdEffectQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/ThresholdEffectQualifier.php',
        'Cloudinary\\Transformation\\Timeline' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Timeline/Timeline.php',
        'Cloudinary\\Transformation\\TimelineQualifierTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Timeline/TimelineQualifierTrait.php',
        'Cloudinary\\Transformation\\ToAnimatedAction' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/ToAnimated/ToAnimatedAction.php',
        'Cloudinary\\Transformation\\ToAnimatedActionTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/ToAnimated/ToAnimatedActionTrait.php',
        'Cloudinary\\Transformation\\ToleranceEffectAction' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/ToleranceEffectAction.php',
        'Cloudinary\\Transformation\\ToleranceEffectQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/ToleranceEffectQualifier.php',
        'Cloudinary\\Transformation\\Transcode' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/Transcode.php',
        'Cloudinary\\Transformation\\TranscodeTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/TranscodeTrait.php',
        'Cloudinary\\Transformation\\Transformation' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Transformation.php',
        'Cloudinary\\Transformation\\TransformationCustomFunctionTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/CustomFunction/TransformationCustomFunctionTrait.php',
        'Cloudinary\\Transformation\\TransformationDeliveryTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/TransformationDeliveryTrait.php',
        'Cloudinary\\Transformation\\TransformationResizeTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/TransformationResizeTrait.php',
        'Cloudinary\\Transformation\\TransformationTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/TransformationTrait.php',
        'Cloudinary\\Transformation\\Transition' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/Transition.php',
        'Cloudinary\\Transformation\\TrimEffect' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/TrimEffect.php',
        'Cloudinary\\Transformation\\Underlay' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/Underlay.php',
        'Cloudinary\\Transformation\\ValueEffectQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/ValueEffectQualifier.php',
        'Cloudinary\\Transformation\\Variable\\Variable' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Variable/Variable.php',
        'Cloudinary\\Transformation\\Variable\\VariableValue' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Variable/VariableValue.php',
        'Cloudinary\\Transformation\\Vectorize' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/Vectorize.php',
        'Cloudinary\\Transformation\\VectorizeTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/VectorizeTrait.php',
        'Cloudinary\\Transformation\\VectorizeValue' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/VectorizeValue.php',
        'Cloudinary\\Transformation\\VehicleObjectGravityBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/VehicleObjectGravityBuilderTrait.php',
        'Cloudinary\\Transformation\\VehicleObjectGravityInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/VehicleObjectGravityInterface.php',
        'Cloudinary\\Transformation\\VideoAppearanceEffectTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Appearance/VideoAppearanceEffectTrait.php',
        'Cloudinary\\Transformation\\VideoCodec' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/Codec/VideoCodec.php',
        'Cloudinary\\Transformation\\VideoCodecTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/Codec/VideoCodecTrait.php',
        'Cloudinary\\Transformation\\VideoEdit' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Edit/VideoEdit.php',
        'Cloudinary\\Transformation\\VideoEditBuilderTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Edit/VideoEditBuilderTrait.php',
        'Cloudinary\\Transformation\\VideoEffect' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/VideoEffect.php',
        'Cloudinary\\Transformation\\VideoEffectTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/VideoEffectTrait.php',
        'Cloudinary\\Transformation\\VideoFlag' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/VideoFlag.php',
        'Cloudinary\\Transformation\\VideoFlagInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/VideoFlagInterface.php',
        'Cloudinary\\Transformation\\VideoFlagTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/VideoFlagTrait.php',
        'Cloudinary\\Transformation\\VideoFormatInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/VideoFormatInterface.php',
        'Cloudinary\\Transformation\\VideoFormatTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/VideoFormatTrait.php',
        'Cloudinary\\Transformation\\VideoOffset' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Timeline/VideoOffset.php',
        'Cloudinary\\Transformation\\VideoOverlay' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/VideoOverlay.php',
        'Cloudinary\\Transformation\\VideoQualifierTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/VideoQualifierTrait.php',
        'Cloudinary\\Transformation\\VideoSampling' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/ToAnimated/VideoSampling.php',
        'Cloudinary\\Transformation\\VideoSource' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/VideoSource.php',
        'Cloudinary\\Transformation\\VideoSourceQualifier' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/VideoSourceQualifier.php',
        'Cloudinary\\Transformation\\VideoSourceTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/VideoSourceTrait.php',
        'Cloudinary\\Transformation\\VideoSpecificTransformationTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/VideoSpecificTransformationTrait.php',
        'Cloudinary\\Transformation\\VideoTransformation' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/VideoTransformation.php',
        'Cloudinary\\Transformation\\VideoTransformationFlagTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/VideoTransformationFlagTrait.php',
        'Cloudinary\\Transformation\\VideoTransformationInterface' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/VideoTransformationInterface.php',
        'Cloudinary\\Transformation\\VideoTransformationTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/VideoTransformationTrait.php',
        'Cloudinary\\Transformation\\ViesusCorrect' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Adjustment/Addon/ViesusCorrect.php',
        'Cloudinary\\Transformation\\Volume' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Playback/Volume.php',
        'Cloudinary\\Transformation\\WhiteBalance' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Addon/Lightroom/WhiteBalance.php',
        'Cloudinary\\Transformation\\X' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/X.php',
        'Cloudinary\\Transformation\\Xmp' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Addon/Lightroom/Xmp.php',
        'Cloudinary\\Transformation\\XmpSourceValue' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Addon/Lightroom/XmpSourceValue.php',
        'Cloudinary\\Transformation\\Y' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Y.php',
        'Cloudinary\\Transformation\\Zoom' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Parameter/Zoom.php',
        'Cloudinary\\Transformation\\ZoomTrait' => __DIR__ . '/..' . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Parameter/ZoomTrait.php',
        'Cloudinary\\Utils' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Utils/Utils.php',
        'Cloudinary\\Utils\\SignatureVerifier' => __DIR__ . '/..' . '/cloudinary/cloudinary_php/src/Utils/SignatureVerifier.php',
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'PhpToken' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/PhpToken.php',
        'Stringable' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/Stringable.php',
        'UnhandledMatchError' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/UnhandledMatchError.php',
        'ValueError' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/ValueError.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit51535c02de4759f571e4091a5b35e356::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit51535c02de4759f571e4091a5b35e356::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit51535c02de4759f571e4091a5b35e356::$classMap;

        }, null, ClassLoader::class);
    }
}
