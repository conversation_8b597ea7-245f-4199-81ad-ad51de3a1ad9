<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Attribute' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Attribute.php',
    'Cloudinary\\Api\\Admin\\AdminApi' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Admin/AdminApi.php',
    'Cloudinary\\Api\\Admin\\AnalysisTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Admin/AnalysisTrait.php',
    'Cloudinary\\Api\\Admin\\ApiEndPoint' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Admin/ApiEndPoint.php',
    'Cloudinary\\Api\\Admin\\AssetsTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Admin/AssetsTrait.php',
    'Cloudinary\\Api\\Admin\\FoldersTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Admin/FoldersTrait.php',
    'Cloudinary\\Api\\Admin\\MetadataFieldsTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Admin/MetadataFieldsTrait.php',
    'Cloudinary\\Api\\Admin\\MiscTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Admin/MiscTrait.php',
    'Cloudinary\\Api\\Admin\\StreamingProfilesTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Admin/StreamingProfilesTrait.php',
    'Cloudinary\\Api\\Admin\\TransformationsTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Admin/TransformationsTrait.php',
    'Cloudinary\\Api\\Admin\\UploadMappingsTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Admin/UploadMappingsTrait.php',
    'Cloudinary\\Api\\Admin\\UploadPresetsTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Admin/UploadPresetsTrait.php',
    'Cloudinary\\Api\\ApiClient' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/ApiClient.php',
    'Cloudinary\\Api\\ApiResponse' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/ApiResponse.php',
    'Cloudinary\\Api\\ApiUtils' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Utils/ApiUtils.php',
    'Cloudinary\\Api\\BaseApiClient' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/BaseApiClient.php',
    'Cloudinary\\Api\\Exception\\AlreadyExists' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Exception/AlreadyExists.php',
    'Cloudinary\\Api\\Exception\\ApiError' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Exception/ApiError.php',
    'Cloudinary\\Api\\Exception\\AuthorizationRequired' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Exception/AuthorizationRequired.php',
    'Cloudinary\\Api\\Exception\\BadRequest' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Exception/BadRequest.php',
    'Cloudinary\\Api\\Exception\\GeneralError' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Exception/GeneralError.php',
    'Cloudinary\\Api\\Exception\\NotAllowed' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Exception/NotAllowed.php',
    'Cloudinary\\Api\\Exception\\NotFound' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Exception/NotFound.php',
    'Cloudinary\\Api\\Exception\\RateLimited' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Exception/RateLimited.php',
    'Cloudinary\\Api\\HttpMethod' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Utils/HttpMethod.php',
    'Cloudinary\\Api\\HttpStatusCode' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Utils/HttpStatusCode.php',
    'Cloudinary\\Api\\Metadata\\DateMetadataField' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Metadata/DateMetadataField.php',
    'Cloudinary\\Api\\Metadata\\EnumMetadataField' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Metadata/EnumMetadataField.php',
    'Cloudinary\\Api\\Metadata\\IntMetadataField' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Metadata/IntMetadataField.php',
    'Cloudinary\\Api\\Metadata\\Metadata' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Metadata/Metadata.php',
    'Cloudinary\\Api\\Metadata\\MetadataDataEntry' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Metadata/MetadataDataEntry.php',
    'Cloudinary\\Api\\Metadata\\MetadataDataSource' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Metadata/MetadataDataSource.php',
    'Cloudinary\\Api\\Metadata\\MetadataField' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Metadata/MetadataField.php',
    'Cloudinary\\Api\\Metadata\\MetadataFieldList' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Metadata/MetadataFieldList.php',
    'Cloudinary\\Api\\Metadata\\MetadataFieldType' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Metadata/MetadataFieldType.php',
    'Cloudinary\\Api\\Metadata\\SetMetadataField' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Metadata/SetMetadataField.php',
    'Cloudinary\\Api\\Metadata\\StringMetadataField' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Metadata/StringMetadataField.php',
    'Cloudinary\\Api\\Metadata\\Validators\\AndValidator' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Metadata/Validators/AndValidator.php',
    'Cloudinary\\Api\\Metadata\\Validators\\ComparisonRule' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Metadata/Validators/ComparisonRule.php',
    'Cloudinary\\Api\\Metadata\\Validators\\DateGreaterThan' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Metadata/Validators/DateGreaterThan.php',
    'Cloudinary\\Api\\Metadata\\Validators\\DateLessThan' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Metadata/Validators/DateLessThan.php',
    'Cloudinary\\Api\\Metadata\\Validators\\IntGreaterThan' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Metadata/Validators/IntGreaterThan.php',
    'Cloudinary\\Api\\Metadata\\Validators\\IntLessThan' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Metadata/Validators/IntLessThan.php',
    'Cloudinary\\Api\\Metadata\\Validators\\MetadataValidation' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Metadata/Validators/MetadataValidation.php',
    'Cloudinary\\Api\\Metadata\\Validators\\StringLength' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Metadata/Validators/StringLength.php',
    'Cloudinary\\Api\\Provisioning\\AccountApi' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Provisioning/AccountApi.php',
    'Cloudinary\\Api\\Provisioning\\AccountApiClient' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Provisioning/AccountApiClient.php',
    'Cloudinary\\Api\\Provisioning\\AccountEndPoint' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Provisioning/AccountEndPoint.php',
    'Cloudinary\\Api\\Provisioning\\UserRole' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Provisioning/UserRole.php',
    'Cloudinary\\Api\\Search\\SearchApi' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Search/SearchApi.php',
    'Cloudinary\\Api\\Search\\SearchFoldersApi' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Search/SearchFoldersApi.php',
    'Cloudinary\\Api\\Search\\SearchQueryInterface' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Search/SearchQueryInterface.php',
    'Cloudinary\\Api\\Search\\SearchQueryTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Search/SearchQueryTrait.php',
    'Cloudinary\\Api\\UploadApiClient' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/UploadApiClient.php',
    'Cloudinary\\Api\\Upload\\ArchiveTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Upload/ArchiveTrait.php',
    'Cloudinary\\Api\\Upload\\ContextCommand' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Upload/ContextCommand.php',
    'Cloudinary\\Api\\Upload\\ContextTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Upload/ContextTrait.php',
    'Cloudinary\\Api\\Upload\\CreativeTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Upload/CreativeTrait.php',
    'Cloudinary\\Api\\Upload\\EditTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Upload/EditTrait.php',
    'Cloudinary\\Api\\Upload\\TagCommand' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Upload/TagCommand.php',
    'Cloudinary\\Api\\Upload\\TagTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Upload/TagTrait.php',
    'Cloudinary\\Api\\Upload\\UploadApi' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Upload/UploadApi.php',
    'Cloudinary\\Api\\Upload\\UploadEndPoint' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Upload/UploadEndPoint.php',
    'Cloudinary\\Api\\Upload\\UploadTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Api/Upload/UploadTrait.php',
    'Cloudinary\\ArrayUtils' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Utils/ArrayUtils.php',
    'Cloudinary\\Asset\\AccessControl\\AccessControlRule' => $vendorDir . '/cloudinary/cloudinary_php/src/Asset/AccessControl/AccessControlRule.php',
    'Cloudinary\\Asset\\AccessControl\\AccessType' => $vendorDir . '/cloudinary/cloudinary_php/src/Asset/AccessControl/AccessType.php',
    'Cloudinary\\Asset\\Analytics' => $vendorDir . '/cloudinary/cloudinary_php/src/Asset/Analytics/Analytics.php',
    'Cloudinary\\Asset\\AssetDescriptor' => $vendorDir . '/cloudinary/cloudinary_php/src/Asset/Descriptor/AssetDescriptor.php',
    'Cloudinary\\Asset\\AssetDescriptorTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Asset/Descriptor/AssetDescriptorTrait.php',
    'Cloudinary\\Asset\\AssetFinalizerTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Asset/AssetFinalizerTrait.php',
    'Cloudinary\\Asset\\AssetInterface' => $vendorDir . '/cloudinary/cloudinary_php/src/Asset/AssetInterface.php',
    'Cloudinary\\Asset\\AssetQualifiers' => $vendorDir . '/cloudinary/cloudinary_php/src/Asset/AssetQualifiers.php',
    'Cloudinary\\Asset\\AssetTransformation' => $vendorDir . '/cloudinary/cloudinary_php/src/Asset/Descriptor/AssetTransformation.php',
    'Cloudinary\\Asset\\AssetType' => $vendorDir . '/cloudinary/cloudinary_php/src/Asset/Descriptor/AssetType.php',
    'Cloudinary\\Asset\\AuthToken' => $vendorDir . '/cloudinary/cloudinary_php/src/Asset/AuthToken.php',
    'Cloudinary\\Asset\\BaseAsset' => $vendorDir . '/cloudinary/cloudinary_php/src/Asset/BaseAsset.php',
    'Cloudinary\\Asset\\BaseMediaAsset' => $vendorDir . '/cloudinary/cloudinary_php/src/Asset/BaseMediaAsset.php',
    'Cloudinary\\Asset\\DeliveryType' => $vendorDir . '/cloudinary/cloudinary_php/src/Asset/Descriptor/DeliveryType.php',
    'Cloudinary\\Asset\\DeliveryTypeTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Asset/DeliveryTypeTrait.php',
    'Cloudinary\\Asset\\File' => $vendorDir . '/cloudinary/cloudinary_php/src/Asset/File.php',
    'Cloudinary\\Asset\\Image' => $vendorDir . '/cloudinary/cloudinary_php/src/Asset/Image.php',
    'Cloudinary\\Asset\\Media' => $vendorDir . '/cloudinary/cloudinary_php/src/Asset/Media.php',
    'Cloudinary\\Asset\\MediaAssetFinalizerTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Asset/MediaAssetFinalizerTrait.php',
    'Cloudinary\\Asset\\ModerationStatus' => $vendorDir . '/cloudinary/cloudinary_php/src/Asset/Moderation/ModerationStatus.php',
    'Cloudinary\\Asset\\ModerationType' => $vendorDir . '/cloudinary/cloudinary_php/src/Asset/Moderation/ModerationType.php',
    'Cloudinary\\Asset\\SearchAsset' => $vendorDir . '/cloudinary/cloudinary_php/src/Asset/SearchAsset.php',
    'Cloudinary\\Asset\\SearchAssetTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Asset/SearchAssetTrait.php',
    'Cloudinary\\Asset\\Video' => $vendorDir . '/cloudinary/cloudinary_php/src/Asset/Video.php',
    'Cloudinary\\ClassUtils' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Utils/ClassUtils.php',
    'Cloudinary\\Cloudinary' => $vendorDir . '/cloudinary/cloudinary_php/src/Cloudinary.php',
    'Cloudinary\\Configuration\\ApiConfig' => $vendorDir . '/cloudinary/cloudinary_php/src/Configuration/ApiConfig.php',
    'Cloudinary\\Configuration\\AssetConfigTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Configuration/AssetConfigTrait.php',
    'Cloudinary\\Configuration\\AuthTokenConfig' => $vendorDir . '/cloudinary/cloudinary_php/src/Configuration/AuthTokenConfig.php',
    'Cloudinary\\Configuration\\BaseConfigSection' => $vendorDir . '/cloudinary/cloudinary_php/src/Configuration/BaseConfigSection.php',
    'Cloudinary\\Configuration\\CloudConfig' => $vendorDir . '/cloudinary/cloudinary_php/src/Configuration/CloudConfig.php',
    'Cloudinary\\Configuration\\CloudConfigTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Configuration/CloudConfigTrait.php',
    'Cloudinary\\Configuration\\ConfigUtils' => $vendorDir . '/cloudinary/cloudinary_php/src/Configuration/ConfigUtils.php',
    'Cloudinary\\Configuration\\ConfigurableInterface' => $vendorDir . '/cloudinary/cloudinary_php/src/Configuration/ConfigurableInterface.php',
    'Cloudinary\\Configuration\\Configuration' => $vendorDir . '/cloudinary/cloudinary_php/src/Configuration/Configuration.php',
    'Cloudinary\\Configuration\\LoggingConfig' => $vendorDir . '/cloudinary/cloudinary_php/src/Configuration/LoggingConfig.php',
    'Cloudinary\\Configuration\\Provisioning\\ProvisioningAccountConfig' => $vendorDir . '/cloudinary/cloudinary_php/src/Configuration/Provisioning/ProvisioningAccountConfig.php',
    'Cloudinary\\Configuration\\Provisioning\\ProvisioningConfigUtils' => $vendorDir . '/cloudinary/cloudinary_php/src/Configuration/Provisioning/ProvisioningConfigUtils.php',
    'Cloudinary\\Configuration\\Provisioning\\ProvisioningConfiguration' => $vendorDir . '/cloudinary/cloudinary_php/src/Configuration/Provisioning/ProvisioningConfiguration.php',
    'Cloudinary\\Configuration\\ResponsiveBreakpointsConfig' => $vendorDir . '/cloudinary/cloudinary_php/src/Configuration/ResponsiveBreakpointsConfig.php',
    'Cloudinary\\Configuration\\TagConfig' => $vendorDir . '/cloudinary/cloudinary_php/src/Configuration/TagConfig.php',
    'Cloudinary\\Configuration\\TagConfigTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Configuration/TagConfigTrait.php',
    'Cloudinary\\Configuration\\UrlConfig' => $vendorDir . '/cloudinary/cloudinary_php/src/Configuration/UrlConfig.php',
    'Cloudinary\\Configuration\\UrlConfigTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Configuration/UrlConfigTrait.php',
    'Cloudinary\\Exception\\ConfigurationException' => $vendorDir . '/cloudinary/cloudinary_php/src/Exception/ConfigurationException.php',
    'Cloudinary\\Exception\\Error' => $vendorDir . '/cloudinary/cloudinary_php/src/Exception/Error.php',
    'Cloudinary\\FileUtils' => $vendorDir . '/cloudinary/cloudinary_php/src/Utils/FileUtils.php',
    'Cloudinary\\HttpClient' => $vendorDir . '/cloudinary/cloudinary_php/src/HttpClient/HttpClient.php',
    'Cloudinary\\JsonUtils' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Utils/JsonUtils.php',
    'Cloudinary\\Log\\Logger' => $vendorDir . '/cloudinary/cloudinary_php/src/Log/Logger.php',
    'Cloudinary\\Log\\LoggerDecorator' => $vendorDir . '/cloudinary/cloudinary_php/src/Log/LoggerDecorator.php',
    'Cloudinary\\Log\\LoggerDecoratorTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Log/LoggerDecoratorTrait.php',
    'Cloudinary\\Log\\LoggerTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Log/LoggerTrait.php',
    'Cloudinary\\Log\\LoggersList' => $vendorDir . '/cloudinary/cloudinary_php/src/Log/LoggersList.php',
    'Cloudinary\\StringUtils' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Utils/StringUtils.php',
    'Cloudinary\\Tag\\AudioSourceType' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/Attribute/AudioSourceType.php',
    'Cloudinary\\Tag\\BaseConfigurableApiTag' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/BaseConfigurableApiTag.php',
    'Cloudinary\\Tag\\BaseImageTag' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/BaseImageTag.php',
    'Cloudinary\\Tag\\BaseTag' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/BaseTag.php',
    'Cloudinary\\Tag\\ClientHintsMetaTag' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/ClientHintsMetaTag.php',
    'Cloudinary\\Tag\\FormInputTag' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/FormInputTag.php',
    'Cloudinary\\Tag\\FormTag' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/FormTag.php',
    'Cloudinary\\Tag\\ImageTag' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/ImageTag.php',
    'Cloudinary\\Tag\\ImageTagDeliveryTypeTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/ImageTagDeliveryTypeTrait.php',
    'Cloudinary\\Tag\\JsConfigTag' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/JsConfigTag.php',
    'Cloudinary\\Tag\\Media' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/Attribute/Media.php',
    'Cloudinary\\Tag\\PictureSourceTag' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/PictureSourceTag.php',
    'Cloudinary\\Tag\\PictureTag' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/PictureTag.php',
    'Cloudinary\\Tag\\Sizes' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/Attribute/Sizes.php',
    'Cloudinary\\Tag\\SourceType' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/Attribute/SourceType.php',
    'Cloudinary\\Tag\\SpriteTag' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/SpriteTag.php',
    'Cloudinary\\Tag\\SrcSet' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/Attribute/SrcSet.php',
    'Cloudinary\\Tag\\Tag' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/Tag.php',
    'Cloudinary\\Tag\\TagBuilder' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/TagBuilder.php',
    'Cloudinary\\Tag\\TagUtils' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/TagUtils.php',
    'Cloudinary\\Tag\\UploadTag' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/UploadTag.php',
    'Cloudinary\\Tag\\VideoSourceTag' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/VideoSourceTag.php',
    'Cloudinary\\Tag\\VideoSourceType' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/Attribute/VideoSourceType.php',
    'Cloudinary\\Tag\\VideoTag' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/VideoTag.php',
    'Cloudinary\\Tag\\VideoTagDeliveryTypeTrait' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/VideoTagDeliveryTypeTrait.php',
    'Cloudinary\\Tag\\VideoThumbnailTag' => $vendorDir . '/cloudinary/cloudinary_php/src/Tag/VideoThumbnailTag.php',
    'Cloudinary\\TransformationUtils' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Utils/TransformationUtils.php',
    'Cloudinary\\Transformation\\AbsolutePosition' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/AbsolutePosition.php',
    'Cloudinary\\Transformation\\AbsolutePositionTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/AbsolutePositionTrait.php',
    'Cloudinary\\Transformation\\Accelerate' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Playback/Accelerate.php',
    'Cloudinary\\Transformation\\AccessoryObjectGravityBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/AccessoryObjectGravityBuilderTrait.php',
    'Cloudinary\\Transformation\\AccessoryObjectGravityInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/AccessoryObjectGravityInterface.php',
    'Cloudinary\\Transformation\\Action' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Action.php',
    'Cloudinary\\Transformation\\AddonEffectTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Addon/AddonEffectTrait.php',
    'Cloudinary\\Transformation\\Adjust' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Adjustment/Adjust.php',
    'Cloudinary\\Transformation\\AdjustmentInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Adjustment/AdjustmentInterface.php',
    'Cloudinary\\Transformation\\Angle' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Rotation/Angle.php',
    'Cloudinary\\Transformation\\AngleQualifierTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Rotation/AngleQualifierTrait.php',
    'Cloudinary\\Transformation\\AnimalObjectGravityBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/AnimalObjectGravityBuilderTrait.php',
    'Cloudinary\\Transformation\\AnimalObjectGravityInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/AnimalObjectGravityInterface.php',
    'Cloudinary\\Transformation\\Animated' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Animated/Animated.php',
    'Cloudinary\\Transformation\\AnimatedEdit' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Animated/AnimatedEdit.php',
    'Cloudinary\\Transformation\\AnimatedEditTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Animated/AnimatedEditTrait.php',
    'Cloudinary\\Transformation\\AnimatedFormat' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/AnimatedFormat.php',
    'Cloudinary\\Transformation\\AnimatedFormatTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/AnimatedFormatTrait.php',
    'Cloudinary\\Transformation\\AppearanceEffect' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Appearance/AppearanceEffect.php',
    'Cloudinary\\Transformation\\ApplianceObjectGravityBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/ApplianceObjectGravityBuilderTrait.php',
    'Cloudinary\\Transformation\\ApplianceObjectGravityInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/ApplianceObjectGravityInterface.php',
    'Cloudinary\\Transformation\\Argument\\AngleTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Rotation/AngleTrait.php',
    'Cloudinary\\Transformation\\Argument\\BaseNamedArgument' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/BaseNamedArgument.php',
    'Cloudinary\\Transformation\\Argument\\Color' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Color/Color.php',
    'Cloudinary\\Transformation\\Argument\\ColorTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Color/ColorTrait.php',
    'Cloudinary\\Transformation\\Argument\\ColorValue' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Color/ColorValue.php',
    'Cloudinary\\Transformation\\Argument\\Degree' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Rotation/Degree.php',
    'Cloudinary\\Transformation\\Argument\\GenericArgument' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/GenericArgument.php',
    'Cloudinary\\Transformation\\Argument\\GenericNamedArgument' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/GenericNamedArgument.php',
    'Cloudinary\\Transformation\\Argument\\Gradient' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Color/Gradient.php',
    'Cloudinary\\Transformation\\Argument\\GradientDirection' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Color/GradientDirection.php',
    'Cloudinary\\Transformation\\Argument\\IndexedArgument' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Misc/IndexedArgument.php',
    'Cloudinary\\Transformation\\Argument\\LimitedGenericNamedArgument' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/LimitedGenericNamedArgument.php',
    'Cloudinary\\Transformation\\Argument\\PointValue' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Canvas/PointValue.php',
    'Cloudinary\\Transformation\\Argument\\RangeArgument' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Misc/RangeArgument.php',
    'Cloudinary\\Transformation\\Argument\\Range\\PreviewDuration' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Argument/Range/PreviewDuration.php',
    'Cloudinary\\Transformation\\Argument\\Range\\Range' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Argument/Range/Range.php',
    'Cloudinary\\Transformation\\Argument\\RotationMode' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Rotation/RotationMode.php',
    'Cloudinary\\Transformation\\Argument\\RotationModeTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Rotation/RotationModeTrait.php',
    'Cloudinary\\Transformation\\Argument\\Text\\FontAntialias' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Text/FontAntialias.php',
    'Cloudinary\\Transformation\\Argument\\Text\\FontFamily' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Text/FontFamily.php',
    'Cloudinary\\Transformation\\Argument\\Text\\FontHinting' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Text/FontHinting.php',
    'Cloudinary\\Transformation\\Argument\\Text\\FontStyle' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Text/FontStyle.php',
    'Cloudinary\\Transformation\\Argument\\Text\\FontWeight' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Text/FontWeight.php',
    'Cloudinary\\Transformation\\Argument\\Text\\Stroke' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Text/Stroke.php',
    'Cloudinary\\Transformation\\Argument\\Text\\Text' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Text/Text.php',
    'Cloudinary\\Transformation\\Argument\\Text\\TextAlignment' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Text/TextAlignment.php',
    'Cloudinary\\Transformation\\Argument\\Text\\TextDecoration' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Text/TextDecoration.php',
    'Cloudinary\\Transformation\\Argument\\Text\\TextStyleTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Text/TextStyleTrait.php',
    'Cloudinary\\Transformation\\Argument\\Text\\TextTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Text/TextTrait.php',
    'Cloudinary\\Transformation\\ArtisticFilter' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/ArtisticFilter.php',
    'Cloudinary\\Transformation\\AspectRatio' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Dimensions/AspectRatio.php',
    'Cloudinary\\Transformation\\AssetBasedSource' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/AssetBasedSource.php',
    'Cloudinary\\Transformation\\AssistColorBlind' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Color/AssistColorBlind.php',
    'Cloudinary\\Transformation\\AudioCodec' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/Audio/AudioCodec.php',
    'Cloudinary\\Transformation\\AudioFormatInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/AudioFormatInterface.php',
    'Cloudinary\\Transformation\\AudioFormatTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/AudioFormatTrait.php',
    'Cloudinary\\Transformation\\AudioFrequency' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/Audio/AudioFrequency.php',
    'Cloudinary\\Transformation\\AudioOverlay' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/AudioOverlay.php',
    'Cloudinary\\Transformation\\AudioQualifierTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/Audio/AudioQualifierTrait.php',
    'Cloudinary\\Transformation\\AudioSource' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/AudioSource.php',
    'Cloudinary\\Transformation\\AudioSourceQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/AudioSourceQualifier.php',
    'Cloudinary\\Transformation\\AudioSourceTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/AudioSourceTrait.php',
    'Cloudinary\\Transformation\\AutoBackground' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Background/AutoBackground.php',
    'Cloudinary\\Transformation\\AutoBackgroundMode' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Background/AutoBackgroundMode.php',
    'Cloudinary\\Transformation\\AutoBackgroundTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Background/AutoBackgroundTrait.php',
    'Cloudinary\\Transformation\\AutoFocus' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/AutoFocus.php',
    'Cloudinary\\Transformation\\AutoGradientBackground' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Background/AutoGradientBackground.php',
    'Cloudinary\\Transformation\\AutoGradientBackgroundTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Background/AutoGradientBackgroundTrait.php',
    'Cloudinary\\Transformation\\AutoGravity' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/AutoGravity.php',
    'Cloudinary\\Transformation\\AutoGravityBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/AutoGravityBuilderTrait.php',
    'Cloudinary\\Transformation\\AutoGravityObject' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/AutoGravityObject.php',
    'Cloudinary\\Transformation\\Background' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Background/Background.php',
    'Cloudinary\\Transformation\\BackgroundColorTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Background/BackgroundColorTrait.php',
    'Cloudinary\\Transformation\\BackgroundQualifierTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Background/BackgroundQualifierTrait.php',
    'Cloudinary\\Transformation\\BackgroundRemoval' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/BackgroundRemoval.php',
    'Cloudinary\\Transformation\\BackgroundTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Background/BackgroundTrait.php',
    'Cloudinary\\Transformation\\BaseAction' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/BaseAction.php',
    'Cloudinary\\Transformation\\BaseArgument' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/BaseArgument.php',
    'Cloudinary\\Transformation\\BaseComponent' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/BaseComponent.php',
    'Cloudinary\\Transformation\\BaseOffsetQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Timeline/BaseOffsetQualifier.php',
    'Cloudinary\\Transformation\\BasePageAction' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/BasePageAction.php',
    'Cloudinary\\Transformation\\BasePosition' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/BasePosition.php',
    'Cloudinary\\Transformation\\BasePositionalSourceContainer' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/BasePositionalSourceContainer.php',
    'Cloudinary\\Transformation\\BaseResizeAction' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/BaseResizeAction.php',
    'Cloudinary\\Transformation\\BaseSource' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/BaseSource.php',
    'Cloudinary\\Transformation\\BaseSourceContainer' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/BaseSourceContainer.php',
    'Cloudinary\\Transformation\\BaseSourceQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/BaseSourceQualifier.php',
    'Cloudinary\\Transformation\\BitRate' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/BitRate.php',
    'Cloudinary\\Transformation\\BlendEffectAction' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/BlendEffectAction.php',
    'Cloudinary\\Transformation\\BlendEffectQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/BlendEffectQualifier.php',
    'Cloudinary\\Transformation\\BlendMode' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/BlendMode.php',
    'Cloudinary\\Transformation\\Blur' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/Blur.php',
    'Cloudinary\\Transformation\\BlurredBackground' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Background/BlurredBackground.php',
    'Cloudinary\\Transformation\\Border' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Border/Border.php',
    'Cloudinary\\Transformation\\BorderQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Border/BorderQualifier.php',
    'Cloudinary\\Transformation\\BorderQualifierTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Border/BorderQualifierTrait.php',
    'Cloudinary\\Transformation\\BorderStyleTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Border/BorderStyleTrait.php',
    'Cloudinary\\Transformation\\BorderValue' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Border/BorderValue.php',
    'Cloudinary\\Transformation\\Cartoonify' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/Cartoonify.php',
    'Cloudinary\\Transformation\\ChromaSubSampling' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Quality/ChromaSubSampling.php',
    'Cloudinary\\Transformation\\ClippingPath' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/ClippingPath.php',
    'Cloudinary\\Transformation\\Codec\\VideoCodecLevel' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/Codec/VideoCodecLevel.php',
    'Cloudinary\\Transformation\\Codec\\VideoCodecProfile' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/Codec/VideoCodecProfile.php',
    'Cloudinary\\Transformation\\ColorEffect' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Color/ColorEffect.php',
    'Cloudinary\\Transformation\\ColorQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Color/ColorQualifier.php',
    'Cloudinary\\Transformation\\ColorQualifierTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Color/ColorQualifierTrait.php',
    'Cloudinary\\Transformation\\ColorSpace' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/ColorSpace/ColorSpace.php',
    'Cloudinary\\Transformation\\ColorSpaceQualifierTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/ColorSpace/ColorSpaceQualifierTrait.php',
    'Cloudinary\\Transformation\\ColorToReplaceTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Color/ColorToReplaceTrait.php',
    'Cloudinary\\Transformation\\ColorTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Color/ColorTrait.php',
    'Cloudinary\\Transformation\\ColoredEffectAction' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Color/ColoredEffectAction.php',
    'Cloudinary\\Transformation\\Colorize' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Color/Colorize.php',
    'Cloudinary\\Transformation\\CommonAdjustmentTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Adjustment/CommonAdjustmentTrait.php',
    'Cloudinary\\Transformation\\CommonEffectTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/CommonEffectTrait.php',
    'Cloudinary\\Transformation\\CommonFlag' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/CommonFlag.php',
    'Cloudinary\\Transformation\\CommonFlagInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/CommonFlagInterface.php',
    'Cloudinary\\Transformation\\CommonFlagTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/CommonFlagTrait.php',
    'Cloudinary\\Transformation\\CommonTransformation' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/CommonTransformation.php',
    'Cloudinary\\Transformation\\CommonTransformationFlagTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/CommonTransformationFlagTrait.php',
    'Cloudinary\\Transformation\\CommonTransformationInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/CommonTransformationInterface.php',
    'Cloudinary\\Transformation\\CommonTransformationTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/CommonTransformationTrait.php',
    'Cloudinary\\Transformation\\Compass' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/Compass.php',
    'Cloudinary\\Transformation\\CompassGravity' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/CompassGravity.php',
    'Cloudinary\\Transformation\\CompassGravityBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/CompassGravityBuilderTrait.php',
    'Cloudinary\\Transformation\\CompassGravityTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/CompassGravityTrait.php',
    'Cloudinary\\Transformation\\CompassPosition' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/CompassPosition.php',
    'Cloudinary\\Transformation\\CompassPositionTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/CompassPositionTrait.php',
    'Cloudinary\\Transformation\\ComponentInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/ComponentInterface.php',
    'Cloudinary\\Transformation\\Concatenate' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Edit/Concatenate.php',
    'Cloudinary\\Transformation\\ConditionQualifierTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Condition/ConditionQualifierTrait.php',
    'Cloudinary\\Transformation\\Conditional' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Condition/Conditional.php',
    'Cloudinary\\Transformation\\CornerRadius' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/CornerRadius.php',
    'Cloudinary\\Transformation\\CornerRadiusQualifierTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/CornerRadiusQualifierTrait.php',
    'Cloudinary\\Transformation\\CornerRadiusTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/CornerRadiusTrait.php',
    'Cloudinary\\Transformation\\Corners' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/Corners.php',
    'Cloudinary\\Transformation\\CornersTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/CornersTrait.php',
    'Cloudinary\\Transformation\\Crop' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Crop/Crop.php',
    'Cloudinary\\Transformation\\CropMode' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Parameter/CropMode.php',
    'Cloudinary\\Transformation\\CropModeTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Parameter/CropModeTrait.php',
    'Cloudinary\\Transformation\\CropPad' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Crop/CropPad.php',
    'Cloudinary\\Transformation\\CropPadTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Crop/CropPadTrait.php',
    'Cloudinary\\Transformation\\CropTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Crop/CropTrait.php',
    'Cloudinary\\Transformation\\CustomFunction' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/CustomFunction/CustomFunction.php',
    'Cloudinary\\Transformation\\CustomFunctionQualifierTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/CustomFunction/CustomFunctionQualifierTrait.php',
    'Cloudinary\\Transformation\\CustomFunctionTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/CustomFunction/CustomFunctionTrait.php',
    'Cloudinary\\Transformation\\CustomFunctionValue' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/CustomFunction/CustomFunctionValue.php',
    'Cloudinary\\Transformation\\CutByImage' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/CutByImage.php',
    'Cloudinary\\Transformation\\CutOut' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/CutOut.php',
    'Cloudinary\\Transformation\\DefaultImage' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Image/Qualifier/DefaultImage.php',
    'Cloudinary\\Transformation\\Delay' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Image/Qualifier/Delay.php',
    'Cloudinary\\Transformation\\Delivery' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Delivery.php',
    'Cloudinary\\Transformation\\DeliveryBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/DeliveryBuilderTrait.php',
    'Cloudinary\\Transformation\\Density' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Image/Qualifier/Density.php',
    'Cloudinary\\Transformation\\Deshake' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Appearance/Deshake.php',
    'Cloudinary\\Transformation\\DetectMultipleTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Generative/DetectMultipleTrait.php',
    'Cloudinary\\Transformation\\DimensionsQualifierTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Dimensions/DimensionsQualifierTrait.php',
    'Cloudinary\\Transformation\\Distort' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/Distort.php',
    'Cloudinary\\Transformation\\Dither' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/Dither.php',
    'Cloudinary\\Transformation\\DropShadow' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/DropShadow.php',
    'Cloudinary\\Transformation\\DropShadowQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/DropShadowQualifier.php',
    'Cloudinary\\Transformation\\Duration' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Timeline/Duration.php',
    'Cloudinary\\Transformation\\DurationEffectAction' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/DurationEffectAction.php',
    'Cloudinary\\Transformation\\DurationEffectQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/DurationEffectQualifier.php',
    'Cloudinary\\Transformation\\Effect' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Effect.php',
    'Cloudinary\\Transformation\\EffectAction' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/EffectAction.php',
    'Cloudinary\\Transformation\\EffectActionTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/EffectActionTrait.php',
    'Cloudinary\\Transformation\\EffectActionTypeTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Argument/EffectActionTypeTrait.php',
    'Cloudinary\\Transformation\\EffectMode' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Argument/EffectMode.php',
    'Cloudinary\\Transformation\\EffectModeTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Argument/EffectModeTrait.php',
    'Cloudinary\\Transformation\\EffectName' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Argument/EffectName.php',
    'Cloudinary\\Transformation\\EffectQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/EffectQualifier.php',
    'Cloudinary\\Transformation\\EffectQualifierTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/EffectQualifierTrait.php',
    'Cloudinary\\Transformation\\EffectRange' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Argument/EffectRange.php',
    'Cloudinary\\Transformation\\EffectType' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Argument/EffectType.php',
    'Cloudinary\\Transformation\\EffectTypeTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Argument/EffectTypeTrait.php',
    'Cloudinary\\Transformation\\ElectronicObjectGravityBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/ElectronicObjectGravityBuilderTrait.php',
    'Cloudinary\\Transformation\\ElectronicObjectGravityInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/ElectronicObjectGravityInterface.php',
    'Cloudinary\\Transformation\\EndIfCondition' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Condition/EndIfCondition.php',
    'Cloudinary\\Transformation\\EndOffset' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Timeline/EndOffset.php',
    'Cloudinary\\Transformation\\ExpressionQualifierMultiValue' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/ExpressionQualifierMultiValue.php',
    'Cloudinary\\Transformation\\Expression\\ArithmeticOperator' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operator/ArithmeticOperator.php',
    'Cloudinary\\Transformation\\Expression\\ArithmeticOperatorBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operator/ArithmeticOperatorBuilderTrait.php',
    'Cloudinary\\Transformation\\Expression\\BaseExpression' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/BaseExpression.php',
    'Cloudinary\\Transformation\\Expression\\BaseExpressionComponent' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/BaseExpressionComponent.php',
    'Cloudinary\\Transformation\\Expression\\BaseOperator' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operator/BaseOperator.php',
    'Cloudinary\\Transformation\\Expression\\Expression' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Expression.php',
    'Cloudinary\\Transformation\\Expression\\ExpressionComponent' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/ExpressionComponent.php',
    'Cloudinary\\Transformation\\Expression\\ExpressionOperator' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/ExpressionOperator.php',
    'Cloudinary\\Transformation\\Expression\\ExpressionTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/ExpressionTrait.php',
    'Cloudinary\\Transformation\\Expression\\ExpressionUtils' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/ExpressionUtils.php',
    'Cloudinary\\Transformation\\Expression\\LogicalOperator' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operator/LogicalOperator.php',
    'Cloudinary\\Transformation\\Expression\\LogicalOperatorBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operator/LogicalOperatorBuilderTrait.php',
    'Cloudinary\\Transformation\\Expression\\Operator' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operator/Operator.php',
    'Cloudinary\\Transformation\\Expression\\PVar' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operand/Variable/Predefined/PVar.php',
    'Cloudinary\\Transformation\\Expression\\PredefinedVariableBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operand/Variable/Predefined/PredefinedVariableBuilderTrait.php',
    'Cloudinary\\Transformation\\Expression\\PredefinedVariableTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operand/Variable/Predefined/PredefinedVariableTrait.php',
    'Cloudinary\\Transformation\\Expression\\RelationalOperator' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operator/RelationalOperator.php',
    'Cloudinary\\Transformation\\Expression\\RelationalOperatorBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operator/RelationalOperatorBuilderTrait.php',
    'Cloudinary\\Transformation\\Expression\\StringRelationalOperator' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operator/StringRelationalOperator.php',
    'Cloudinary\\Transformation\\Expression\\StringRelationalOperatorBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operator/StringRelationalOperatorBuilderTrait.php',
    'Cloudinary\\Transformation\\Expression\\UVal' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operand/Value/UVal.php',
    'Cloudinary\\Transformation\\Expression\\UVar' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operand/Variable/User/UVar.php',
    'Cloudinary\\Transformation\\Expression\\UserVariableBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operand/Variable/User/UserVariableBuilderTrait.php',
    'Cloudinary\\Transformation\\Expression\\ValueBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Expression/Operand/Value/ValueBuilderTrait.php',
    'Cloudinary\\Transformation\\Extract' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/Extract.php',
    'Cloudinary\\Transformation\\FetchImageSource' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/FetchImageSource.php',
    'Cloudinary\\Transformation\\FetchSourceQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/FetchSourceQualifier.php',
    'Cloudinary\\Transformation\\FetchVideoSource' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/FetchVideoSource.php',
    'Cloudinary\\Transformation\\Fill' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Fill/Fill.php',
    'Cloudinary\\Transformation\\FillLight' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Adjustment/FillLight.php',
    'Cloudinary\\Transformation\\FillPad' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Fill/FillPad.php',
    'Cloudinary\\Transformation\\FillPadTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Fill/FillPadTrait.php',
    'Cloudinary\\Transformation\\FillTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Fill/FillTrait.php',
    'Cloudinary\\Transformation\\Flag' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/Flag.php',
    'Cloudinary\\Transformation\\FlagQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/FlagQualifier.php',
    'Cloudinary\\Transformation\\FlagQualifierTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/FlagQualifierTrait.php',
    'Cloudinary\\Transformation\\FocalGravity' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/FocalGravity.php',
    'Cloudinary\\Transformation\\FocalGravityBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/FocalGravityBuilderTrait.php',
    'Cloudinary\\Transformation\\FocalGravityInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/FocalGravityInterface.php',
    'Cloudinary\\Transformation\\FocalGravityTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/FocalGravityTrait.php',
    'Cloudinary\\Transformation\\FocalPosition' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/FocalPosition.php',
    'Cloudinary\\Transformation\\FocusOn' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/FocusOn.php',
    'Cloudinary\\Transformation\\FoodObjectGravityBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/FoodObjectGravityBuilderTrait.php',
    'Cloudinary\\Transformation\\FoodObjectGravityInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/FoodObjectGravityInterface.php',
    'Cloudinary\\Transformation\\ForegroundObject' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Effect/ForegroundObject.php',
    'Cloudinary\\Transformation\\ForegroundObjectTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Effect/ForegroundObjectTrait.php',
    'Cloudinary\\Transformation\\Format' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/Format.php',
    'Cloudinary\\Transformation\\FormatInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/FormatInterface.php',
    'Cloudinary\\Transformation\\FormatQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/FormatQualifier.php',
    'Cloudinary\\Transformation\\FormatQualifierTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/FormatQualifierTrait.php',
    'Cloudinary\\Transformation\\FormatTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/FormatTrait.php',
    'Cloudinary\\Transformation\\Fps' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/Fps.php',
    'Cloudinary\\Transformation\\Frame' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/Frame.php',
    'Cloudinary\\Transformation\\FullListExpressionQualifierMultiValue' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/FullListExpressionQualifierMultiValue.php',
    'Cloudinary\\Transformation\\FullListQualifierMultiValue' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/FullListQualifierMultiValue.php',
    'Cloudinary\\Transformation\\FurnitureObjectGravityBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/FurnitureObjectGravityBuilderTrait.php',
    'Cloudinary\\Transformation\\FurnitureObjectGravityInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/FurnitureObjectGravityInterface.php',
    'Cloudinary\\Transformation\\GenerativeEffect' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Generative/GenerativeEffect.php',
    'Cloudinary\\Transformation\\GenerativeEffectAction' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Generative/GenerativeEffectAction.php',
    'Cloudinary\\Transformation\\GenerativeEffectTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Generative/GenerativeEffectTrait.php',
    'Cloudinary\\Transformation\\GenerativeFillBackground' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Background/GenerativeFillBackground.php',
    'Cloudinary\\Transformation\\GenerativeRecolor' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Generative/GenerativeRecolor.php',
    'Cloudinary\\Transformation\\GenerativeRemove' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Generative/GenerativeRemove.php',
    'Cloudinary\\Transformation\\GenerativeReplace' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Generative/GenerativeReplace.php',
    'Cloudinary\\Transformation\\GenericResize' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Generic/GenericResize.php',
    'Cloudinary\\Transformation\\GenericResizeTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Generic/GenericResizeTrait.php',
    'Cloudinary\\Transformation\\GradientFade' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/GradientFade.php',
    'Cloudinary\\Transformation\\GradientFadeQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/GradientFadeQualifier.php',
    'Cloudinary\\Transformation\\Gravity' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/Gravity.php',
    'Cloudinary\\Transformation\\GravityQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/GravityQualifier.php',
    'Cloudinary\\Transformation\\GravityQualifierTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/GravityQualifierTrait.php',
    'Cloudinary\\Transformation\\GravityTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/GravityTrait.php',
    'Cloudinary\\Transformation\\IfCondition' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Condition/IfCondition.php',
    'Cloudinary\\Transformation\\IfElse' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Condition/IfElse.php',
    'Cloudinary\\Transformation\\ImageAdjustmentTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Adjustment/ImageAdjustmentTrait.php',
    'Cloudinary\\Transformation\\ImageColorEffectTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Color/ImageColorEffectTrait.php',
    'Cloudinary\\Transformation\\ImageEffect' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/ImageEffect.php',
    'Cloudinary\\Transformation\\ImageEffectTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/ImageEffectTrait.php',
    'Cloudinary\\Transformation\\ImageFlag' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/ImageFlag.php',
    'Cloudinary\\Transformation\\ImageFlagInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/ImageFlagInterface.php',
    'Cloudinary\\Transformation\\ImageFlagTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/ImageFlagTrait.php',
    'Cloudinary\\Transformation\\ImageFormatInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/ImageFormatInterface.php',
    'Cloudinary\\Transformation\\ImageFormatTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/ImageFormatTrait.php',
    'Cloudinary\\Transformation\\ImageOverlay' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/ImageOverlay.php',
    'Cloudinary\\Transformation\\ImagePixelEffectTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/ImagePixelEffectTrait.php',
    'Cloudinary\\Transformation\\ImageQualifierTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Image/Qualifier/ImageQualifierTrait.php',
    'Cloudinary\\Transformation\\ImageQualifierTransformationTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Image/Qualifier/ImageQualifierTransformationTrait.php',
    'Cloudinary\\Transformation\\ImageSource' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/ImageSource.php',
    'Cloudinary\\Transformation\\ImageSourceQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/ImageSourceQualifier.php',
    'Cloudinary\\Transformation\\ImageSourceTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/ImageSourceTrait.php',
    'Cloudinary\\Transformation\\ImageSpecificTransformationTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Image/ImageSpecificTransformationTrait.php',
    'Cloudinary\\Transformation\\ImageTransformation' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Image/ImageTransformation.php',
    'Cloudinary\\Transformation\\ImageTransformationFlagTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/ImageTransformationFlagTrait.php',
    'Cloudinary\\Transformation\\ImageTransformationInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Image/ImageTransformationInterface.php',
    'Cloudinary\\Transformation\\ImageTransformationTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Image/ImageTransformationTrait.php',
    'Cloudinary\\Transformation\\Imagga' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Addon/Imagga.php',
    'Cloudinary\\Transformation\\ImaggaTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Addon/ImaggaTrait.php',
    'Cloudinary\\Transformation\\Improve' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Adjustment/Improve.php',
    'Cloudinary\\Transformation\\ImproveMode' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Adjustment/ImproveMode.php',
    'Cloudinary\\Transformation\\IndoorObjectGravityBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/IndoorObjectGravityBuilderTrait.php',
    'Cloudinary\\Transformation\\IndoorObjectGravityInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/IndoorObjectGravityInterface.php',
    'Cloudinary\\Transformation\\JpegMini' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Quality/JpegMini.php',
    'Cloudinary\\Transformation\\KeyframeInterval' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/KeyframeInterval.php',
    'Cloudinary\\Transformation\\KitchenObjectGravityBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/KitchenObjectGravityBuilderTrait.php',
    'Cloudinary\\Transformation\\KitchenObjectGravityInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/KitchenObjectGravityInterface.php',
    'Cloudinary\\Transformation\\LayerFlag' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/LayerFlag.php',
    'Cloudinary\\Transformation\\LayerFlagInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/LayerFlagInterface.php',
    'Cloudinary\\Transformation\\LayerFlagTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/LayerFlagTrait.php',
    'Cloudinary\\Transformation\\LayerName' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/LayerName.php',
    'Cloudinary\\Transformation\\LayerQualifierFactory' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/LayerQualifierFactory.php',
    'Cloudinary\\Transformation\\LayerQualifierTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/LayerQualifierTrait.php',
    'Cloudinary\\Transformation\\LayerSourceTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/LayerSourceTrait.php',
    'Cloudinary\\Transformation\\LayerStackPosition' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/LayerStackPosition.php',
    'Cloudinary\\Transformation\\LayeredImageTransformationTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Image/LayeredImageTransformationTrait.php',
    'Cloudinary\\Transformation\\LevelEffectAction' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/LevelEffectAction.php',
    'Cloudinary\\Transformation\\LevelEffectQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/LevelEffectQualifier.php',
    'Cloudinary\\Transformation\\LightroomEffect' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Addon/Lightroom/LightroomEffect.php',
    'Cloudinary\\Transformation\\LightroomEffectInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Addon/Lightroom/LightroomEffectInterface.php',
    'Cloudinary\\Transformation\\LightroomEffectQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Addon/Lightroom/LightroomEffectQualifier.php',
    'Cloudinary\\Transformation\\LightroomEffectTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Addon/Lightroom/LightroomEffectTrait.php',
    'Cloudinary\\Transformation\\LimitedEffectQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/LimitedEffectQualifier.php',
    'Cloudinary\\Transformation\\LiquidRescaling' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/LiquidRescaling.php',
    'Cloudinary\\Transformation\\ListEffectQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/ListEffectQualifier.php',
    'Cloudinary\\Transformation\\ListExpressionQualifierMultiValue' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/ListExpressionQualifierMultiValue.php',
    'Cloudinary\\Transformation\\ListQualifierMultiValue' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/ListQualifierMultiValue.php',
    'Cloudinary\\Transformation\\Loop' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Playback/Loop.php',
    'Cloudinary\\Transformation\\LutLayer' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/LutLayer.php',
    'Cloudinary\\Transformation\\LutSourceQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/LutSourceQualifier.php',
    'Cloudinary\\Transformation\\MakeTransparent' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/MakeTransparent.php',
    'Cloudinary\\Transformation\\MatrixValue' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/MatrixValue.php',
    'Cloudinary\\Transformation\\MediaOverlay' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/MediaOverlay.php',
    'Cloudinary\\Transformation\\MinMaxRange' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Argument/Range/MinMaxRange.php',
    'Cloudinary\\Transformation\\MiscEffect' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/MiscEffect.php',
    'Cloudinary\\Transformation\\MiscEffectTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/MiscEffectTrait.php',
    'Cloudinary\\Transformation\\NamedTransformation' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Named/NamedTransformation.php',
    'Cloudinary\\Transformation\\NamedTransformationQualifierTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Named/NamedTransformationQualifierTrait.php',
    'Cloudinary\\Transformation\\ObjectGravity' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/ObjectGravity.php',
    'Cloudinary\\Transformation\\ObjectGravityBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/ObjectGravityBuilderTrait.php',
    'Cloudinary\\Transformation\\ObjectGravityInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/ObjectGravityInterface.php',
    'Cloudinary\\Transformation\\ObjectGravityPriorityBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/ObjectGravityPriorityBuilderTrait.php',
    'Cloudinary\\Transformation\\ObjectGravityPriorityInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/ObjectGravityPriorityInterface.php',
    'Cloudinary\\Transformation\\ObjectGravityTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/ObjectGravityTrait.php',
    'Cloudinary\\Transformation\\Offset' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Offset.php',
    'Cloudinary\\Transformation\\OffsetTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/OffsetTrait.php',
    'Cloudinary\\Transformation\\Opacity' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Adjustment/Opacity.php',
    'Cloudinary\\Transformation\\OpacityQualifierTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Adjustment/OpacityQualifierTrait.php',
    'Cloudinary\\Transformation\\OutdoorObjectGravityBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/OutdoorObjectGravityBuilderTrait.php',
    'Cloudinary\\Transformation\\OutdoorObjectGravityInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/OutdoorObjectGravityInterface.php',
    'Cloudinary\\Transformation\\Outline' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/Outline.php',
    'Cloudinary\\Transformation\\OutlineMode' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/OutlineMode.php',
    'Cloudinary\\Transformation\\OutlineQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/OutlineQualifier.php',
    'Cloudinary\\Transformation\\Overlay' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/Overlay.php',
    'Cloudinary\\Transformation\\Pad' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Pad/Pad.php',
    'Cloudinary\\Transformation\\PadTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Pad/PadTrait.php',
    'Cloudinary\\Transformation\\Page' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/Page.php',
    'Cloudinary\\Transformation\\PageAllTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PageAllTrait.php',
    'Cloudinary\\Transformation\\PageIndexTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PageIndexTrait.php',
    'Cloudinary\\Transformation\\PageLayerNameTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PageLayerNameTrait.php',
    'Cloudinary\\Transformation\\PageLayerNamesTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PageLayerNamesTrait.php',
    'Cloudinary\\Transformation\\PageNameTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PageNameTrait.php',
    'Cloudinary\\Transformation\\PageNamesTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PageNamesTrait.php',
    'Cloudinary\\Transformation\\PageNumberTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PageNumberTrait.php',
    'Cloudinary\\Transformation\\PageQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PageQualifier.php',
    'Cloudinary\\Transformation\\PageQualifierTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PageQualifierTrait.php',
    'Cloudinary\\Transformation\\PageRangeTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PageRangeTrait.php',
    'Cloudinary\\Transformation\\PageValue' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PageValue.php',
    'Cloudinary\\Transformation\\Palette' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Color/Palette.php',
    'Cloudinary\\Transformation\\PersonObjectGravityBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/PersonObjectGravityBuilderTrait.php',
    'Cloudinary\\Transformation\\PersonObjectGravityInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/PersonObjectGravityInterface.php',
    'Cloudinary\\Transformation\\PixelEffect' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/PixelEffect.php',
    'Cloudinary\\Transformation\\PixelEffectRegionTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/PixelEffectRegionTrait.php',
    'Cloudinary\\Transformation\\PixelEffectTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/PixelEffectTrait.php',
    'Cloudinary\\Transformation\\Pixelate' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/Pixelate.php',
    'Cloudinary\\Transformation\\PlaybackEffect' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Playback/PlaybackEffect.php',
    'Cloudinary\\Transformation\\PlaybackEffectTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Playback/PlaybackEffectTrait.php',
    'Cloudinary\\Transformation\\Point' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Point.php',
    'Cloudinary\\Transformation\\PointTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/PointTrait.php',
    'Cloudinary\\Transformation\\Position' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Position.php',
    'Cloudinary\\Transformation\\PositionFlagTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/PositionFlagTrait.php',
    'Cloudinary\\Transformation\\PositioningQualifierTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/PositioningQualifierTrait.php',
    'Cloudinary\\Transformation\\PositiveFloatValue' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Misc/PositiveFloatValue.php',
    'Cloudinary\\Transformation\\Prefix' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Image/Qualifier/Prefix.php',
    'Cloudinary\\Transformation\\Preview' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Playback/Preview.php',
    'Cloudinary\\Transformation\\Progressive' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/Progressive.php',
    'Cloudinary\\Transformation\\PromptTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Generative/PromptTrait.php',
    'Cloudinary\\Transformation\\PsdLayer' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PsdLayer.php',
    'Cloudinary\\Transformation\\PsdTools' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/PsdTools.php',
    'Cloudinary\\Transformation\\Qualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Qualifier.php',
    'Cloudinary\\Transformation\\QualifierMultiValue' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/QualifierMultiValue.php',
    'Cloudinary\\Transformation\\Qualifier\\BaseExpressionQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/BaseExpressionQualifier.php',
    'Cloudinary\\Transformation\\Qualifier\\BaseQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/BaseQualifier.php',
    'Cloudinary\\Transformation\\Qualifier\\Dimensions\\Dimensions' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Dimensions/Dimensions.php',
    'Cloudinary\\Transformation\\Qualifier\\Dimensions\\DimensionsTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Dimensions/DimensionsTrait.php',
    'Cloudinary\\Transformation\\Qualifier\\Dimensions\\Dpr' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Dpr.php',
    'Cloudinary\\Transformation\\Qualifier\\Dimensions\\Height' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Dimensions/Height.php',
    'Cloudinary\\Transformation\\Qualifier\\Dimensions\\Width' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Dimensions/Width.php',
    'Cloudinary\\Transformation\\Qualifier\\GenericQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/GenericQualifier.php',
    'Cloudinary\\Transformation\\Qualifier\\Misc\\BreakpointsJson' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Misc/BreakpointsJson.php',
    'Cloudinary\\Transformation\\Qualifier\\Value\\ColorValueTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Color/ColorValueTrait.php',
    'Cloudinary\\Transformation\\QualifiersAction' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifiersAction.php',
    'Cloudinary\\Transformation\\Quality' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Quality/Quality.php',
    'Cloudinary\\Transformation\\QualityBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Quality/QualityBuilderTrait.php',
    'Cloudinary\\Transformation\\QualityQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Quality/QualityQualifier.php',
    'Cloudinary\\Transformation\\QualityQualifierTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Quality/QualityQualifierTrait.php',
    'Cloudinary\\Transformation\\QualityTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Quality/QualityTrait.php',
    'Cloudinary\\Transformation\\RecolorQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Adjustment/RecolorQualifier.php',
    'Cloudinary\\Transformation\\RectangleRegion' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Canvas/RectangleRegion.php',
    'Cloudinary\\Transformation\\Region' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Region.php',
    'Cloudinary\\Transformation\\RegionEffectAction' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/RegionEffectAction.php',
    'Cloudinary\\Transformation\\RegionEffectTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/RegionEffectTrait.php',
    'Cloudinary\\Transformation\\RegionTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/RegionTrait.php',
    'Cloudinary\\Transformation\\RemoteSourceValue' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/RemoteSourceValue.php',
    'Cloudinary\\Transformation\\RemoveBackground' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Pixel/RemoveBackground.php',
    'Cloudinary\\Transformation\\ReplaceColor' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Adjustment/ReplaceColor.php',
    'Cloudinary\\Transformation\\Reshape' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/Reshape.php',
    'Cloudinary\\Transformation\\ReshapeQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/ReshapeQualifier.php',
    'Cloudinary\\Transformation\\ReshapeTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/ReshapeTrait.php',
    'Cloudinary\\Transformation\\Resize' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Resize.php',
    'Cloudinary\\Transformation\\ResizeFactory' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/ResizeFactory.php',
    'Cloudinary\\Transformation\\ResizeMode' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Parameter/ResizeMode.php',
    'Cloudinary\\Transformation\\ResizeModeTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Parameter/ResizeModeTrait.php',
    'Cloudinary\\Transformation\\ResizeQualifierTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Parameter/ResizeQualifierTrait.php',
    'Cloudinary\\Transformation\\ResizeTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/ResizeTrait.php',
    'Cloudinary\\Transformation\\Rotate' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Rotation/Rotate.php',
    'Cloudinary\\Transformation\\RotationDegreeInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Rotation/RotationDegreeInterface.php',
    'Cloudinary\\Transformation\\RotationModeInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Rotation/RotationModeInterface.php',
    'Cloudinary\\Transformation\\RoundCorners' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/RoundCorners.php',
    'Cloudinary\\Transformation\\Scale' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Scale/Scale.php',
    'Cloudinary\\Transformation\\ScaleTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Scale/ScaleTrait.php',
    'Cloudinary\\Transformation\\Shadow' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/Shadow.php',
    'Cloudinary\\Transformation\\ShakeStrength' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Appearance/ShakeStrength.php',
    'Cloudinary\\Transformation\\Shear' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/Shear.php',
    'Cloudinary\\Transformation\\SimulateColorBlind' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Color/SimulateColorBlind.php',
    'Cloudinary\\Transformation\\SmartObject' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/SmartObject.php',
    'Cloudinary\\Transformation\\SmartObjectName' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/SmartObjectName.php',
    'Cloudinary\\Transformation\\SmartObjectQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Page/SmartObjectQualifier.php',
    'Cloudinary\\Transformation\\Source' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/Source.php',
    'Cloudinary\\Transformation\\SourceBasedEffectAction' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/SourceBasedEffectAction.php',
    'Cloudinary\\Transformation\\SourceValue' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/SourceValue.php',
    'Cloudinary\\Transformation\\SquareSizeEffectAction' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/SquareSizeEffectAction.php',
    'Cloudinary\\Transformation\\SquareSizeEffectQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/SquareSizeEffectQualifier.php',
    'Cloudinary\\Transformation\\StartOffset' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Timeline/StartOffset.php',
    'Cloudinary\\Transformation\\StreamingProfile' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/StreamingProfile.php',
    'Cloudinary\\Transformation\\StrengthEffectAction' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/StrengthEffectAction.php',
    'Cloudinary\\Transformation\\StrengthEffectQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/StrengthEffectQualifier.php',
    'Cloudinary\\Transformation\\StyleTransfer' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/StyleTransfer/StyleTransfer.php',
    'Cloudinary\\Transformation\\StyleTransferQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/StyleTransfer/StyleTransferQualifier.php',
    'Cloudinary\\Transformation\\SubtitlesSource' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/SubtitlesSource.php',
    'Cloudinary\\Transformation\\SubtitlesSourceQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/SubtitlesSourceQualifier.php',
    'Cloudinary\\Transformation\\TextColorTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Color/TextColorTrait.php',
    'Cloudinary\\Transformation\\TextFit' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Dimensions/TextFit.php',
    'Cloudinary\\Transformation\\TextFitTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Dimensions/TextFitTrait.php',
    'Cloudinary\\Transformation\\TextSource' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/TextSource.php',
    'Cloudinary\\Transformation\\TextSourceQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/TextSourceQualifier.php',
    'Cloudinary\\Transformation\\TextStyle' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/QualifierValue/Text/TextStyle.php',
    'Cloudinary\\Transformation\\ThemeEffect' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Appearance/ThemeEffect.php',
    'Cloudinary\\Transformation\\ThemeEffectTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Appearance/ThemeEffectTrait.php',
    'Cloudinary\\Transformation\\ThemeQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Appearance/ThemeQualifier.php',
    'Cloudinary\\Transformation\\ThresholdEffectAction' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/ThresholdEffectAction.php',
    'Cloudinary\\Transformation\\ThresholdEffectQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/ThresholdEffectQualifier.php',
    'Cloudinary\\Transformation\\Timeline' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Timeline/Timeline.php',
    'Cloudinary\\Transformation\\TimelineQualifierTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Timeline/TimelineQualifierTrait.php',
    'Cloudinary\\Transformation\\ToAnimatedAction' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/ToAnimated/ToAnimatedAction.php',
    'Cloudinary\\Transformation\\ToAnimatedActionTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/ToAnimated/ToAnimatedActionTrait.php',
    'Cloudinary\\Transformation\\ToleranceEffectAction' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/ToleranceEffectAction.php',
    'Cloudinary\\Transformation\\ToleranceEffectQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/ToleranceEffectQualifier.php',
    'Cloudinary\\Transformation\\Transcode' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/Transcode.php',
    'Cloudinary\\Transformation\\TranscodeTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/TranscodeTrait.php',
    'Cloudinary\\Transformation\\Transformation' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Transformation.php',
    'Cloudinary\\Transformation\\TransformationCustomFunctionTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/CustomFunction/TransformationCustomFunctionTrait.php',
    'Cloudinary\\Transformation\\TransformationDeliveryTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/TransformationDeliveryTrait.php',
    'Cloudinary\\Transformation\\TransformationResizeTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/TransformationResizeTrait.php',
    'Cloudinary\\Transformation\\TransformationTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/TransformationTrait.php',
    'Cloudinary\\Transformation\\Transition' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/Transition.php',
    'Cloudinary\\Transformation\\TrimEffect' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Shape/TrimEffect.php',
    'Cloudinary\\Transformation\\Underlay' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/Underlay.php',
    'Cloudinary\\Transformation\\ValueEffectQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/ValueEffectQualifier.php',
    'Cloudinary\\Transformation\\Variable\\Variable' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Variable/Variable.php',
    'Cloudinary\\Transformation\\Variable\\VariableValue' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Variable/VariableValue.php',
    'Cloudinary\\Transformation\\Vectorize' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/Vectorize.php',
    'Cloudinary\\Transformation\\VectorizeTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/VectorizeTrait.php',
    'Cloudinary\\Transformation\\VectorizeValue' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Misc/VectorizeValue.php',
    'Cloudinary\\Transformation\\VehicleObjectGravityBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/VehicleObjectGravityBuilderTrait.php',
    'Cloudinary\\Transformation\\VehicleObjectGravityInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Gravity/ObjectGravity/VehicleObjectGravityInterface.php',
    'Cloudinary\\Transformation\\VideoAppearanceEffectTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Appearance/VideoAppearanceEffectTrait.php',
    'Cloudinary\\Transformation\\VideoCodec' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/Codec/VideoCodec.php',
    'Cloudinary\\Transformation\\VideoCodecTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/Codec/VideoCodecTrait.php',
    'Cloudinary\\Transformation\\VideoEdit' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Edit/VideoEdit.php',
    'Cloudinary\\Transformation\\VideoEditBuilderTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Edit/VideoEditBuilderTrait.php',
    'Cloudinary\\Transformation\\VideoEffect' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/VideoEffect.php',
    'Cloudinary\\Transformation\\VideoEffectTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/VideoEffectTrait.php',
    'Cloudinary\\Transformation\\VideoFlag' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/VideoFlag.php',
    'Cloudinary\\Transformation\\VideoFlagInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/VideoFlagInterface.php',
    'Cloudinary\\Transformation\\VideoFlagTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/VideoFlagTrait.php',
    'Cloudinary\\Transformation\\VideoFormatInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/VideoFormatInterface.php',
    'Cloudinary\\Transformation\\VideoFormatTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Delivery/Format/VideoFormatTrait.php',
    'Cloudinary\\Transformation\\VideoOffset' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Qualifier/Timeline/VideoOffset.php',
    'Cloudinary\\Transformation\\VideoOverlay' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/VideoOverlay.php',
    'Cloudinary\\Transformation\\VideoQualifierTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/VideoQualifierTrait.php',
    'Cloudinary\\Transformation\\VideoSampling' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/Transcode/ToAnimated/VideoSampling.php',
    'Cloudinary\\Transformation\\VideoSource' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/VideoSource.php',
    'Cloudinary\\Transformation\\VideoSourceQualifier' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/VideoSourceQualifier.php',
    'Cloudinary\\Transformation\\VideoSourceTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Layer/VideoSourceTrait.php',
    'Cloudinary\\Transformation\\VideoSpecificTransformationTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/VideoSpecificTransformationTrait.php',
    'Cloudinary\\Transformation\\VideoTransformation' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/VideoTransformation.php',
    'Cloudinary\\Transformation\\VideoTransformationFlagTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Flag/VideoTransformationFlagTrait.php',
    'Cloudinary\\Transformation\\VideoTransformationInterface' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/VideoTransformationInterface.php',
    'Cloudinary\\Transformation\\VideoTransformationTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Video/VideoTransformationTrait.php',
    'Cloudinary\\Transformation\\ViesusCorrect' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Adjustment/Addon/ViesusCorrect.php',
    'Cloudinary\\Transformation\\Volume' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Playback/Volume.php',
    'Cloudinary\\Transformation\\WhiteBalance' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Addon/Lightroom/WhiteBalance.php',
    'Cloudinary\\Transformation\\X' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/X.php',
    'Cloudinary\\Transformation\\Xmp' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Addon/Lightroom/Xmp.php',
    'Cloudinary\\Transformation\\XmpSourceValue' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Effect/Addon/Lightroom/XmpSourceValue.php',
    'Cloudinary\\Transformation\\Y' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Positioning/Y.php',
    'Cloudinary\\Transformation\\Zoom' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Parameter/Zoom.php',
    'Cloudinary\\Transformation\\ZoomTrait' => $vendorDir . '/cloudinary/transformation-builder-sdk/src/Transformation/Resize/Parameter/ZoomTrait.php',
    'Cloudinary\\Utils' => $vendorDir . '/cloudinary/cloudinary_php/src/Utils/Utils.php',
    'Cloudinary\\Utils\\SignatureVerifier' => $vendorDir . '/cloudinary/cloudinary_php/src/Utils/SignatureVerifier.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'PhpToken' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/PhpToken.php',
    'Stringable' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Stringable.php',
    'UnhandledMatchError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/UnhandledMatchError.php',
    'ValueError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/ValueError.php',
);
