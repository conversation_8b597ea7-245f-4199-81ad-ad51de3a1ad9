<?php
/**
 * This file is part of the Cloudinary PHP package.
 *
 * (c) Cloudinary
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Cloudinary\Transformation;

/**
 * Interface FurnitureObjectGravityInterface
 *
 * @api
 */
interface FurnitureObjectGravityInterface
{
    //Furniture Category
    public const FURNITURE = 'furniture';
    public const CHAIR     = 'chair';
    public const SOFA  = 'sofa';
    public const POTTED_PLANT = 'pottedplant';
    public const BED          = 'bed';
    public const DINING_TABLE = 'diningtable';
    public const TOILET       = 'toilet';
}
