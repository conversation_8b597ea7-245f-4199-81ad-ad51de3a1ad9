# GetSendersListSenders

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | Id of the sender | 
**name** | **string** | From Name associated to the sender | 
**email** | **string** | From Email associated to the sender | 
**active** | **bool** | Status of sender (true&#x3D;activated, false&#x3D;deactivated) | 
**ips** | [**\Brevo\Client\Model\GetSendersListIps[]**](GetSendersListIps.md) | List of dedicated IP(s) available in the account. This data is displayed only for dedicated IPs | [optional] 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


