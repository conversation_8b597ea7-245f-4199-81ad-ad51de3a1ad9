# GetAccountRelay

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**enabled** | **bool** | Status of your transactional email Account (true&#x3D;Enabled, false&#x3D;Disabled) | 
**data** | [**\Brevo\Client\Model\GetAccountRelayData**](GetAccountRelayData.md) |  | 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


