<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0,viewport-fit=cover">
    <title>MIKO - Ready to Serve!</title>
    <meta name="theme-color" content="#ff5018">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="manifest" crossorigin="use-credentials" href="/manifest.json">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="MIKO">
    <link rel="apple-touch-icon" href="/icons/icon-180x180.png">
    <style>
        :root { --primary-accent: #ff5018; --secondary-accent: #FF7F50; --neutral-base: #F7F7F7; --text-dark-elements: #333333; --subtle-detail: #A7A7A7; --pure-white: #FFFFFF; }
        * { margin: 0; padding: 0; box-sizing: border-box; -webkit-tap-highlight-color: transparent; -webkit-touch-callout: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; }
        *:focus { outline: none; }
        html { touch-action: manipulation; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; scrollbar-width: none; -ms-overflow-style: none; overscroll-behavior: none; }
        html::-webkit-scrollbar { display: none; }
        body { font-family: 'Montserrat', sans-serif; background-color: var(--primary-accent); height: 100vh; width: 100vw; display: flex; justify-content: center; align-items: center; overflow: hidden; position: relative; touch-action: manipulation; -webkit-touch-callout: none; -webkit-user-select: none; -khtml-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; -webkit-tap-highlight-color: transparent; overscroll-behavior: none; -webkit-overflow-scrolling: touch; }
        .splash-screen-overlay { position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-image: url("data:image/svg+xml,%3Csvg width='200' height='200' viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='foodPattern' x='0' y='0' width='200' height='200' patternUnits='userSpaceOnUse'%3E%3Cg fill='none' stroke='white' stroke-width='1.5' opacity='0.2'%3E%3C!-- Tomato/Round Item --%3E%3Ccircle cx='40' cy='40' r='18'/%3E%3Cpath d='M40 22 L40 58 M22 40 L58 40'/%3E%3C!-- Chili Pepper 1 --%3E%3Cpath d='M160 30 Q175 15, 185 30 T190 50 T180 70 T160 60 Z'/%3E%3C!-- Cucumber Slice --%3E%3Ccircle cx='140' cy='150' r='15'/%3E%3Cpath d='M140 135 L140 165 M125 150 L155 150'/%3E%3C!-- Leaf --%3E%3Cpath d='M60 110 Q70 100, 80 110 T90 120 T80 130 T70 120 T60 110 Z'/%3E%3C!-- Chili Pepper 2 (rotated) --%3E%3Cg transform='translate(20, 100) rotate(90)'%3E%3Cpath d='M160 30 Q175 15, 185 30 T190 50 T180 70 T160 60 Z'/%3E%3C/g%3E%3C/g%3E%3C/pattern%3E%3C/defs%3E%3Crect x='0' y='0' width='100%25' height='100%25' fill='url(%23foodPattern)'/%3E%3C/svg%3E"); background-size: 200px 200px; background-repeat: repeat; z-index: -1; transition: opacity 0.5s ease-out; }
        .splash-container { width: 100vw; height: 100vh; max-width: none; background-color: transparent; box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1); position: relative; overflow: hidden; display: flex; flex-direction: column; align-items: center; justify-content: center; }
        .logo-container { width: 120px; height: 120px; background: var(--neutral-base); border-radius: 30px; margin-bottom: 0px; display: flex; justify-content: center; align-items: center; box-shadow: 0 10px 30px rgba(255, 107, 53, 0.3); animation: pulse 2s infinite; z-index: 2; opacity: 1; pointer-events: auto; }
        .logo { font-size: 70px; color: var(--primary-accent); }
        @keyframes pulse { 0% { transform: scale(1); } 50% { transform: scale(1.05); } 100% { transform: scale(1); } }
        .bottom-loading { position: fixed; bottom: 80px; left: 50%; transform: translateX(-50%); width: 70%; z-index: 10002; text-align: center; }
        .loading-text { color: white; font-size: 14px; font-weight: 500; margin-bottom: 12px; text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3); }
        .loading-bar-container { width: 100%; height: 3px; border-radius: 2px; overflow: hidden; background: transparent; }
        .loading-bar-fill { height: 100%; background: white; border-radius: 2px; width: 0%; transition: width 0.3s ease; box-shadow: 0 0 10px rgba(255, 255, 255, 0.5); }
    </style>
</head>
<body>
    <div class="splash-screen-overlay"></div>
    <div class="splash-container" id="splashScreen">
        <div class="logo-container" id="logoContainer">
            <i class="fas fa-utensils logo" id="logoIcon"></i>
        </div>
    </div>
    <!-- Bottom loading bar -->
    <div id="bottomLoading" class="bottom-loading">
        <div id="loadingText" class="loading-text">Please wait...</div>
        <div class="loading-bar-container">
            <div id="loadingBarFill" class="loading-bar-fill"></div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/service-worker.js')
                    .then(registration => {
                    })
                    .catch(error => {
                    });
            }

            const splashScreen = document.getElementById('splashScreen');
            const logoIcon = document.getElementById('logoIcon');

            if (!splashScreen) {
                if (!navigator.onLine) {
                    window.location.replace('/offline');
                } else {
                    window.location.replace('/home');
                }
                return;
            }

            const contentDisplayDuration = 2000;

            async function checkUserStatus() {
                try {
                    const response = await fetch('/login.php?path=/api/check-user-status', { method: 'POST', headers: { 'Content-Type': 'application/json' }, credentials: 'include' });
                    const data = await response.json();
                    completeBottomLoading(() => window.location.replace(data.success && (data.user_type === 'guest' || data.user_type === 'authenticated') ? '/home' : '/login'));
                } catch (error) { window.location.replace('/login'); }
            }

            function startBottomLoading() {
                const [text, bar] = [document.getElementById('loadingText'), document.getElementById('loadingBarFill')];
                text.textContent = 'Please wait...'; bar.style.width = '0%';
                window.loadingState = { text, bar, totalCategories: 0, loadedCategories: 0 };
            }

            function updateLoadingProgress(stage, current = 0, total = 0) {
                if (!window.loadingState) return;
                const { text, bar } = window.loadingState; let progress = 0;
                const stages = { fetching: ['Making special customization for you...', 5], categories: ['Updating to latest version...', 15], caching: [`Preparing your experience... (${current}/${window.loadingState.totalCategories})`, 15 + (current / window.loadingState.totalCategories) * 70], complete: ['Almost ready...', 90], ready: ['Ready!', 100] };
                if (stage === 'categories') window.loadingState.totalCategories = total;
                if (stage === 'caching') window.loadingState.loadedCategories = current;
                [text.textContent, progress] = stages[stage] || ['', 0];
                bar.style.width = `${Math.min(progress, 100)}%`;
            }

            function completeBottomLoading(callback) { updateLoadingProgress('ready'); setTimeout(callback, 300); }

            startBottomLoading();
            preloadAllCategories().then(() => checkUserStatus());
        });

        const categoryCache = new Map(); let preloadingInProgress = false;

        const preloadAllCategories = async () => {
            if (preloadingInProgress) return Promise.resolve();
            preloadingInProgress = true;
            return new Promise(async (resolve) => {
                try {
                    updateLoadingProgress('fetching');
                    const categoriesResponse = await fetch('/home.php?action=getCategories');
                    if (!categoriesResponse.ok) throw new Error('Failed to fetch categories');
                    const categories = await categoriesResponse.json();
                    updateLoadingProgress('categories', 0, categories.length);
                    const batchSize = 2; let successCount = 0;
                    for (let i = 0; i < categories.length; i += batchSize) {
                        const batch = categories.slice(i, i + batchSize);
                        const batchPromises = batch.map(async (category) => {
                            try {
                                const response = await fetch(`/home.php?action=getFoodItems&category=${encodeURIComponent(category.name)}`, { headers: { 'Cache-Control': 'max-age=600', 'X-Preload': 'true' } });
                                if (response.ok) {
                                    const foodItems = await response.json();
                                    if (foodItems && Array.isArray(foodItems)) {
                                        categoryCache.set(category.name, foodItems);
                                        foodItems.forEach(item => { if (item.image && item.image !== 'null') { const img = new Image(); img.src = item.image; } });
                                        return { success: true, category: category.name, count: foodItems.length };
                                    }
                                }
                                throw new Error(`HTTP ${response.status}`);
                            } catch (error) { return { success: false, category: category.name, error: error.message }; }
                        });
                        const batchResults = await Promise.allSettled(batchPromises);
                        successCount += batchResults.filter(r => r.status === 'fulfilled' && r.value.success).length;
                        updateLoadingProgress('caching', Math.min(i + batchSize, categories.length), categories.length);
                    }
                    updateLoadingProgress('complete');
                } catch (error) { } finally { preloadingInProgress = false; resolve(); }
            });
        };
    </script>
</body>
</html>