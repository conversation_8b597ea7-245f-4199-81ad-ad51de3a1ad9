document.addEventListener('DOMContentLoaded', () => {
    const urlParams = new URLSearchParams(window.location.search);
    const emailFromUrl = urlParams.get('email');
    if (emailFromUrl) { 
        sessionStorage.setItem('logged_in_email', emailFromUrl); 
        localStorage.setItem('logged-in', 'true');
        console.log('User email saved to sessionStorage from URL:', { email: emailFromUrl });
        console.log('Logged-in flag set in localStorage.');
        window.history.replaceState({}, document.title, window.location.pathname);
        window.location.href = 'home.html';
    } else {
        const isLoggedIn = localStorage.getItem('logged-in'); 
        const loggedInEmail = sessionStorage.getItem('logged_in_email'); 

        if (isLoggedIn === 'true' && loggedInEmail) { 
            window.location.href = 'home.html';
        } else {
            if (isLoggedIn === 'true' && !loggedInEmail) {
                 localStorage.removeItem('logged-in');

            }
        }
    }
});
