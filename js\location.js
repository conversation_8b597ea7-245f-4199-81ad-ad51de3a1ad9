// location.js

// --- CSS for Location Modals ---
// This CSS will be dynamically injected into the document head.
const locationModalStyles = `
    /* Bottom Modals (General) */
    .bottom-modal-overlay {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: flex-end;
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
    }

    .bottom-modal-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    .bottom-modal-content {
        background-color: var(--white);
        width: 100%;
        max-width: 800px;
        height: 50vh;
        border-top-left-radius: 20px;
        border-top-right-radius: 20px;
        box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.2);
        transform: translateY(100%);
        transition: transform 0.3s ease-in-out;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .bottom-modal-overlay.active .bottom-modal-content {
        transform: translateY(0);
    }

    .bottom-modal-body {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        position: relative;
    }

    /* Map Containers in Modals */
    #locationMapContainer {
        flex-grow: 1;
        height: 100%;
        width: 100%;
        min-height: 200px;
        position: relative;
    }

    .refresh-location-btn {
        position: absolute;
        bottom: 1rem;
        right: 1rem;
        background-color: var(--primary);
        color: var(--white);
        width: 40px;
        height: 40px;
        border-radius: 50%;
        padding: 0;
        border: none;
        font-size: 1.2rem;
        font-weight: 600;
        cursor: pointer;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        z-index: 1001;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: background-color 0.2s ease;
    }

    .refresh-location-btn:hover {
        background-color: var(--secondary);
    }

    /* Custom Leaflet Icon */
    .leaflet-marker-icon.custom-orange-person-icon {
        background-color: var(--secondary);
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: white;
        font-size: 1.2rem;
        width: 30px !important;
        height: 30px !important;
        margin-left: -15px !important;
        margin-top: -15px !important;
        border: 2px solid white;
        box-shadow: 0 2px 5px rgba(0,0,0,0.3);
    }

    /* Modal Buttons */
    .modal-buttons-container {
        display: flex;
        justify-content: space-around;
        padding: 1rem;
        background-color: var(--white);
        border-top: 1px solid var(--neutral);
        gap: 10px;
        flex-wrap: wrap;
    }

    .modal-button {
        flex: 1;
        min-width: 150px;
        padding: 0.8rem 1.2rem;
        border: none;
        border-radius: 10px;
        font-size: 0.9rem;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.2s ease, transform 0.2s ease;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .modal-button.primary {
        background-color: var(--primary);
        color: var(--white);
    }

    .modal-button.primary:hover {
        background-color: var(--secondary);
        transform: translateY(-2px);
    }

    .modal-button.secondary {
        background-color: var(--neutral);
        color: var(--text);
        border: 1px solid var(--text);
    }

    .modal-button.secondary:hover {
        background-color: #e0e0e0;
        transform: translateY(-2px);
    }

    /* New Address Modal */
    .new-address-modal-overlay {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: flex-end;
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
    }

    .new-address-modal-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    .new-address-modal-content {
        background-color: var(--white);
        width: 100%;
        max-width: 100%;
        height: 100vh;
        border-top-left-radius: 0;
        border-top-right-radius: 0;
        box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.2);
        transform: translateY(0);
        transition: transform 0.3s ease-in-out, height 0.3s ease-in-out;
        display: flex;
        flex-direction: column;
        padding: 0;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .new-address-modal-overlay.active .new-address-modal-content {
        transform: translateY(0);
    }

    .new-address-modal-content.modal-address-selected {
        height: 30rem;
    }

    .new-address-modal-content h2 {
        color: var(--primary);
        margin-bottom: 1rem;
        position: absolute;
        top: 1.5rem;
        left: 0;
        right: 0;
        z-index: 2;
        text-align: center;
        width: 100%;
        background-color: rgba(255, 255, 255, 0.8);
        padding: 0.5rem 0;
    }

    .new-address-modal-footer {
        margin-top: auto;
        padding: 1rem;
        border-top: 1px solid var(--neutral);
        display: flex;
        justify-content: center;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 2;
        background-color: rgba(255, 255, 255, 0.8);
    }

    .close-new-address-modal-btn {
        position: absolute;
        top: 3rem;
        left: 0.5rem;
        background-color: var(--primary);
        color: var(--white);
        width: 40px;
        height: 40px;
        border-radius: 50%;
        padding: 0;
        border: none;
        font-size: 1.2rem;
        font-weight: 600;
        cursor: pointer;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        z-index: 1001;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: background-color 0.2s ease;
    }

    .close-new-address-modal-btn:hover {
        background-color: var(--secondary);
    }

    .new-address-white-bottom-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 45vh;
        background-color: var(--white);
        z-index: 10;
        border-top-left-radius: 20px;
        border-top-right-radius: 20px;
        box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.2);
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        will-change: transform;
        overflow-y: auto;
        transition: height 0.3s ease-in-out;
    }

    .new-address-modal-content.modal-address-selected .new-address-white-bottom-overlay {
        height: 150px;
        justify-content: flex-start;
        overflow-y: hidden;
    }

    #newAddressMapContainer {
        flex-grow: 1;
        height: auto;
        width: 100%;
        min-height: 200px;
        position: relative;
        z-index: 1;
        opacity: 0.9;
    }

    .address-input-wrapper {
        width: 90%;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-top: 1rem;
        margin-bottom: 1rem;
    }

    .address-search-container {
        flex-grow: 1;
        display: flex;
        gap: 0.5rem;
        background-color: var(--white);
        border-radius: 100px;
        padding: 0.8rem 1.5rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        align-items: center;
        border: 1px solid var(--neutral);
        transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }

    .address-search-container:hover {
        border-color: var(--primary);
        box-shadow: 0 0 15px rgba(255, 107, 53, 0.4);
    }

    #searchModeContent {
        display: flex;
        align-items: center;
        flex-grow: 1;
        gap: 0.5rem;
    }

    .address-search-input {
        flex-grow: 1;
        border: none;
        background: transparent;
        padding: 0;
        font-size: 1rem;
        color: var(--text);
        outline: none;
    }

    .address-search-back-arrow {
        font-size: 1.2rem;
        color: var(--text);
        cursor: pointer;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: var(--white);
        display: flex;
        justify-content: center;
        align-items: center;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        border: 1px solid var(--neutral);
        transition: border-color 0.3s ease, box-shadow 0.3s ease;
        flex-shrink: 0;
    }

    .address-search-back-arrow:hover {
        border-color: var(--primary);
        box-shadow: 0 0 15px rgba(255, 107, 53, 0.4);
    }

    .address-search-icon {
        font-size: 1rem;
        color: var(--text);
    }

    .address-search-status {
        font-size: 0.9rem;
        color: var(--text);
        margin-top: 0.5rem;
        text-align: center;
        padding: 0 1rem; /* Added padding for better spacing */
    }

    /* --- IMPROVED ADDRESS RESULTS LIST STYLES --- */
    .address-results-list {
        width: 90%;
        list-style: none;
        padding: 0;
        margin-top: 1rem; /* Increased margin for better separation */
        max-height: calc(45vh - 120px); /* Adjusted height calculation */
        overflow-y: auto;
        background-color: var(--white);
        border-radius: 10px;
        box-shadow: var(--shadow); /* Added a more prominent shadow */
        border: 1px solid var(--neutral); /* Subtle border for the whole list */
        display: flex; /* Use flexbox for the list container */
        flex-direction: column; /* Stack items vertically */
        gap: 0; /* No gap between items, borders will handle separation */
    }

    .address-results-list-item {
        padding: 1rem 1.2rem; /* Increased padding for better touch target and readability */
        border-bottom: 1px solid var(--neutral); /* Clearer separator */
        cursor: pointer;
        text-align: left;
        font-size: 1rem; /* Slightly larger font size */
        color: var(--text);
        transition: background-color 0.2s ease, transform 0.1s ease; /* Added transform for a subtle click effect */
        display: flex;
        align-items: center;
        gap: 0.75rem; /* Increased space between icon and text */
    }

    .address-results-list-item:last-child {
        border-bottom: none;
    }

    .address-results-list-item:hover {
        background-color: var(--neutral);
        transform: translateY(-1px); /* Subtle lift on hover */
    }

    .address-results-list-item:active {
        transform: translateY(0); /* Reset on click */
        background-color: rgba(255, 107, 53, 0.1); /* Light orange background on active */
    }

    .address-results-list-item i {
        color: var(--primary); /* Make the icon primary color */
        font-size: 1.1rem; /* Slightly larger icon */
        flex-shrink: 0; /* Prevent icon from shrinking */
    }

    .address-results-list-item span {
        flex-grow: 1; /* Allow text to take available space */
        white-space: normal; /* Allow text to wrap if needed */
        word-break: break-word; /* Break long words */
    }
    /* --- END IMPROVED ADDRESS RESULTS LIST STYLES --- */

    /* Fixed Map Pin */
    .fixed-map-pin {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1000;
        color: var(--primary);
        font-size: 2.5rem;
        pointer-events: none;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .fixed-map-pin--rest {
        animation: pulse 1.5s infinite ease-in-out;
    }

    .fixed-map-pin--active {
        opacity: 0.7;
        transform: translate(-50%, -50%) scale(0.95);
        transition: transform 0.1s ease-out, opacity 0.1s ease-out;
    }

    @keyframes pulse {
        0% {
            transform: translate(-50%, -50%) scale(1);
            opacity: 1;
        }
        50% {
            transform: translate(-50%, -50%) scale(1.05);
            opacity: 0.8;
        }
        100% {
            transform: translate(-50%, -50%) scale(1);
            opacity: 1;
        }
    }

    .new-address-refresh-btn {
        position: absolute;
        top: 52.5%;
        right: 0.5rem;
        transform: translateY(-50%);
        bottom: auto;
        z-index: 1001;
    }

    .static-address-info-text {
        font-size: 0.9rem;
        color: var(--text);
        margin-top: 0.5rem;
        text-align: center;
        padding: 0 1rem; /* Added padding */
    }

    .hidden {
        display: none !important;
    }

    /* Selected Address Card */
    .selected-address-card {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 90%;
        background-color: var(--white);
        border-radius: 100px;
        padding: 0.8rem 1.5rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        border: 1px solid var(--neutral);
        margin-top: 1rem;
        margin-bottom: 1rem;
    }

    .selected-address-card p {
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex-grow: 1;
    }

    .edit-address-icon {
        font-size: 1.2rem;
        color: var(--primary);
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 50%;
        transition: background-color 0.2s ease;
        flex-shrink: 0;
    }

    .edit-address-icon:hover {
        background-color: rgba(255, 107, 53, 0.1);
    }

    /* Confirmation Section */
    .confirmation-section {
        width: 90%;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        padding: 1rem 0;
    }

    .confirmation-message {
        display: flex;
        align-items: center;
        background-color: #e0f7fa;
        border-left: 5px solid #00bcd4;
        border-radius: 8px;
        padding: 1rem;
        font-size: 0.9rem;
        color: #00796b;
        width: 100%;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        text-align: left;
    }

    .confirmation-message i {
        font-size: 1.5rem;
        color: #00bcd4;
        margin-right: 0.8rem;
    }

    .confirm-button {
        background-color: var(--primary);
        color: var(--white);
        padding: 0.8rem 2rem;
        border: none;
        border-radius: 10px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
        transition: background-color 0.2s ease, transform 0.2s ease;
        width: 100%;
        max-width: 300px;
    }

    .confirm-button:hover {
        background-color: var(--secondary);
        transform: translateY(-2px);
    }
`;

// --- HTML for Location Modals ---
// This HTML will be dynamically injected into the document body.
const locationModalHTML = `
    <div class="bottom-modal-overlay" id="locationModal">
        <div class="bottom-modal-content">
            <div class="bottom-modal-body">
                <div id="locationMapContainer">
                    <!-- Refresh Location button for the first modal - moved inside map container -->
                    <button class="refresh-location-btn" id="refreshMainLocationMapBtn">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
                <div class="modal-buttons-container">
                    <button class="modal-button primary" id="useCurrentLocationBtn">Use My Current Location</button>
                    <button class="modal-button secondary" id="addNewAddressBtn">Add a New Address</button>
                </div>
            </div>
        </div>
    </div>

    <!-- New modal for adding address -->
    <div class="new-address-modal-overlay" id="newAddressModal">
        <div class="new-address-modal-content">
            <button class="close-new-address-modal-btn" id="closeNewAddressModal">
                <i class="fas fa-times"></i>
            </button>
            <div id="newAddressMapContainer">
                <!-- Fixed pin overlay -->
                <div id="fixedMapPin" class="fixed-map-pin">
                    <i class="fas fa-map-marker-alt"></i>
                </div>
            </div>
            <!-- Refresh Location button for the second modal - moved outside map container -->
            <button class="refresh-location-btn new-address-refresh-btn" id="refreshNewAddressMapBtn">
                <i class="fas fa-sync-alt"></i>
            </button>
            <div class="new-address-white-bottom-overlay">
                <!-- This container will hold either the search input or the selected address card -->
                <div class="address-input-wrapper">
                    <i class="fas fa-arrow-left address-search-back-arrow" id="addressSearchBackArrow"></i>
                    <div class="address-search-container" id="addressSearchContainer">
                        <!-- Search mode elements -->
                        <div id="searchModeContent">
                            <i class="fas fa-search address-search-icon"></i>
                            <input type="text" id="addressSearchInput" class="address-search-input" placeholder="Search for an address...">
                        </div>
                    </div>
                </div>

                <!-- Selected address display mode elements - moved outside address-search-container -->
                <div id="selectedAddressModeContent" class="selected-address-card hidden">
                    <p id="selectedAddressText"></p>
                    <i class="fas fa-edit edit-address-icon" id="editAddressIcon"></i>
                </div>

                <!-- Initial content for the new address modal (hidden when searching/selected) -->
                <div id="initialAddressPrompt">
                    <p style="color: var(--primary); font-size: 150px; font-weight: 600; margin-bottom: 0; line-height: 1;"><i class="fas fa-globe-americas"></i></p>
                    <p class="static-address-info-text">Enter an address to explore restaurants around you.</p>
                    <a id="useCurrentLocationLink" style="color: var(--primary); text-decoration: underline; margin-top: 0.5rem; font-size: 0.9rem;">Use my current location</a>
                </div>
                <p id="addressSearchStatus" class="address-search-status hidden"></p>
                <ul id="addressResultsList" class="address-results-list hidden">
                    <!-- Search results will be populated here -->
                </ul>

                <!-- Confirmation Section - hidden by default -->
                <div id="confirmationSection" class="confirmation-section hidden">
                    <div class="confirmation-message">
                        <i class="fas fa-info-circle"></i>
                        <span>Aapka rider pinned location per deliver karega. Aap apne likhay huay patay per aglay page per tabdeelian karsakte hain.</span>
                    </div>
                    <button class="confirm-button">Confirm</button>
                </div>
            </div>
        </div>
    </div>
`;

// --- JavaScript for Location Logic ---
document.addEventListener('DOMContentLoaded', () => {
    // Inject CSS
    const styleTag = document.createElement('style');
    styleTag.innerHTML = locationModalStyles;
    document.head.appendChild(styleTag);

    // Inject HTML
    document.body.insertAdjacentHTML('beforeend', locationModalHTML);

    // Get references to elements after they are added to the DOM
    const locationStatus = document.getElementById('locationStatus');
    const locationDetails = document.getElementById('locationDetails');
    const locationInfoClickable = document.getElementById('locationInfoClickable');
    const locationModal = document.getElementById('locationModal');

    const refreshMainLocationMapBtn = document.getElementById('refreshMainLocationMapBtn');
    const refreshNewAddressMapBtn = document.getElementById('refreshNewAddressMapBtn');

    const useCurrentLocationBtn = document.getElementById('useCurrentLocationBtn');
    const addNewAddressBtn = document.getElementById('addNewAddressBtn');
    const newAddressModal = document.getElementById('newAddressModal');
    const closeNewAddressModal = document.getElementById('closeNewAddressModal');
    const addressSearchBackArrow = document.getElementById('addressSearchBackArrow');

    const addressSearchInput = document.getElementById('addressSearchInput');
    const addressSearchStatus = document.getElementById('addressSearchStatus');
    const addressResultsList = document.getElementById('addressResultsList');
    const initialAddressPrompt = document.getElementById('initialAddressPrompt');
    const fixedMapPin = document.getElementById('fixedMapPin');
    const fixedMapPinIcon = fixedMapPin.querySelector('i');

    const searchModeContent = document.getElementById('searchModeContent');
    const selectedAddressModeContent = document.getElementById('selectedAddressModeContent');
    const selectedAddressText = document.getElementById('selectedAddressText');
    const editAddressIcon = document.getElementById('editAddressIcon');
    const confirmationSection = document.getElementById('confirmationSection');
    const useCurrentLocationLink = document.getElementById('useCurrentLocationLink');


    let currentLatitude = 0;
    let currentLongitude = 0;

    let locationMapInitialized = false;
    let locationMap;
    let locationMarker;

    let newAddressMapInitialized = false;
    let newAddressMap;
    let moveEndTimer; // Timer for debouncing map movement
    let pinMode = 'rest'; // 'rest' or 'active'
    let isInitialNewAddressMapLoad = false; // New flag to control initial reverse geocoding

    // Define the custom icon for the map marker (used for the initial location map)
    const customOrangePersonIcon = L.divIcon({
        className: 'custom-orange-person-icon',
        html: '<i class="fas fa-user-alt"></i>',
        iconSize: [30, 30], // Size of the icon div
        iconAnchor: [15, 15] // Point of the icon which will will correspond to marker's location
    });

    // Function to update the URL hash
    const updateUrlHash = (hash) => {
        if (window.location.hash !== `#${hash}`) {
            window.location.hash = hash;
        }
    };

    // Function to handle hash changes and navigate modals
    const handleHashChange = () => {
        const hash = window.location.hash.substring(1); // Remove the '#'

        locationModal.classList.remove('active');
        newAddressModal.classList.remove('active');

        if (hash === 'location') {
            openLocationModal(false);
        } else if (hash === 'new-address') {
            openNewAddressModal(false);
        }
    };

    // Function to fetch and update location
    const fetchAndUpdateLocation = async (animateMap = false, onSuccessCallback = null, onErrorCallback = null) => {
        locationStatus.innerHTML = 'Current Location <i class="fas fa-chevron-right"></i>';
        locationDetails.textContent = 'Fetching location...';

        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                async (position) => {
                    currentLatitude = position.coords.latitude;
                    currentLongitude = position.coords.longitude;

                    locationDetails.textContent = `Location found, getting address...`;

                    try {
                        const nominatimUrl = `https://nominatim.openstreetmap.org/reverse?format=json&lat=${currentLatitude}&lon=${currentLongitude}&zoom=18&addressdetails=1&hl=en`;
                        const response = await fetch(nominatimUrl);
                        const data = await response.json();

                        if (data && data.display_name) {
                            locationDetails.textContent = data.display_name;
                        } else {
                            locationDetails.textContent = `Lat: ${currentLatitude.toFixed(2)}, Lon: ${currentLongitude.toFixed(2)} (Address not found)`;
                        }
                    } catch (error) {
                        locationDetails.textContent = `Lat: ${currentLatitude.toFixed(2)}, Lon: ${currentLongitude.toFixed(2)} (Network Error)`;
                    }

                    if (locationMapInitialized && locationMap) {
                        const targetLatLng = L.latLng(currentLatitude, currentLongitude);
                        const currentCenter = locationMap.getCenter();
                        const currentZoom = locationMap.getZoom();
                        const distance = currentCenter.distanceTo(targetLatLng);

                        const zoomThreshold = 10;
                        const finalZoom = 13;
                        const duration = 1.5;

                        if (animateMap && (currentZoom < zoomThreshold || distance > 500)) {
                            locationMap.flyTo([currentLatitude, currentLongitude], 5, { duration: duration });
                            setTimeout(() => {
                                locationMap.flyTo([currentLatitude, currentLongitude], finalZoom, { duration: duration });
                                locationMarker.setLatLng([currentLatitude, currentLongitude]);
                                locationMarker.setPopupContent('Your Location').openPopup();
                            }, duration * 1000);
                        } else {
                            locationMap.setView([currentLatitude, currentLongitude], 13);
                            locationMarker.setLatLng([currentLatitude, currentLongitude]);
                            locationMarker.setPopupContent('Your Location').openPopup();
                        }
                        locationMap.invalidateSize();
                    }

                    if (newAddressMapInitialized && newAddressMap) {
                        newAddressMap.setView([currentLatitude, currentLongitude], 13);
                        newAddressMap.invalidateSize();
                        await reverseGeocodeAndUpdateInput(currentLatitude, currentLongitude);
                    }

                    if (onSuccessCallback) {
                        onSuccessCallback();
                    }
                },
                (error) => {
                    locationStatus.innerHTML = 'Current Location <i class="fas fa-chevron-right"></i>';
                    switch(error.code) {
                        case error.PERMISSION_DENIED:
                            locationDetails.textContent = 'Permission denied.';
                            break;
                        case error.POSITION_UNAVAILABLE:
                            locationDetails.textContent = 'Location information unavailable.';
                            break;
                        case error.TIMEOUT:
                            locationDetails.textContent = 'Request timed out.';
                            break;
                        case error.UNKNOWN_ERROR:
                            locationDetails.textContent = 'An unknown error occurred.';
                            break;
                    }
                    currentLatitude = 31.5204;
                    currentLongitude = 74.3487; // Default to Lahore, Pakistan

                    if (locationMapInitialized && locationMap) {
                        locationMap.setView([currentLatitude, currentLongitude], 13);
                        if (locationMarker) {
                            locationMarker.setLatLng([currentLatitude, currentLongitude]);
                            locationMarker.setPopupContent('Your Location').openPopup();
                        }
                        locationMap.invalidateSize();
                    }
                    if (newAddressMapInitialized && newAddressMap) {
                        newAddressMap.setView([currentLatitude, currentLongitude], 13);
                        newAddressMap.invalidateSize();
                        addressSearchInput.value = '';
                    }

                    if (onErrorCallback) {
                        onErrorCallback(error);
                    }
                },
                {
                    enableHighAccuracy: true,
                    timeout: 5000,
                    maximumAge: 0
                }
            );
        } else {
            locationStatus.innerHTML = 'Current Location <i class="fas fa-chevron-right"></i>';
            locationDetails.textContent = 'Geolocation not supported by your browser.';
            currentLatitude = 31.5204;
            currentLongitude = 74.3487; // Default to Lahore, Pakistan

            if (locationMapInitialized && locationMap) {
                locationMap.setView([currentLatitude, currentLongitude], 13);
                if (locationMarker) {
                    locationMarker.setLatLng([currentLatitude, currentLongitude]);
                    locationMarker.setPopupContent('Your Location').openPopup();
                }
                locationMap.invalidateSize();
            }
            if (newAddressMapInitialized && newAddressMap) {
                newAddressMap.setView([currentLatitude, currentLongitude], 13);
                newAddressMap.invalidateSize();
                addressSearchInput.value = '';
            }

            if (onErrorCallback) {
                onErrorCallback(new Error('Geolocation not supported'));
            }
        }
    };

    // Function to open the main location modal
    const openLocationModal = (updateHash = true) => {
        locationModal.classList.add('active');
        setTimeout(() => {
            if (!locationMapInitialized) {
                locationMap = L.map('locationMapContainer', { minZoom: 5, zoomControl: false }).setView([currentLatitude, currentLongitude], 13);
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: ''
                }).addTo(locationMap);
                locationMarker = L.marker([currentLatitude, currentLongitude], { icon: customOrangePersonIcon }).addTo(locationMap)
                    .bindPopup('Your Location')
                    .openPopup();
                locationMapInitialized = true;
            } else {
                locationMap.setView([currentLatitude, currentLongitude], 13);
                if (locationMarker) {
                    locationMarker.setLatLng([currentLatitude, currentLongitude]);
                    locationMarker.setPopupContent('Your Location').openPopup();
                }
            }
            locationMap.invalidateSize();
        }, 300);
        if (updateHash) {
            updateUrlHash('location');
        }
    };

    // Function to close the main location modal
    const closeLocationModal = (updateHash = true) => {
        locationModal.classList.remove('active');
        if (updateHash) {
            updateUrlHash('');
        }
    };

    // Function to switch to search mode (initial state or after edit)
    const switchToSearchMode = () => {
        addressSearchInput.value = '';
        addressResultsList.innerHTML = '';
        addressSearchStatus.classList.add('hidden');
        addressSearchStatus.textContent = '';

        searchModeContent.classList.remove('hidden');
        selectedAddressModeContent.classList.add('hidden');
        initialAddressPrompt.classList.remove('hidden');
        addressResultsList.classList.add('hidden');
        confirmationSection.classList.add('hidden');
        newAddressModal.classList.remove('modal-address-selected');
    };

    // Function to switch to selected address mode
    const switchToSelectedAddressMode = (displayName) => {
        selectedAddressText.textContent = displayName;
        searchModeContent.classList.add('hidden');
        selectedAddressModeContent.classList.remove('hidden');
        initialAddressPrompt.classList.add('hidden');
        addressResultsList.classList.add('hidden');
        addressSearchStatus.classList.add('hidden');
        addressSearchStatus.textContent = '';
        confirmationSection.classList.remove('hidden');
        newAddressModal.classList.add('modal-address-selected');
    };

    // Function to open the new address modal
    const openNewAddressModal = (updateHash = true) => {
        newAddressModal.classList.add('active');
        isInitialNewAddressMapLoad = true;
        setTimeout(() => {
            if (!newAddressMapInitialized) {
                newAddressMap = L.map('newAddressMapContainer', { minZoom: 5, zoomControl: false }).setView([51.5074, 0.1278], 13); // Default to London
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: ''
                }).addTo(newAddressMap);
                newAddressMapInitialized = true;

                newAddressMap.on('move', handleMapMoveStart);
                newAddressMap.on('moveend', handleMapMoveEnd);
            } else {
                newAddressMap.setView([51.5074, 0.1278], 13); // Default to London
            }
            newAddressMap.invalidateSize();

            switchToSearchMode();

            fixedMapPin.classList.remove('fixed-map-pin--active');
            fixedMapPin.classList.add('fixed-map-pin--rest');
            fixedMapPinIcon.classList.remove('fa-map-pin');
            fixedMapPinIcon.classList.add('fa-map-marker-alt');
        }, 300);
        if (updateHash) {
            updateUrlHash('new-address');
        }
    };

    // Function to close the new address modal
    const closeNewAddressModalFunc = (updateHash = true) => {
        newAddressModal.classList.remove('active');
        switchToSearchMode();
        if (updateHash) {
            updateUrlHash('location');
        }
    };

    // Debounce function for search input
    let searchTimeout;
    const debounce = (func, delay) => {
        return function(...args) {
            const context = this;
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => func.apply(context, args), delay);
        };
    };

    // Handle map move start (pin goes to active mode)
    const handleMapMoveStart = () => {
        pinMode = 'active';
        fixedMapPin.classList.add('fixed-map-pin--active');
        fixedMapPin.classList.remove('fixed-map-pin--rest');
        fixedMapPinIcon.classList.remove('fa-map-marker-alt');
        fixedMapPinIcon.classList.add('fa-map-pin');
        clearTimeout(moveEndTimer);
    };

    // Function to handle map move end and reverse geocode
    const handleMapMoveEnd = async () => {
        clearTimeout(moveEndTimer);
        moveEndTimer = setTimeout(async () => {
            pinMode = 'rest';
            fixedMapPin.classList.remove('fixed-map-pin--active');
            fixedMapPin.classList.add('fixed-map-pin--rest');
            fixedMapPinIcon.classList.remove('fa-map-pin');
            fixedMapPinIcon.classList.add('fa-map-marker-alt');

            if (isInitialNewAddressMapLoad) {
                isInitialNewAddressMapLoad = false;
                return;
            }

            const centerLatLng = newAddressMap.getCenter();
            await reverseGeocodeAndUpdateInput(centerLatLng.lat, centerLatLng.lng);
        }, 500);
    };

    // Reusable function to reverse geocode and update input/status
    const reverseGeocodeAndUpdateInput = async (lat, lon) => {
        try {
            const nominatimUrl = `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lon}&zoom=18&addressdetails=1&hl=en`;
            const response = await fetch(nominatimUrl);
            const data = await response.json();

            if (data && data.display_name) {
                addressSearchInput.value = data.display_name;
                addressSearchStatus.classList.add('hidden');
                addressSearchStatus.textContent = '';
            } else {
                addressSearchInput.value = '';
                addressSearchStatus.textContent = 'Could not find address details for this location.';
                addressSearchStatus.classList.remove('hidden');
            }
        } catch (error) {
            addressSearchInput.value = '';
            addressSearchStatus.textContent = 'Network error fetching address details.';
            addressSearchStatus.classList.remove('hidden');
            console.error('Reverse geocoding error:', error);
        }
    };

    // Function to search for an address
    const searchAddress = async () => {
        const address = addressSearchInput.value.trim();
        addressResultsList.innerHTML = '';

        if (!address) {
            addressSearchStatus.classList.add('hidden');
            addressSearchStatus.textContent = '';
            initialAddressPrompt.classList.remove('hidden');
            addressResultsList.classList.add('hidden');
            confirmationSection.classList.add('hidden');
            return;
        }

        addressSearchStatus.textContent = 'Searching for addresses...';
        addressSearchStatus.classList.remove('hidden');
        initialAddressPrompt.classList.add('hidden');
        addressResultsList.classList.remove('hidden');
        confirmationSection.classList.add('hidden');

        try {
            const nominatimUrl = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}&limit=7&countrycodes=pk`;
            const response = await fetch(nominatimUrl);
            const data = await response.json();

            if (data && data.length > 0) {
                addressSearchStatus.classList.add('hidden');
                addressSearchStatus.textContent = '';
                data.forEach((result, index) => {
                    const listItem = document.createElement('div');
                    listItem.classList.add('address-results-list-item');
                    if (index < data.length - 1) {
                        listItem.style.borderBottom = '1px solid var(--neutral)';
                    }

                    const icon = document.createElement('i');
                    icon.classList.add('fas', 'fa-map-marker-alt');
                    listItem.appendChild(icon);
                    const textSpan = document.createElement('span');
                    textSpan.textContent = result.display_name;
                    listItem.appendChild(textSpan);

                    listItem.dataset.lat = result.lat;
                    listItem.dataset.lon = result.lon;
                    listItem.addEventListener('click', () => {
                        const lat = parseFloat(listItem.dataset.lat);
                        const lon = parseFloat(listItem.dataset.lon);
                        const displayName = textSpan.textContent;
                        newAddressMap.setView([lat, lon], 13);
                        switchToSelectedAddressMode(displayName);
                    });
                    addressResultsList.appendChild(listItem);
                });
            } else {
                addressSearchStatus.textContent = 'No addresses found. Please check your spelling and try again.';
                addressSearchStatus.classList.remove('hidden');
                addressResultsList.classList.add('hidden');
                initialAddressPrompt.classList.remove('hidden');
            }
        } catch (error) {
            addressSearchStatus.textContent = 'Error searching address. Please try again later.';
            addressSearchStatus.classList.remove('hidden');
            addressResultsList.classList.add('hidden');
            initialAddressPrompt.classList.remove('hidden');
            console.error('Address search error:', error);
        }
    };

    // Initial fetch of location on page load
    fetchAndUpdateLocation(false);

    // Event listener for opening the main location modal
    locationInfoClickable.addEventListener('click', () => openLocationModal());

    // Event listener for closing the main location modal (by clicking overlay)
    locationModal.addEventListener('click', (event) => {
        if (event.target === locationModal) {
            closeLocationModal();
        }
    });

    // Event listener for the refresh button in the main location modal
    refreshMainLocationMapBtn.addEventListener('click', () => fetchAndUpdateLocation(true));

    // Event listener for the refresh button in the new address modal (re-geocode current map center)
    refreshNewAddressMapBtn.addEventListener('click', () => {
        fetchAndUpdateLocation(true);
    });

    // Event listeners for the new buttons
    if (useCurrentLocationLink) {
        useCurrentLocationLink.removeAttribute('href');
    }

    useCurrentLocationBtn.addEventListener('click', () => {
        // Assuming showMessageBox is globally available or imported
        fetchAndUpdateLocation(true, () => {
            localStorage.setItem('userLatitude', currentLatitude);
            localStorage.setItem('userLongitude', currentLongitude);
            // showMessageBox('Your current location has been selected and saved!', [{ text: 'OK', callback: null, className: 'btn-primary-popup' }]);
            closeLocationModal();
        }, (error) => {
            console.error("Error using current location:", error);
        });
    });

    addNewAddressBtn.addEventListener('click', () => {
        openNewAddressModal();
    });

    // Event listener for the new "Use my current location" link
    useCurrentLocationLink.addEventListener('click', (event) => {
        event.preventDefault();
        fetchAndUpdateLocation(true);
    });

    // Event listener for the new address modal's back arrow
    addressSearchBackArrow.addEventListener('click', () => {
        closeNewAddressModalFunc();
    });

    // Event listener for closing the new address modal
    closeNewAddressModal.addEventListener('click', () => closeNewAddressModalFunc());
    newAddressModal.addEventListener('click', (event) => {
        if (event.target === newAddressModal) {
            closeNewAddressModalFunc();
        }
    });

    // Event listener for the new address search bar (live search with debounce)
    addressSearchInput.addEventListener('input', debounce(searchAddress, 500));

    // Add event listener for the edit icon
    editAddressIcon.addEventListener('click', () => {
        switchToSearchMode();
    });

    // Add hashchange listener and call on initial load
    window.addEventListener('hashchange', handleHashChange);
    handleHashChange();
});
