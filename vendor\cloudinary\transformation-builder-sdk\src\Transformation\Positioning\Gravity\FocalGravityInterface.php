<?php
/**
 * This file is part of the Cloudinary PHP package.
 *
 * (c) Cloudinary
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Cloudinary\Transformation;

/**
 * Interface FocalGravityInterface
 *
 * @package Cloudinary\Transformation
 */
interface FocalGravityInterface
{
    public const ADVANCED_FACE  = 'adv_face';
    public const ADV_FACE = 'adv_face';
    public const ADVANCED_FACES = 'adv_faces';
    public const ADV_FACES      = 'adv_faces';
    public const ADVANCED_EYES = 'adv_eyes';
    public const ADV_EYES      = 'adv_eyes';
    public const BODY     = 'body';
    public const FACE = 'face';
    public const FACES = 'faces';
    public const NO_FACES = 'no_faces';
    public const CUSTOM   = 'custom';
    public const CUSTOM_NO_OVERRIDE = 'custom_no_override';
    public const OCR_TEXT           = 'ocr_text';
    public const NONE        = 'none';
}
