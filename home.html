<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0, viewport-fit=cover">
    <meta name="theme-color" content="#FF6B35">
    <title>MIKO - Home</title>
    <link rel="manifest" crossorigin="use-credentials" href="/manifest.json">

    <!-- Google Fonts - Poppins and DM Sans -->
    <link href="https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Apple-specific PWA meta tags for iOS icons and full-screen mode -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="MIKO">
    <!-- Apple Touch Icon (recommended size 180x180 for modern iOS devices) -->
    <link rel="apple-touch-icon" href="/icons/icon-180x180.png">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Remixicons for navigation icons (re-added for profile and send) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.2.0/remixicon.css">
    <!-- Custom skeleton loading styles -->
    <style>
        .skeletor-pulse {
            animation: skeleton-pulse 1.5s ease-in-out infinite alternate;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
        }

        .skeletor-line {
            border-radius: 4px;
            background: #f0f0f0;
        }

        .skeletor-card {
            border-radius: 8px;
            background: #f0f0f0;
        }

        @keyframes skeleton-pulse {
            0% {
                background-position: -200% 0;
            }
            100% {
                background-position: 200% 0;
            }
        }
    </style>
    <style>
        /* General Reset and Variables */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Montserrat', sans-serif; /* Changed font to Montserrat */
            /* Disable default blueish highlight on click/tap */
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none; /* Disable callout on long press */
            -webkit-user-select: none;   /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* PERFORMANCE OPTIMIZATIONS */
        .categories-grid,
        .food-items-grid,
        .offers-grid,
        .restaurants-grid {
            contain: layout style paint;
            will-change: transform;
        }

        .food-item-card,
        .category-item {
            contain: layout style;
            transform: translateZ(0); /* Force GPU acceleration */
        }

        .food-item-image {
            content-visibility: auto;
            contain-intrinsic-size: 150px 120px;
        }

        /* SEAMLESS CATEGORY SWITCHING */
        .food-items-grid {
            transition: opacity 0.15s ease-out;
        }

        .food-items-grid.switching {
            opacity: 0.7;
        }

        .category-item {
            transition: all 0.2s ease-out;
        }

        .category-item.active-category {
            transform: scale(1.05);
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
        }

        /* RESTAURANT CARDS STYLING */
        .restaurant-card {
            display: flex;
            flex-direction: column;
            background: white;
            border-radius: 16px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
            margin-bottom: 16px;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .restaurant-card img {
            width: 100%;
            height: 160px;
            object-fit: cover;
            background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
        }

        .restaurant-details {
            padding: 16px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .restaurant-details h3 {
            font-size: 18px;
            font-weight: 700;
            margin: 0 0 6px 0;
            color: var(--text-color);
            line-height: 1.3;
        }

        .restaurant-details p {
            font-size: 14px;
            color: var(--subtle-detail);
            margin: 0 0 12px 0;
            line-height: 1.5;
            flex: 1;
        }

        /* RESTAURANT CARDS STYLING (from old_home.html) */
        .restaurant-card {
            display: flex;
            flex-direction: column;
            background: white;
            border-radius: 16px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
            margin-bottom: 16px;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .restaurant-card img {
            width: 100%;
            height: 160px;
            object-fit: cover;
            background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
        }

        .restaurant-details {
            padding: 16px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .restaurant-details h3 {
            font-size: 18px;
            font-weight: 700;
            margin: 0 0 6px 0;
            color: var(--text-color);
            line-height: 1.3;
        }

        .restaurant-details p {
            font-size: 14px;
            color: var(--subtle-detail);
            margin: 0 0 12px 0;
            line-height: 1.5;
            flex: 1;
        }

        .restaurant-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 13px;
            color: var(--primary-color);
            font-weight: 600;
            margin-top: auto;
            padding-top: 8px;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }

        .restaurant-meta span {
            display: flex;
            align-items: center;
            gap: 4px;
            background: rgba(255, 107, 53, 0.1);
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 12px;
        }



        /* OFFER CARDS STYLING - CONSISTENT MOBILE & DESKTOP */
        .offer-banner {
            position: relative;
            display: block;
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            cursor: pointer;
            /* Fixed dimensions for consistency */
            width: 280px;
            height: 180px;
            flex-shrink: 0;
        }

        .offer-banner:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .offer-banner img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            background: #f0f0f0;
            display: block;
        }

        .offer-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.75));
            padding: 20px 16px 16px;
            color: white;
            z-index: 2;
        }

        .offer-discount {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 6px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
            line-height: 1.2;
        }

        .offer-coupon {
            font-size: 12px;
            font-weight: 600;
            background: rgba(255, 255, 255, 0.25);
            padding: 6px 10px;
            border-radius: 8px;
            display: inline-block;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.4);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* OFFERS GRID - CONSISTENT SCROLLING */
        .offers-grid {
            display: flex;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            gap: 16px;
            padding: 0 16px 16px 16px;
            scroll-snap-type: x mandatory;
        }

        .offers-grid .offer-banner {
            scroll-snap-align: start;
        }

        /* MOBILE SPECIFIC ADJUSTMENTS */
        @media (max-width: 768px) {
            .offer-banner {
                /* Keep same dimensions on mobile */
                width: 280px;
                height: 180px;
            }

            .offers-grid {
                padding: 0 12px 12px 12px;
                gap: 12px;
            }

            .offer-overlay {
                padding: 16px 12px 12px;
            }

            .offer-discount {
                font-size: 16px;
            }

            .offer-coupon {
                font-size: 11px;
                padding: 4px 8px;
            }
        }

        /* DESKTOP SPECIFIC */
        @media (min-width: 769px) {
            .offers-grid {
                gap: 20px;
                padding: 0 20px 20px 20px;
            }
        }

        /* Remove outline on focus for all elements */
        *:focus {
            outline: none;
        }

        /* --- START: iOS PWA optimized styles --- */
        html {
            /* Prevent elastic scroll without breaking layout */
            touch-action: manipulation;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            /* Hide scrollbar for Firefox */
            scrollbar-width: none;
            /* Hide scrollbar for Edge/IE */
            -ms-overflow-style: none;
            /* Prevent elastic scroll */
            overscroll-behavior: none;
        }

        /* Hide scrollbar for Webkit browsers (Chrome, Safari) */
        html::-webkit-scrollbar {
            display: none;
        }

        body {
            background-color: var(--body-bg);
            color: var(--text-color);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-bottom: 80px;

            /* Prevent touch gestures and zoom */
            touch-action: manipulation;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-tap-highlight-color: transparent;

            /* Prevent elastic scroll on iOS */
            overscroll-behavior: none;
            -webkit-overflow-scrolling: touch;
        }
        /* --- END: iOS PWA optimized styles --- */

        :root {
            /* Site's Base Color Palette (Updated) */
            --primary-accent: #ff5018;
            --secondary-accent: #FF7F50;
            --neutral-base: #F7F7F7;
            --text-dark-elements: #333333;
            --subtle-detail: #A7A7A7;
            --pure-white: #FFFFFF;
            --shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            --transition: all 0.5s ease; /* Increased transition duration */

            /* Light Theme Colors (Default) */
            --body-bg: var(--neutral-base);
            --text-color: var(--text-dark-elements);
            --card-bg: var(--pure-white);
            --header-bg: var(--primary-accent);
            --header-text: var(--pure-white);

            /* Navigation specific colors for LIGHT theme */
            --nav-bg: var(--pure-white); /* Light background for nav */
            --nav-item-inactive-color: var(--text-dark-elements); /* Darker text for inactive on light bg */
            --nav-item-active-bg: var(--primary-accent); /* Primary color for active background */
            --nav-item-active-color: var(--pure-white); /* White icon for active on primary bg */
            --nav-add-button-bg: var(--primary-accent); /* Primary color for the add button */
            --nav-add-button-color: var(--pure-white); /* White icon for the add button */
            --nav-shadow-color: rgba(0, 0, 0, 0.15); /* Slightly darker shadow for light nav */

            /* Dark Theme Colors */
            --dark-body-bg: #1a1a1a; /* Dark background */
            --dark-text-color: #f0f0f0; /* Light text */
            --dark-card-bg: #2a2a2a; /* Slightly lighter dark for cards */
            --dark-header-bg: #ff5018; /* Keep primary accent for header */
            --dark-header-text: #ffffff; /* White text for header */

            /* Navigation specific colors for DARK theme */
            --dark-nav-bg: #2a2a2a; /* Dark background for nav */
            --dark-nav-item-inactive-color: #cccccc; /* Lighter text for inactive on dark bg */
            --dark-nav-item-active-bg: var(--primary-accent); /* Primary color for active background */
            --dark-nav-item-active-color: var(--pure-white); /* White icon for active on primary bg */
            --dark-nav-add-button-bg: var(--primary-accent); /* Primary color for the add button */
            --dark-nav-add-button-color: var(--pure-white); /* White icon for the add button */
            --dark-nav-shadow-color: rgba(255, 255, 255, 0.1); /* Lighter shadow for dark nav */

            /* --- Font Sizes (using rem for scalability) --- */
            --font-size-base: 1rem; /* 16px - Base for p and general text */
            --font-size-small: 0.75rem; /* 12px - For subtle details, review counts */
            --font-size-medium: 0.9rem; /* 14.4px - For category names, coupon tags */
            --font-size-large: 1.0rem; /* 16px - For food item names, prices */
            --font-size-x-large: 1.1rem; /* 17.6px - For restaurant names, h4 */
            --font-size-xx-large: 1.3rem; /* 20.8px - For h2 */
            --font-size-greetings: 1.8rem; /* 28.8px - For header-greeting */
            --font-size-heading: 2.2rem; /* 35.2px - For a main page title if needed */
        }

        /* Media query for dark mode preference */
        @media (prefers-color-scheme: dark) {
            :root {
                --body-bg: var(--dark-body-bg);
                --text-color: var(--dark-text-color);
                --card-bg: var(--dark-card-bg);
                --header-bg: var(--dark-header-bg);
                --header-text: var(--dark-header-text);

                --nav-bg: var(--dark-nav-bg);
                --nav-item-inactive-color: var(--dark-nav-item-inactive-color);
                --nav-item-active-color: var(--dark-nav-item-active-color);
                --nav-item-active-bg: var(--dark-nav-item-active-bg);
                --nav-add-button-bg: var(--dark-nav-add-button-bg);
                --nav-add-button-color: var(--dark-nav-add-button-color);
                --nav-shadow-color: var(--dark-nav-shadow-color);
            }
        }


        /* App Header */
        .app-header {
            width: 100%;
            height: 305px; /* Set initial height to 280px */
            max-height: 305px;
            background-color: var(--primary-accent);
            background-image: url("data:image/svg+xml,%3Csvg width='200' height='200' viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='foodPattern' x='0' y='0' width='200' height='200' patternUnits='userSpaceOnUse'%3E%3Cg fill='none' stroke='white' stroke-width='1.5' opacity='0.2'%3E%3C!-- Tomato/Round Item --%3E%3Ccircle cx='40' cy='40' r='18'/%3E%3Cpath d='M40 22 L40 58 M22 40 L58 40'/%3E%3C!-- Chili Pepper 1 --%3E%3Cpath d='M160 30 Q175 15, 185 30 T190 50 T180 70 T160 60 Z'/%3E%3C!-- Cucumber Slice --%3E%3Ccircle cx='140' cy='150' r='15'/%3E%3Cpath d='M140 135 L140 165 M125 150 L155 150'/%3E%3C!-- Leaf --%3E%3Cpath d='M60 110 Q70 100, 80 110 T90 120 T80 130 T70 120 T60 110 Z'/%3E%3C!-- Chili Pepper 2 (rotated) --%3E%3Cg transform='translate(20, 100) rotate(90)'%3E%3Cpath d='M160 30 Q175 15, 185 30 T190 50 T180 70 T160 60 Z'/%3E%3C/g%3E%3C/g%3E%3C/pattern%3E%3C/defs%3E%3Crect x='0' y='0' width='100%25' height='100%25' fill='url(%23foodPattern)'/%3E%3C/svg%3E");
            color: var(--header-text);
            display: flex;
            flex-direction: column; /* Changed to column for top and bottom sections */
            justify-content: center;
            align-items: center;
            box-shadow: var(--shadow);
            border-bottom-left-radius: 50px; /* Changed from 40px to 50px */
            border-bottom-right-radius: 50px; /* Changed from 40px to 50px */
            position: sticky;
            top: 0;
            z-index: 100;
            /* Added slide-down animation */
            animation: slide-down 0.7s ease-out forwards;
            /* Unified transition for all transformable properties, REMOVED HEIGHT */
            transition: padding 0.3s ease, border-radius 0.3s ease, background-color 0.3s ease, height 0.3s ease, transform 0.3s ease, opacity 0.3s ease; /* Added transform, opacity */
            overflow: hidden; /* Ensure content is clipped during height transition */
        }

        /* Keyframes for slide-down animation */
        @keyframes slide-down {
            0% {
                transform: translateY(-100%);
                opacity: 0;
            }
            100% {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* Styles for the smaller sticky header - STAGE 3 (Final) */
        .app-header.header-scrolled {
            border-bottom-left-radius: 25px; /* Changed from 15px to 25px */
            border-bottom-right-radius: 25px; /* Changed from 15px to 25px */
        }

        /* New style for the MIKO logo */
        .app-logo {
            background-color: var(--pure-white);
            color: var(--primary-accent);
            padding: 0.3rem 0.8rem;
            border-radius: 20px; /* Pill shape */
            font-size: var(--font-size-medium); /* Applied variable */
            font-weight: 600;
            margin-bottom: 0.5rem; /* Space between logo and top row */
            box-shadow: var(--shadow);
            z-index: 101; /* Ensure it's above other elements if needed */
        }

        .header-top-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            padding: 0 1rem; /* Simplified padding to 0 1rem */
            transition: padding 0.3s ease; /* Smooth transition for padding */
        }

        .header-profile-pic {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: #ccc; /* Placeholder background */
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .header-profile-pic img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .header-location {
            display: flex;
            flex-direction: column; /* Main container for location info, stacked vertically */
            align-items: center; /* Center items horizontally within this column */
            color: var(--pure-white);
            cursor: pointer;
        }

        .header-location-top-row {
            display: flex; /* New row for "Delivery location" and arrow */
            align-items: center; /* Vertically center items in this row */
            gap: 4px; /* Small gap between text and arrow */
        }

        .header-location-label {
            font-size: var(--font-size-medium); /* Applied variable */
            opacity: 0.8;
            font-weight: 400; /* Lighter weight */
        }

        .down-arrow-icon-small-wrapper {
            display: flex; /* Ensure SVG is centered within its small div */
            align-items: center;
            justify-content: center;
        }

        .header-location-name {
            font-weight: 600; /* Bolder for the actual location */
            font-size: var(--font-size-medium); /* Applied variable */
            margin-top: 2px; /* Small space between label row and location name */
        }

        /* Container for the right-side icons */
        .header-right-icons {
            position: relative; /* Establish positioning context for children */
            width: 40px; /* Fixed width and height for the container */
            height: 40px;
            display: flex; /* Use flex to center the absolute children */
            justify-content: center;
            align-items: center;
        }

        /* Notification Icon Styling */
        .header-notification-icon,
        .header-search-icon-small {
            position: absolute; /* Position both icons absolutely within their parent */
            top: 0;
            left: 0;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--pure-white);
            box-shadow: var(--shadow);
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--text-dark-elements); /* Ensures the icon color is dark */
            font-size: var(--font-size-large); /* Applied variable */
            cursor: pointer;
            transition: opacity 0.3s ease, transform 0.3s ease; /* Explicitly added transition */
            /* Initial state for notification icon */
            opacity: 1;
            transform: scale(1);
            pointer-events: auto;
            z-index: 10; /* Ensure notification is on top initially */
        }

        .header-notification-icon svg {
            width: 24px;
            height: 24px;
        }

        /* Small Search Icon Styling */
        .header-search-icon-small {
            /* Initial state for search icon */
            opacity: 0;
            transform: scale(0.8); /* Start smaller */
            pointer-events: none; /* Disable clicks when hidden */
            z-index: 5; /* Ensure search is behind notification initially */
        }

        .header-search-icon-small svg {
            width: 24px;
            height: 24px;
            fill: var(--subtle-detail); /* Matches the search bar icon color */
        }

        .header-bottom-row {
            width: 100%;
            text-align: left;
            padding: 0 1rem 0.5rem 1rem;
            transition: opacity 0.3s ease, max-height 0.3s ease, padding 0.3s ease, margin 0.3s ease; /* Faster transition */
            max-height: 200px; /* Initial max-height to allow transition */
        }

        .header-greeting {
            font-size: var(--font-size-greetings); /* Applied variable */
            font-weight: 700;
            color: var(--pure-white);
            margin-bottom: 1rem; /* Re-added margin */
            margin-top: 1.5rem; /* Added margin-top */
            transition: opacity 0.3s ease, transform 0.3s ease, margin 0.3s ease, font-size 0.3s ease; /* Added transform */
        }

        .header-search-bar {
            background-color: var(--pure-white);
            border-radius: 100px; /* Changed to 100px for full roundness */
            display: flex;
            align-items: center;
            padding: 0.8rem 1rem; /* This applies 0.8rem padding to top and bottom, and 1rem to left and right */
            box-shadow: var(--shadow);
            width: 100%;
            transition: opacity 0.3s ease, transform 0.3s ease, padding 0.3s ease; /* Added transform */
        }

        .header-search-bar svg { /* Style for the new SVG icon */
            width: 24px;
            height: 24px;
            margin-right: 0.8rem;
            fill: var(--subtle-detail); /* Use subtle-detail for fill color */
        }

        .header-search-bar input {
            border: none;
            flex-grow: 1;
            font-size: var(--font-size-large); /* Applied variable */
            color: var(--text-dark-elements);
            background: transparent;

            /* Prevent iOS Safari auto-scroll on focus */
            transform: translateZ(0);
            -webkit-transform: translateZ(0);
            -webkit-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
            touch-action: manipulation;
        }

        .header-search-bar input::placeholder {
            color: var(--subtle-detail);
        }

        .header-filter-icon {
            color: var(--subtle-detail);
            font-size: var(--font-size-large); /* Applied variable */
            margin-left: 0.8rem;
            cursor: pointer;
        }

        .header-title {
            font-size: var(--font-size-x-large); /* Applied variable (could be h4 or similar) */
            font-weight: 600;
        }

        /* Main Content */
        .main-content {
            flex-grow: 1;
            width: 100%;
            max-width: 800px;
            padding: 0;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start; /* Align content to the top */
            /* Removed initial opacity and transform, as skeleton will be visible */
        }

        /* Section Titles */
        .categories-section h2,
        .food-items-section h2,
        .offers-section h2,
        .restaurants-section h2 { /* Added restaurants section title */
            font-size: var(--font-size-xx-large); /* Applied variable */
            font-weight: 600;
            color: var(--text-color);
            /* Updated margin as requested */
            margin: -10px 10px 10px 10px;
            text-align: left; /* Default alignment for most titles */
        }

        /* Specific style for the #SpecialForYou heading */
        .special-for-you-section h2 {
            font-size: var(--font-size-xx-large); /* Applied variable */
            font-weight: 600;
            color: var(--text-color);
            /* Removed margin-bottom and padding-left, now handled by .section-header-container */
            padding-left: 0; /* Remove left padding */
            text-align: center; /* Center the text */
        }

        /* Categories Section */
        .categories-section {
            width: 100%;
            text-align: left;
            margin-top: 1.5rem; /* Space from header */
        }

        /* Skeleton Loading Styles */
        .skeleton-container {
            display: flex;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            white-space: nowrap;
            padding: 0 .5rem .5rem .5rem;
            gap: 0.75rem; /* Adjust as needed for specific grids */
            align-items: center;
            /* Hide scrollbar for Firefox */
            scrollbar-width: none;
            /* Hide scrollbar for Edge/IE */
            -ms-overflow-style: none;
        }

        /* Hide scrollbar for Webkit browsers */
        .skeleton-container::-webkit-scrollbar {
            display: none;
        }



        /* Hide actual content initially using opacity and pointer-events */
        .actual-content-hidden {
            opacity: 0;
            pointer-events: none; /* Disable clicks when hidden */
        }

        /* Show actual content when loaded using opacity and pointer-events */
        .actual-content-visible {
            opacity: 1;
            pointer-events: auto;
        }

        /* Add transition to the grids for smooth fading */
        .categories-grid,
        .food-items-grid,
        .offers-grid,
        .restaurants-grid { /* Added restaurants-grid */
            transition: opacity 0.3s ease-in-out;
        }

        /* Ensure skeletons are hidden with display: none when not needed */
        .skeleton-container.hidden {
            display: none;
        }

        /* Categories Grid */
        .categories-grid {
            display: flex; /* Changed to flex for horizontal layout */
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            white-space: nowrap;
            padding: 0.5rem .5rem .5rem .5rem;
            gap: 0.75rem; /* Adjust as needed for specific grids */
            align-items: center;
            /* Hide scrollbar for Firefox */
            scrollbar-width: none;
            /* Hide scrollbar for Edge/IE */
            -ms-overflow-style: none;
        }

        /* Custom Scrollbar Styles for Webkit (Chrome, Safari, Edge) */
        .categories-grid::-webkit-scrollbar {
            display: none; /* Hide scrollbar for Webkit browsers */
        }

        .food-items-grid::-webkit-scrollbar,
        .offers-grid::-webkit-scrollbar,
        .restaurants-grid::-webkit-scrollbar { /* Added for restaurants-grid */
            /* Hide scrollbar for Firefox */
            scrollbar-width: none;
            /* Hide scrollbar for Edge/IE */
            -ms-overflow-style: none;
        }

        .food-items-grid::-webkit-scrollbar,
        .offers-grid::-webkit-scrollbar,
        .restaurants-grid::-webkit-scrollbar { /* Added for restaurants-grid */
            display: none; /* Hide scrollbar for Webkit browsers */
        }

        .categories-grid::-webkit-scrollbar-track,
        .food-items-grid::-webkit-scrollbar-track,
        .offers-grid::-webkit-scrollbar-track,
        .restaurants-grid::-webkit-scrollbar-track { /* Added for restaurants-grid */
            background: transparent; /* Transparent track */
            border-radius: 10px;
        }

        .categories-grid::-webkit-scrollbar-thumb,
        .food-items-grid::-webkit-scrollbar-thumb,
        .offers-grid::-webkit-scrollbar-thumb,
        .restaurants-grid::-webkit-scrollbar-thumb { /* Added for restaurants-grid */
            background-color: var(--primary-accent); /* Orange thumb */
            border-radius: 10px;
            border: 2px solid transparent; /* Transparent border to make thumb thinner if needed */
        }

        .category-item {
            display: inline-flex; /* Use inline-flex for horizontal alignment of emoji and name */
            align-items: center;
            justify-content: center;
            background-color: var(--card-bg);
            border: 1px solid transparent; /* Added transparent border for smooth transition */
            border-radius: 25px; /* Reverted to slightly larger pill shape for better appearance */
            padding: 0.6rem 1.2rem; /* Adjusted padding for wider pill shape */
            text-decoration: none;
            color: var(--text-color);
            transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, transform 0.3s ease-out; /* Added transform to transition */
            flex-shrink: 0; /* Prevent shrinking */
            position: relative; /* Needed for transform */
            height: 40px;
        }

        .category-item:hover {
            background-color: var(--card-bg); /* Keep background as it is */
            color: var(--text-color); /* Keep text color as it is */
            border: 1px solid var(--primary-accent); /* Show orange outline on hover */
        }

        .category-item.active-category {
            background-color: var(--primary-accent); /* Fills with primary accent color */
            color: var(--pure-white); /* Ensures active category text is always white */
            font-weight: 600;
            border-color: var(--primary-accent); /* Ensure border is also primary accent when active */
        }

        .category-item .emoji {
            font-size: var(--font-size-medium); /* Applied variable */
            margin-right: 0.4rem; /* Slightly more space between emoji and name */
            margin-bottom: 0; /* Remove bottom margin */
            line-height: 1;
        }

        .category-item .name {
            font-size: var(--font-size-medium); /* Applied variable */
            font-weight: 500;
            white-space: nowrap; /* Prevent text wrapping */
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Food Items Section */
        .food-items-section {
            width: 100%;
            text-align: left;
            margin-top: 1.5rem; /* Space from categories section */
        }

        .food-items-grid {
            display: flex;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            white-space: nowrap;
            padding: 0 1rem 0.5rem 1rem;
            gap: 1rem;
            align-items: flex-start;
        }

        .food-item-card {
            background-color:#8b8b8b0f; /* Changed to transparent */
            border-radius: 15px;

            overflow: hidden;
            text-decoration: none;
            color: var(--text-color);
            display: flex;
            flex-direction: column;
            align-items: center;
            transition: transform 0.2s ease-in-out;
            position: relative; /* For the discount tag positioning */
            flex-shrink: 0; /* Prevent shrinking in horizontal layout */
            width: 200px; /* Increased width for a larger appearance */
            min-height: 280px; /* Increased minimum height to ensure it's taller */
        }

        /* New discount tag styles */
        .discount-tag {
            position: absolute;
            top: 15px; /* Adjusted to match the image */
            left: 0;
            background: var(--primary-accent); /* Use primary accent color */
            color: var(--pure-white);
            font-size: var(--font-size-small); /* Applied variable */
            font-weight: 700;
            padding: 0.2rem 0.6rem; /* Adjusted padding */
            box-shadow: -1px 2px 3px rgba(0, 0, 0, 0.3); /* Match ribbon shadow */
            display: flex; /* Added flex to align content */
            align-items: center; /* Center items vertically */
            gap: 0.3rem; /* Space between icon and text */
            z-index: 10; /* Ensure it's above the image */
        }

        .discount-tag:before,
        .discount-tag:after {
            content: "";
            position: absolute;
        }

        .discount-tag:before {
            width: 7px;
            height: 100%;
            top: 0;
            left: -6.5px;
            padding: 0 0 7px;
            background: inherit; /* Inherit background from parent (.discount-tag) */
            border-radius: 5px 0 0 5px;
        }

        .discount-tag:after {
            width: 5px;
            height: 5px;
            bottom: -5px;
            left: -4.5px;
            background: var(--secondary-accent); /* Use secondary accent for the small corner */
            border-radius: 55px 0 0 5px;
        }

        .discount-tag svg {
            width: 16px; /* Adjust SVG size within the tag */
            height: 16px;
            fill: currentColor; /* Ensure SVG color matches text color */
        }

        /* New container for image and details to ensure full card coverage and better layout */
        .food-item-content {
            display: flex;
            flex-direction: column;
            width: 100%;
            height: 100%; /* Ensure it covers the whole card */
            padding-bottom: 0.8rem; /* Padding for the bottom of the content area */
        }

        .food-item-image {
            width: 90%; /* Adjusted width */
            height: 170px; /* Fixed height for images */
            object-fit: contain; /* Changed to contain to show full image */
            border-radius: 10px; /* Added border-radius to the image */
            margin-bottom: 4px; /* Space below image */
            align-self: center; /* Center the image within the card */
            transition: opacity 0.3s ease; /* Smooth transition for lazy loading */
        }

        /* Lazy loading styles */
        .lazy-load {
            opacity: 0.7; /* Slightly transparent while loading */
            background-color: #f0f0f0; /* Light background while loading */
        }

        .lazy-load:not([src*="data:image"]) {
            opacity: 1; /* Full opacity when loaded */
        }

        /* Favorite icon wrapper - NO CIRCLE */
        .food-item-favorite-wrapper {
            width: 24px; /* Adjust to match SVG size */
            height: 24px; /* Adjust to match SVG size */
            display: flex;
            justify-content: center;
            align-items: center;
            /* Removed border-radius, background-color, box-shadow */
            flex-shrink: 0; /* Prevent shrinking when placed in flex container */
            margin-left: auto; /* Push to the right within the price row */
            cursor: pointer; /* Indicate it's clickable */
        }

        .food-item-favorite-wrapper svg { /* Style for the new SVG icon */
            width: 100%; /* Make SVG fill the wrapper */
            height: 100%;
            /* Fill color is now handled by the SVG itself or the .liked class */
            transition: fill 0.2s ease-in-out; /* Transition for fill color */
        }

        .food-item-favorite-wrapper.liked svg path {
            /* fill: #ff0a0a; Removed as SVG now has internal fill */
            stroke: none; /* Remove stroke when filled */
        }
        /* Ensure default icon stroke is visible */
        .food-item-favorite-wrapper:not(.liked) svg path {
            stroke: #808080; /* Grey stroke for unliked state */
            fill: none; /* No fill for unliked state */
        }


        .food-item-details {
            padding: 18.8px 6px 5px 6px;/* Adjusted padding for better horizontal spacing */
            width: 100%;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            flex-grow: 1; /* Allow details to grow and fill available space */
            justify-content: space-between; /* Distribute content vertically */
        }

        .food-item-name-wrapper {
            display: flex;
            justify-content: space-between;
            align-items: flex-start; /* Align items to the top */
            width: 100%;
            margin-bottom: 0.4rem; /* Space below name wrapper */
        }

        .food-item-name {
            font-size: var(--font-size-large); /* Applied variable */
            font-weight: 700; /* Bolder font weight */
            /* Removed white-space, overflow, text-overflow to allow wrapping */
            color: var(--text-color); /* Changed to var(--text-color) for dark mode compatibility */
            flex-grow: 1; /* Allow name to take available space */
            padding-right: 0.5rem; /* Space between name and heart */
        }

        .food-item-reviews-wrapper {
            display: flex;
            align-items: center;
            font-size: var(--font-size-small); /* Applied variable */
            color: var(--subtle-detail);
            margin-bottom: 0.4rem; /* Space below reviews */
            width: 100%; /* Ensure it takes full width */
        }

        /* Style for the new SVG star icon */
        .food-item-reviews-wrapper .star-icon-svg {
            width: 16px; /* Adjust size as needed */
            height: 16px;
            margin-right: 0.3rem;
            flex-shrink: 0; /* Prevent shrinking */
        }

        .food-item-reviews-wrapper .star-icon-svg path {
            fill: #ffc038; /* Ensure the fill color is yellow/gold */
            stroke: #e2ac18; /* Ensure the stroke color is also yellow/gold */
        }

        .food-item-price-row {
            display: flex;
            justify-content: space-between; /* Distribute space between price and icon */
            align-items: center; /* Align vertically in the middle */
            width: 100%;
            margin-top: auto; /* Push price row to the bottom of the details section */
        }

        .food-item-price-section {
            display: flex; /* Use flex to align original and current price */
            align-items: baseline; /* Align prices at their baseline */
            gap: 0.5rem; /* Space between original and current price */
        }

        .food-item-price {
            font-size: var(--font-size-large); /* Applied variable */
            font-weight: 700; /* Bolder price */
            color: var(--primary-accent); /* Changed to primary accent (orange) */
            white-space: nowrap; /* Prevent wrapping */
            overflow: hidden; /* Hide overflow */
            text-overflow: ellipsis; /* Add ellipsis if text overflows */
            /* Removed margin-right to fix spacing, now handled by gap in .food-item-price-section */
        }

        .food-item-original-price {
            font-size: var(--font-size-small); /* Applied variable */
            color: var(--subtle-detail);
            text-decoration: line-through;
            white-space: nowrap; /* Prevent wrapping */
            overflow: hidden; /* Hide overflow */
            text-overflow: ellipsis; /* Add ellipsis if text overflows */
            /* Removed margin-right to fix spacing, now handled by gap in .food-item-price-section */
        }

        /* Offers Section */
        .offers-section {
            width: 100%;
            text-align: left;
            margin-top: 1.5rem; /* Space from previous section */
        }

        .offers-grid {
            display: flex;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            white-space: nowrap;
            padding: 0 1rem 1rem 1rem;
            gap: 1rem;
            align-items: flex-start;
            /* Hide scrollbar for Firefox */
            scrollbar-width: none;
            /* Hide scrollbar for Edge/IE */
            -ms-overflow-style: none;
        }

        .offers-grid::-webkit-scrollbar {
            display: none; /* Hide scrollbar for Webkit browsers */
        }

        /* Styles for the actual offer banners */
        .offer-banner {
            position: relative; /* Needed for absolute positioning of overlay */
            flex-shrink: 0;
            width: 385px;
            height: 170px;
            border-radius: 15px;
            box-shadow: var(--shadow);
            overflow: hidden; /* Ensures image and overlay stay within bounds */
            text-decoration: none; /* Remove underline from link */
            color: var(--pure-white); /* Default text color for overlay content */
        }

        .offer-banner img {
            width: 100%;
            height: 100%;
            object-fit: cover; /* Cover the entire banner area */
            border-radius: 15px; /* Match banner border-radius */
            transition: transform 0.3s ease; /* Smooth hover effect */
        }

        .offer-banner:hover img {
            transform: scale(1.05); /* Slight zoom on hover */
        }

        .offer-banner .overlay {
            position: absolute;
            top: 10px;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: flex-end; /* Align content to the bottom */
            align-items: flex-start; /* Align content to the left */
            padding: 1rem;
            border-radius: 15px; /* Match banner border-radius */
        }

        .offer-banner .discount-text {
            font-size: var(--font-size-xx-large); /* Applied variable */
            font-weight: 700;
            color: var(--pure-white);
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
            margin-bottom: 0.2rem;
        }

        .offer-banner .code-text {
            font-size: var(--font-size-medium); /* Applied variable */
            font-weight: 500;
            color: var(--pure-white);
            opacity: 0.8;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
        }

        /* New styles for Restaurants Section */
        .restaurants-section {
            width: 100%;
            text-align: left;
            margin-top: 1.5rem; /* Space from previous section */
            margin-bottom: 1.5rem; /* Space before nav bar */
        }

        /* Modified restaurants-grid for single column layout */
        .restaurants-grid {
            display: block; /* Stack items vertically */
            padding: 0 1rem; /* Consistent padding with other sections */
            /* Removed overflow-x, white-space, gap as they are not needed for block layout */
        }

        /* Modified restaurant-card for column layout */
        .restaurant-card {
            background-color: var(--card-bg); /* Use card background */
            border-radius: 15px;
            box-shadow: var(--shadow);
            overflow: hidden;
            text-decoration: none;
            color: var(--text-color);
            display: flex; /* Use flex for column layout of image and details */
            flex-direction: column; /* Image on top, details below */
            align-items: flex-start; /* Align items to the left within the card */
            transition: transform 0.2s ease-in-out;
            width: 100%; /* Full width of the container */
            /* Removed fixed height for rectangular card, let it adjust based on content */
            margin-bottom: 1rem; /* Space between cards */
            padding: 0; /* Remove padding from here, individual sections will have it */
            position: relative; /* For heart icon and coupon tag positioning */
        }

        .restaurant-card:last-child {
            margin-bottom: 0; /* No margin for the last card */
        }

        /* New wrapper for the image to allow absolute positioning of the heart icon */
        .restaurant-image-wrapper {
            position: relative;
            width: 100%;
            height: 200px; /* Increased height for the image */
            overflow: hidden;
            border-radius: 15px 15px 0 0; /* Rounded top corners, sharp bottom corners */
        }

        /* Modified restaurant-image for column layout */
        .restaurant-image {
            width: 100%; /* Full width for the image */
            height: 100%; /* Fill the wrapper height */
            object-fit: cover; /* Cover the area, cropping if necessary */
            border-radius: 15px 15px 0 0; /* Match wrapper border-radius */
            flex-shrink: 0; /* Prevent shrinking */
            transition: opacity 0.3s ease; /* Smooth transition for lazy loading */
        }

        /* New style for the heart icon on restaurant cards */
        .restaurant-favorite-wrapper {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 40px; /* Larger tap target */
            height: 40px;
            background-color: var(--pure-white);
            border-radius: 50%;
            box-shadow: var(--shadow);
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            z-index: 10; /* Ensure it's above the image */
        }

        .restaurant-favorite-wrapper svg {
            width: 24px; /* Icon size */
            height: 24px;
            transition: fill 0.2s ease-in-out, stroke 0.2s ease-in-out;
        }

        .restaurant-favorite-wrapper.liked svg path {
            fill: #ff0000; /* Red fill when liked */
            stroke: none; /* Remove stroke when filled */
        }

        .restaurant-favorite-wrapper:not(.liked) svg path {
            fill: none; /* No fill when not liked */
            stroke: #808080; /* Grey stroke when not liked */
        }

        /* Modified restaurant-details for column layout */
        .restaurant-details {
            display: flex;
            flex-direction: column;
            justify-content: space-between; /* Distribute content vertically */
            flex-grow: 1; /* Allow details to take up remaining space */
            align-items: flex-start; /* Align text to the left */
            padding: 0.8rem 1rem 0.6rem 1rem; /* Padding inside the details section */
            width: 100%; /* Ensure details take full width */
        }

        /* New container for name, rating, and delivery info */
        .restaurant-details-main {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            margin-bottom: 0.5rem; /* Space before coupon tag */
        }

        /* New row for name and rating */
        .restaurant-name-rating-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            margin-bottom: 0.2rem; /* Small space between name/rating and delivery info */
        }

        .restaurant-name {
            font-size: var(--font-size-x-large); /* Applied variable */
            font-weight: 700;
            color: var(--text-color);
            flex-grow: 1;
            white-space: normal; /* Allow text to wrap */
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2; /* Limit to 2 lines */
            line-clamp: 2; /* Standard property for compatibility */
            -webkit-box-orient: vertical;
            padding-right: 0.5rem; /* Space between name and rating */
        }

        .restaurant-rating {
            display: flex;
            align-items: center;
            font-size: var(--font-size-medium); /* Applied variable */
            color: var(--subtle-details); /* Darker text for rating */
            font-weight: 600; /* Bolder rating */
            flex-shrink: 0; /* Prevent shrinking */
            gap: 0.2rem; /* Smaller gap between star and number */
        }

        /* Style for the new SVG star icon in restaurant cards */
        .restaurant-rating .star-icon-svg {
            width: 16px; /* Adjust size as needed */
            height: 16px;
            margin-right: 0.3rem;
            flex-shrink: 0; /* Prevent shrinking */
        }

        .restaurant-rating .star-icon-svg path {
            fill: #ffc038; /* Ensure the fill color is yellow/gold */
            stroke: #e2ac18; /* Ensure the stroke color is also yellow/gold */
        }

        .restaurant-rating .review-count {
            font-size: var(--font-size-small); /* Applied variable */
            font-weight: 400; /* Lighter weight */
            color: var(--subtle-detail);
            margin-left: 0.2rem; /* Space between rating and count */
        }

        /* New row for delivery time and cuisine type */
        .restaurant-time-cuisine-row {
            display: flex;
            align-items: center;
            width: 100%;
            font-size: var(--font-size-medium); /* Applied variable */
            color: var(--subtle-detail);
            font-weight: 500;
            gap: 0.4rem; /* Small gap between elements */
            margin-bottom: 0.2rem; /* Small space below this row */
        }

        /* New row for distance and delivery charges */
        .restaurant-distance-delivery-row {
            display: flex;
            align-items: center;
            width: 100%;
            font-size: var(--font-size-medium); /* Applied variable */
            color: var(--subtle-detail);
            font-weight: 500;
            gap: 0.4rem; /* Small gap between elements */
            margin-bottom: 0.5rem; /* Space below this row and before separator */
        }

        .restaurant-delivery-time,
        .restaurant-distance-info,
        .restaurant-delivery-charges,
        .restaurant-cuisine-type {
            display: flex;
            align-items: center;
            white-space: nowrap;
            flex-shrink: 0;
            gap: 0.3rem; /* Space between icon and text */
        }

        .restaurant-delivery-time .time-icon-svg,
        .restaurant-distance-info .distance-icon-svg,
        .restaurant-delivery-charges .delivery-icon-svg {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
            fill: var(--subtle-detail);
        }

        /* Separator styles */
        .dot-separator {
            color: var(--subtle-detail);
            font-size: var(--font-size-medium); /* Applied variable */
            margin: 0 0.2rem;
            flex-shrink: 0;
        }
        .dash-separator {
            color: var(--subtle-detail);
            font-size: var(--font-size-medium); /* Applied variable */
            margin: 0 0.2rem;
            flex-shrink: 0;
        }

        /* Updated restaurant-info-separator for dashed line */
        .restaurant-info-separator {
            width: 100%;
            border-top: 1px dashed #e0e0e0; /* Changed to dashed border */
            margin: 0.5rem 0; /* Space above and below separator */
        }

        /* New container for coupon tags */
        .restaurant-offers-container {
            display: flex;
            flex-wrap: wrap; /* Allow items to wrap to the next line */
            gap: 0.5rem; /* Space between individual coupon tags */
            width: 100%;
        }

        /* New coupon tag for restaurant cards */
        .restaurant-coupon-tag {
            background-color: #ffe5e5; /* Light pink background */
            color: #ff0000; /* Red text */
            font-size: var(--font-size-medium); /* Applied variable */
            font-weight: 600;
            padding: 0.4rem 0.8rem;
            border-radius: 50px; /* Changed to 50px for pill shape */
            display: flex;
            align-items: center;
            gap: 0.4rem;
            /* Removed margin-top and margin-bottom as gap on parent handles it */
            align-self: flex-start; /* Align to the left */
            flex-shrink: 0; /* Prevent shrinking */
        }
        /* No need for last-child margin adjustment on individual tags due to gap */


        .restaurant-coupon-tag svg {
            width: 18px;
            height: 18px;
            fill: #ff0000; /* Red fill for icon */
        }


        /* New Floating Bottom Navigation Bar */
        .nav {
            position: fixed;
            bottom: 20px; /* Distance from the bottom */
            left: 50%;
            transform: translateX(-50%);
            width: 90%;
            background-color: var(--nav-bg); /* Uses theme variable */
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 5px 0; /* Reduced vertical padding */
            border-radius: 50px; /* 100% radius for rounded ends */
            /* Adjusted glowing border: reduced blur and spread */
            box-shadow: 0 4px 15px var(--nav-shadow-color), 0 0 3px 0.5px var(--primary-accent);
            z-index: 1000;
            overflow: hidden; /* Hide items outside the initial narrow width */
            height: 80px; /* Fixed height for consistent centering of items */
            /* Added slide-up animation */
            animation: slide-up 0.7s ease-out forwards;
            animation-delay: 0.2s; /* Delay the animation slightly after header */
            opacity: 0; /* Start hidden for animation */
        }

        /* Keyframes for slide-up animation */
        @keyframes slide-up {
            0% {
                transform: translateX(-50%) translateY(100%);
                opacity: 0;
            }
            100% {
                transform: translateX(-50%) translateY(0);
                opacity: 1;
            }
        }

        .nav__list {
            list-style: none;
            display: flex;
            justify-content: space-around; /* Distribute items */
            align-items: center;
            width: 100%;
            height: 100%; /* Fill parent nav height */
            padding: 0;
            margin: 0;
        }

        .nav__link {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: var(--nav-item-inactive-color); /* Uses theme variable */
            padding: 10px;
            border-radius: 50%; /* Make individual items circular */
            cursor: pointer;
            width: 50px; /* Fixed width for each item when expanded */
            height: 50px;
            flex-shrink: 0; /* Prevent shrinking */
            flex-grow: 0; /* Prevent growing initially */
            transition: color 0.3s ease, background-color 0.3s ease, width 0.3s ease, flex-grow 0.3s ease; /* Add width/flex-grow to transition */
        }

        .nav__link i {
            font-size: 1.6rem; /* Larger icon size */
            margin-bottom: 0;
        }

        .nav__link.active-link {
            background-color: var(--nav-item-active-bg); /* Uses theme variable */
            color: var(--nav-item-active-color); /* Uses theme variable */
        }

        /* Hover effect for inactive links: make them look like active links */
        .nav__link:not(.active-link):hover {
            background-color: var(--nav-item-active-bg);
            color: var(--nav-item-active-color);
        }

        .nav__link.active-link:hover {
            /* Keep active state on hover for active links, or add subtle effect */
            background-color: var(--nav-item-active-bg);
            color: var(--nav-item-active-color);
            transform: scale(1.05); /* Subtle scale for active item hover */
        }

        /* Special style for the central QR button */
        .nav__qr-button {
            background-color: var(--nav-add-button-bg); /* Uses theme variable */
            color: var(--nav-add-button-color); /* Uses theme variable */
            border-radius: 50%;
            width: 75px; /* Increased size */
            height: 75px; /* Increased size */
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2); /* Initial shadow */
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease; /* Add box-shadow to transition */
            margin-bottom: 0; /* Removed bottom margin */
            flex-shrink: 0; /* Prevent shrinking */
            flex-grow: 0; /* Prevent growing */
            animation: glowing-border 2s infinite ease-in-out; /* Apply the new animation */
        }

        .nav__qr-button svg { /* Style for the SVG inside the button */
            width: 60%; /* Adjust SVG size relative to button */
            height: 60%;
            object-fit: contain;
        }

        /* Keyframes for continuous glowing border animation */
        @keyframes glowing-border {
            0% {
                transform: scale(1);
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2),
                            0 0 0 0px var(--primary-accent); /* No initial glow */
            }
            50% {
                transform: scale(1.02); /* Slightly reduced scale up */
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25),
                            0 0 8px 3px var(--primary-accent); /* Reduced blur and spread */
            }
            100% {
                transform: scale(1);
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2),
                            0 0 0 0px var(--primary-accent); /* Glow shrinks back */
            }
        }

        /* Remove the animation from active-link as it's now applied directly to nav__qr-button */
        .nav__qr-button.active-link {
            /* No scale transformation, the glow is the main active indicator */
            transform: scale(1); /* Ensure no unintended scaling from other rules */
            /* Base shadow for the button, plus the initial state of the glow */
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2); /* Keep a base shadow, animation handles glow */
            /* Animation is now on .nav__qr-button */
        }

        /* Remove hover effect for the QR button */
        .nav__qr-button:hover {
            /* Keep its original colors on hover */
            background-color: var(--nav-add-button-bg);
            color: var(--nav-add-button-color);
            /* Do not reset transform or animation on hover if already active */
        }

        /* Styling for SVG icons in nav links */
        .nav__link svg {
            width: 1.6rem; /* Match font icon size */
            height: 1.6rem;
            fill: currentColor; /* Ensure SVG fill color changes with text color */
            margin-bottom: 0;
        }

        /* Styling for the new card image icon */
        .nav__link .card-icon-img {
            width: 1.6rem; /* Match other icon sizes */
            height: 1.6rem;
            object-fit: contain;
        }
    </style>
</head>
<body>
    <header class="app-header">
        <div class="app-logo">MIKO</div>
        <div class="header-top-row">
            <div class="header-profile-pic">
                <img id="profilePicture" src="https://placehold.co/40x40/FF6B35/FFFFFF?text=P" alt="Profile Picture">
            </div>
            <div class="header-location" id="locationInfoClickable">
                <div class="header-location-top-row">
                    <span class="header-location-label">Delivery location</span>
                    <!-- Removed the down-arrow-icon-small-wrapper SVG as per user request -->
                </div>
                <span class="header-location-name" id="locationDetails">
                    Fetching location...
                </span>
            </div>
            <!-- Container for the right-side icons -->
            <div class="header-right-icons">
                <!-- Notification Icon -->
                <div class="header-notification-icon" id="notificationIcon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="44" height="44" color="#000000" fill="none">
                        <defs />
                        <path fill="#141B34" d="M12.75,1.25 C13.233,1.25 13.776,1.333 14.221,1.624 C14.715,1.948 15,2.471 15,3.125 C15,3.589 14.859,4.069 14.629,4.484 C17.781,5.287 20.162,8.061 20.341,11.452 C20.35,11.624 20.356,11.789 20.363,11.948 C20.385,12.49 20.404,12.962 20.503,13.413 C20.62,13.953 20.837,14.377 21.278,14.708 C22.047,15.285 22.5,16.191 22.5,17.153 C22.5,18.534 21.413,19.75 19.95,19.75 L16.425,19.75 C16.077,21.462 14.564,22.75 12.75,22.75 C10.936,22.75 9.422,21.462 9.075,19.75 L5.55,19.75 C4.087,19.75 3,18.534 3,17.153 C3,16.26 3.39,15.415 4.062,14.837 L4.222,14.708 C4.663,14.377 4.88,13.953 4.997,13.413 C5.076,13.052 5.104,12.678 5.123,12.265 L5.137,11.948 C5.144,11.789 5.15,11.624 5.159,11.452 C5.338,8.061 7.719,5.287 10.871,4.484 C10.641,4.069 10.5,3.589 10.5,3.125 C10.5,2.471 10.785,1.948 11.279,1.624 C11.724,1.333 12.267,1.25 12.75,1.25 Z M12.75,5.75 C9.505,5.75 6.828,8.29 6.657,11.53 C6.651,11.658 6.645,11.793 6.64,11.934 C6.618,12.494 6.592,13.139 6.463,13.732 C6.293,14.518 5.928,15.304 5.122,15.908 C4.73,16.202 4.5,16.663 4.5,17.153 C4.5,17.768 4.976,18.25 5.55,18.25 L19.95,18.25 C20.524,18.25 21,17.768 21,17.153 C21,16.663 20.77,16.202 20.378,15.908 C19.572,15.304 19.207,14.518 19.037,13.732 C18.934,13.258 18.897,12.75 18.875,12.279 L18.86,11.934 C18.855,11.793 18.849,11.658 18.843,11.53 C18.672,8.29 15.995,5.75 12.75,5.75 Z M14.872,19.75 L10.628,19.75 C10.937,20.624 11.77,21.25 12.75,21.25 C13.73,21.25 14.563,20.624 14.872,19.75 Z M12.75,2.75 C12.405,2.75 12.198,2.815 12.1,2.879 C12.06,2.906 12.042,2.929 12.032,2.949 C12.02,2.97 12,3.021 12,3.125 C12,3.357 12.103,3.672 12.291,3.926 C12.485,4.187 12.663,4.25 12.75,4.25 C12.837,4.25 13.015,4.187 13.209,3.926 C13.397,3.672 13.5,3.357 13.5,3.125 C13.5,3.021 13.48,2.97 13.468,2.949 C13.458,2.929 13.44,2.906 13.4,2.879 C13.302,2.815 13.095,2.75 12.75,2.75 Z" />
                    </svg>
                </div>
                <!-- Small Search Icon (initially hidden) -->
                <div class="header-search-icon-small" id="smallSearchIcon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="44" height="44" color="#ffffff" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M1.74902 9.49219C1.74902 13.7622 5.22902 17.2422 9.49902 17.2422C11.3648 17.2422 13.0797 16.5778 14.4194 15.4734C14.4347 15.4921 14.4511 15.5102 14.4685 15.5276L15.9685 17.0276L15.9686 17.0277C15.5784 17.8566 15.725 18.8793 16.4084 19.5627L18.4484 21.6028C18.8784 22.0328 19.4484 22.2428 20.0184 22.2428V22.2528C20.5884 22.2528 21.1584 22.0328 21.5984 21.6028C22.4684 20.7328 22.4684 19.3127 21.5984 18.4427L19.5584 16.4028C18.8744 15.7267 17.8568 15.5821 17.0301 15.969L17.0285 15.9675L15.5285 14.4675C15.5113 14.4503 15.4934 14.4341 15.475 14.4189C16.5825 13.0783 17.249 11.3609 17.249 9.49219C17.249 5.22219 13.769 1.74219 9.49902 1.74219C5.22902 1.74219 1.74902 5.22219 1.74902 9.49219ZM3.24902 9.49219C3.24902 6.04219 6.04902 3.24219 9.49902 3.24219C12.949 3.24219 15.749 6.04219 15.749 9.49219C15.749 12.9422 12.949 15.7422 9.49902 15.7422C6.04902 15.7422 3.24902 12.9422 3.24902 9.49219ZM17.4584 18.4928L19.4984 20.5328C19.7784 20.8128 20.2484 20.8128 20.5384 20.5328C20.8184 20.2528 20.8184 19.7828 20.5384 19.4928L18.4984 17.4527C18.2184 17.1727 17.7484 17.1727 17.4584 17.4527C17.1784 17.7327 17.1784 18.2028 17.4584 18.4928Z" fill="#141B34" />
                    </svg>
                </div>
            </div>
        </div>
        <div class="header-bottom-row">
            <h1 class="header-greeting">What you'd like<br>to eat for today?</h1>
            <div class="header-search-bar">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="44" height="44" color="#ffffff" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M1.74902 9.49219C1.74902 13.7622 5.22902 17.2422 9.49902 17.2422C11.3648 17.2422 13.0797 16.5778 14.4194 15.4734C14.4347 15.4921 14.4511 15.5102 14.4685 15.5276L15.9685 17.0276L15.9686 17.0277C15.5784 17.8566 15.725 18.8793 16.4084 19.5627L18.4484 21.6028C18.8784 22.0328 19.4484 22.2428 20.0184 22.2428V22.2528C20.5884 22.2528 21.1584 22.0328 21.5984 21.6028C22.4684 20.7328 22.4684 19.3127 21.5984 18.4427L19.5584 16.4028C18.8744 15.7267 17.8568 15.5821 17.0301 15.969L17.0285 15.9675L15.5285 14.4675C15.5113 14.4503 15.4934 14.4341 15.475 14.4189C16.5825 13.0783 17.249 11.3609 17.249 9.49219C17.249 5.22219 13.769 1.74219 9.49902 1.74219C5.22902 1.74219 1.74902 5.22219 1.74902 9.49219ZM3.24902 9.49219C3.24902 6.04219 6.04902 3.24219 9.49902 3.24219C12.949 3.24219 15.749 6.04219 15.749 9.49219C15.749 12.9422 12.949 15.7422 9.49902 15.7422C6.04902 15.7422 3.24902 12.9422 3.24902 9.49219ZM17.4584 18.4928L19.4984 20.5328C19.7784 20.8128 20.2484 20.8128 20.5384 20.5328C20.8184 20.2528 20.8184 19.7828 20.5384 19.4928L18.4984 17.4527C18.2184 17.1727 17.7484 17.1727 17.4584 17.4527C17.1784 17.7327 17.1784 18.2028 17.4584 18.4928Z" fill="#141B34" />
                </svg>
                <input type="text" placeholder="Search menu, restaurant or craving">
                <i class="fas fa-sliders-h header-filter-icon"></i>
                <!-- Removed the header-reload-icon as per user request -->
            </div>
        </div>
    </header>

    <main class="main-content">
        <!-- Categories Section -->
        <section class="categories-section">
            <div class="section-header-container">
                <h2 id="categoriesHeading" class="actual-content-hidden">Categories</h2>
            </div>
            <div class="categories-grid-skeleton skeleton-container">
                <!-- Skeletons will be dynamically generated here -->
            </div>
            <div id="categoriesGrid" class="categories-grid actual-content-hidden">
                <!-- Categories will be dynamically loaded here -->
            </div>
        </section>

        <!-- Food Items Section (Popular Now) -->
        <section class="food-items-section">
            <!-- Removed the heading and heading skeleton for food items -->
            <div class="food-items-grid-skeleton skeleton-container">
                <!-- Skeletons will be dynamically generated here -->
            </div>
            <div id="foodItemsGrid" class="food-items-grid actual-content-hidden">
                <!-- Food items will be dynamically loaded here -->
            </div>
        </section>

        <!-- Offers Section -->
        <section class="offers-section">
            <div class="section-header-container">
                <h2 id="offersHeading" class="actual-content-hidden">#SpecialForYou</h2>
            </div>
            <div id="offersGridSkeleton" class="offers-grid-skeleton skeleton-container">
                <!-- Skeletons will be dynamically generated here -->
            </div>
            <div id="offersGrid" class="offers-grid actual-content-hidden">
                <!-- Offers will be dynamically loaded here -->
            </div>
        </section>

        <!-- New Restaurants Section -->
        <section class="restaurants-section">
            <div class="section-header-container">
                <h2 id="restaurantsHeading" class="actual-content-hidden">Nearby Restaurants</h2>
            </div>
            <div id="restaurantsGridSkeleton" class="restaurants-grid-skeleton skeleton-container">
                <!-- Skeletons will be dynamically generated here -->
            </div>
            <div id="restaurantsGrid" class="restaurants-grid actual-content-hidden">
                <!-- Restaurants will be dynamically loaded here -->
            </div>
        </section>
    </main>

    <!-- New Floating Bottom Navigation Bar -->
    <nav class="nav">
        <ul class="nav__list">
            <li>
                <a href="/home.html" class="nav__link active-link" id="homeNavItem">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="1.6rem" height="1.6rem" fill="currentColor">
                        <defs />
                        <path fill="currentColor" d="M9.526,2.049 C10.352,1.549 11.126,1.25 12,1.25 C12.874,1.25 13.648,1.549 14.474,2.049 C15.274,2.532 16.188,3.246 17.335,4.142 L18.373,4.952 C18.422,4.99 18.471,5.028 18.518,5.065 C19.894,6.138 20.784,6.832 21.267,7.825 C21.751,8.817 21.751,9.947 21.75,11.695 L21.75,14.028 C21.75,15.872 21.75,17.333 21.597,18.476 C21.44,19.652 21.108,20.603 20.36,21.354 C19.611,22.105 18.662,22.438 17.49,22.596 C16.35,22.75 14.894,22.75 13.057,22.75 L10.943,22.75 C9.106,22.75 7.65,22.75 6.511,22.596 C5.338,22.438 4.389,22.105 3.64,21.354 C2.892,20.603 2.561,19.652 2.403,18.476 C2.25,17.333 2.25,15.872 2.25,14.028 L2.25,11.88 C2.25,11.818 2.25,11.756 2.25,11.696 L2.25,11.696 C2.249,9.947 2.249,8.817 2.733,7.825 C3.216,6.832 4.106,6.138 5.482,5.065 C5.530,5.028 5.578,4.99 5.627,4.952 L6.665,4.142 C7.813,3.246 8.727,2.532 9.526,2.049 Z M10.302,3.332 C9.588,3.764 8.744,4.422 7.550,5.354 L6.550,6.134 C4.976,7.363 4.394,7.840 4.081,8.482 C3.768,9.124 3.750,9.879 3.750,11.880 L3.750,13.972 C3.750,15.885 3.752,17.245 3.890,18.277 C4.025,19.287 4.279,19.870 4.703,20.295 C5.126,20.720 5.706,20.974 6.711,21.110 C7.739,21.248 9.093,21.250 11.000,21.250 L13.000,21.250 C14.907,21.250 16.261,21.248 17.289,21.110 C18.295,20.974 18.874,20.720 19.297,20.295 C19.721,19.870 19.975,19.287 20.110,18.277 C20.249,17.245 20.250,15.885 20.250,13.972 L20.250,11.880 C20.250,9.879 20.232,9.124 19.919,8.482 C19.606,7.840 19.024,7.363 17.450,6.134 L16.450,5.354 C15.256,4.422 14.412,3.764 13.698,3.332 C13.001,2.911 12.499,2.750 12.000,2.750 C11.501,2.750 11.000,2.911 10.302,3.332 Z" />
                    </svg>
                </a>
            </li>

            <li>
                <a href="/cart.html" class="nav__link" id="cartNavItem">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="1.6rem" height="1.6rem" fill="currentColor">
                        <defs />
                        <path fill="currentColor" d="M1.75,2 C1.75,1.586 2.086,1.25 2.5,1.25 L3.438,1.25 C4.7,1.25 5.8,2.109 6.106,3.333 L6.11,3.347 L6.614,5.75 L19.448,5.75 C19.952,5.75 20.415,5.75 20.786,5.809 C21.201,5.875 21.633,6.033 21.939,6.454 C22.233,6.859 22.271,7.318 22.242,7.737 C22.215,8.132 22.117,8.618 22.006,9.17 L21.996,9.217 C21.591,11.231 21.192,13.131 20.253,14.5 C19.767,15.21 19.134,15.785 18.293,16.176 C17.46,16.562 16.461,16.75 15.263,16.75 L8.463,16.75 C7.643,16.753 6.921,17.380 6.776,18.250 L17.5,18.250 C18.743,18.250 19.750,19.257 19.750,20.500 C19.750,21.743 18.743,22.750 17.500,22.750 C16.257,22.750 15.250,21.743 15.250,20.500 C15.250,20.237 15.295,19.985 15.378,19.750 L12.622,19.750 C12.705,19.985 12.750,20.237 12.750,20.500 C12.750,21.743 11.743,22.750 10.500,22.750 C9.257,22.750 8.250,21.743 8.250,20.500 C8.250,20.237 8.295,19.985 8.378,19.750 L6.411,19.750 C5.741,19.750 5.250,19.193 5.250,18.571 C5.250,17.243 6.015,16.071 7.138,15.545 L5.290,6.742 C5.271,6.686 5.258,6.627 5.253,6.565 L4.648,3.685 C4.505,3.135 4.008,2.750 3.438,2.750 L2.500,2.750 C2.086,2.750 1.750,2.414 1.750,2.000 Z M8.880,15.250 L15.263,15.250 C16.309,15.250 17.079,15.085 17.661,14.815 C18.236,14.548 18.667,14.162 19.016,13.653 C19.747,12.585 20.104,11.019 20.526,8.921 C20.649,8.307 20.726,7.919 20.746,7.634 C20.755,7.499 20.748,7.422 20.740,7.379 C20.734,7.349 20.728,7.340 20.726,7.337 L20.723,7.335 C20.707,7.327 20.659,7.307 20.551,7.290 C20.312,7.252 19.970,7.250 19.394,7.250 L6.929,7.250 L8.609,15.250 L8.880,15.250 Z M9.750,20.500 C9.750,20.914 10.086,21.250 10.500,21.250 C10.914,21.250 11.250,20.914 11.250,20.500 C11.250,20.086 10.914,19.750 10.500,19.750 C10.086,19.750 9.750,20.086 9.750,20.500 Z M17.500,19.750 C17.086,19.750 16.750,20.086 16.750,20.500 C16.750,20.914 17.086,21.250 17.500,21.250 C17.914,21.250 18.250,20.914 18.250,20.500 C18.250,20.086 17.914,19.750 17.500,19.750 Z" />
                    </svg>
                </a>
            </li>

            <li>
                <a href="#Qr" class="nav__qr-button" id="qrButtonNavItem">
                    <svg width="232px" height="232px" viewBox="0 0 32 32" enable-background="new 0 0 32 32" version="1.1" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#ffffff" stroke="#ffffff"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"></g><g id="SVGRepo_iconCarrier"> <g id="Layer_1"></g> <g id="Layer_2"> <g> <polyline fill="none" points=" 30,6 30,2 26,2 " stroke="#ffffff" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"></polyline> <polyline fill="none" points=" 6,2 2,2 2,6 " stroke="#ffffff" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"></polyline> <polyline fill="none" points=" 2,26 2,30 6,30 " stroke="#ffffff" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"></polyline> <polyline fill="none" points=" 26,30 30,30 30,26 " stroke="#ffffff" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"></polyline> <rect fill="none" height="8" stroke="#ffffff" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2" width="8" x="5" y="5"></rect> <rect fill="none" height="8" stroke="#ffffff" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2" width="8" x="19" y="5"></rect> <rect fill="none" height="8" stroke="#ffffff" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2" width="8" x="5" y="19"></rect> <polyline fill="none" points=" 19,23 19,19 23,19 " stroke="#ffffff" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"></polyline> <polyline fill="none" points=" 27,23 27,27 23,27 " stroke="#ffffff" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"></polyline> <rect fill="none" height="2" stroke="#ffffff" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2" width="2" x="22" y="22"></rect> </g> </g> </g></svg>
                </a>
            </li>

            <li>
                <a href="/membership.html" class="nav__link" id="membershipNavItem">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="1.6rem" height="1.6rem" fill="currentColor">
                        <defs />
                        <path fill="currentColor" d="M10.453,3.25 C10.453,3.25 13.548,3.25 13.548,3.25 C15.387,3.25 16.831,3.25 17.969,3.379 C19.132,3.51 20.074,3.784 20.857,4.402 C21.092,4.587 21.309,4.792 21.507,5.014 C22.173,5.761 22.471,6.667 22.612,7.778 C22.750,8.857 22.750,10.223 22.750,11.947 L22.750,12.053 C22.750,13.777 22.750,15.143 22.612,16.222 C22.471,17.333 22.173,18.239 21.507,18.986 C21.309,19.208 21.092,19.413 20.857,19.598 C20.074,20.216 19.132,20.490 17.969,20.621 C16.831,20.750 15.387,20.750 13.547,20.750 L10.453,20.750 C8.613,20.750 7.169,20.750 6.031,20.621 C4.868,20.490 3.926,20.216 3.143,19.598 C2.908,19.413 2.691,19.208 2.493,18.986 C1.827,18.239 1.529,17.333 1.388,16.222 C1.250,15.143 1.250,13.777 1.250,12.053 L1.250,11.947 C1.250,10.223 1.250,8.857 1.388,7.778 C1.529,6.667 1.827,5.761 2.493,5.014 C2.691,4.792 2.908,4.587 3.143,4.402 C3.926,3.784 4.868,3.510 6.031,3.379 C7.169,3.250 8.613,3.250 10.452,3.250 L10.453,3.250 Z M2.765,9.750 C2.750,10.396 2.750,11.138 2.750,12.000 C2.750,13.789 2.751,15.059 2.876,16.032 C2.997,16.985 3.226,17.554 3.613,17.988 C3.752,18.144 3.905,18.289 4.072,18.420 C4.544,18.793 5.168,19.014 6.199,19.131 C7.244,19.249 8.603,19.250 10.500,19.250 L13.500,19.250 C15.397,19.250 16.757,19.249 17.801,19.131 C18.832,19.014 19.456,18.793 19.928,18.420 C20.095,18.289 20.248,18.144 20.387,17.988 C20.774,17.554 21.003,16.985 21.124,16.032 C21.249,15.059 21.250,13.789 21.250,12.000 C21.250,11.138 21.250,10.396 21.235,9.750 Z M4.072,5.579 C3.905,5.711 3.752,5.856 3.613,6.012 C3.226,6.446 2.997,7.015 2.876,7.968 C2.864,8.059 2.853,8.153 2.844,8.250 L21.156,8.250 C21.147,8.153 21.136,8.059 21.124,7.968 C21.003,7.015 20.774,6.446 20.387,6.012 C20.248,5.856 20.095,5.711 19.928,5.579 C19.456,5.207 18.832,4.986 17.801,4.869 C16.757,4.751 15.397,4.750 13.500,4.750 L10.500,4.750 C8.603,4.750 7.244,4.751 6.199,4.869 C5.168,4.986 4.544,5.207 4.072,5.579 Z M13.750,16.000 C13.750,15.586 14.086,15.250 14.500,15.250 L18.000,15.250 C18.414,15.250 18.750,15.586 18.750,16.000 C18.750,16.414 18.414,16.750 18.000,16.750 L14.500,16.750 C14.086,16.750 13.750,16.414 13.750,16.000 Z M10.000,15.250 L11.500,15.250 C11.914,15.250 12.250,15.586 12.250,16.000 C12.250,16.414 11.914,16.750 11.500,16.750 L10.000,16.750 C9.586,16.750 9.250,16.414 9.250,16.000 C9.250,15.586 9.586,15.250 10.000,15.250 Z" />
                    </svg>
                </a>
            </li>

            <li>
                <a href="/profile.html" class="nav__link" id="profileNavItem">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="1.6rem" height="1.6rem" fill="currentColor">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M12 14.25C8.55 14.25 5.75 17.05 5.75 20.5C5.75 20.91 5.41 21.25 5 21.25C4.59 21.25 4.25 20.91 4.25 20.5C4.25 17.3105 6.19168 14.5617 8.95394 13.3749C7.33129 12.3571 6.25 10.5522 6.25 8.5C6.25 5.33 8.83 2.75 12 2.75C15.17 2.75 17.75 5.33 17.75 8.5C17.75 10.5522 16.6687 12.3571 15.0461 13.3749C17.8083 14.5617 19.75 17.3105 19.75 20.5C19.75 20.91 19.41 21.25 19 21.25C18.59 21.25 18.25 20.91 18.25 20.5C18.25 17.05 15.45 14.25 12 14.25ZM12 12.75C9.66 12.75 7.75 10.84 7.75 8.5C7.75 6.16 9.66 4.25 12 4.25C14.34 4.25 16.25 6.16 16.25 8.5C16.25 10.84 14.34 12.75 12 12.75Z" fill="currentColor" />
                    </svg>
                </a>
            </li>
        </ul>
    </nav>

    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            const profilePicture = document.getElementById('profilePicture');
            const homeNavItem = document.getElementById('homeNavItem');
            const qrButtonNavItem = document.getElementById('qrButtonNavItem');
            const membershipNavItem = document.getElementById('membershipNavItem');
            const cartNavItem = document.getElementById('cartNavItem');
            const categoriesGrid = document.getElementById('categoriesGrid'); // Get the categories grid container
            const foodItemsGrid = document.getElementById('foodItemsGrid'); // Get the food items grid container
            const offersGrid = document.getElementById('offersGrid'); // Get the offers grid container
            const restaurantsGrid = document.getElementById('restaurantsGrid'); // Get the new restaurants grid container


            const appHeader = document.querySelector('.app-header'); // Get the app header element
            const headerGreeting = document.querySelector('.header-greeting');
            const headerSearchBar = document.querySelector('.header-search-bar');
            const notificationIcon = document.getElementById('notificationIcon');
            const smallSearchIcon = document.getElementById('smallSearchIcon');
            const headerBottomRow = document.querySelector('.header-bottom-row'); // Get the header-bottom-row element
            const locationDetails = document.getElementById('locationDetails');

            // Global variables for user's location
            let userLatitude = null;
            let userLongitude = null;
            let allOffers = []; // Global variable to store all fetched offers
            let currencySymbol = '$'; // Default currency symbol, will be fetched from DB

            // Default random location (Tokyo, Japan) for fallback
            const DEFAULT_LATITUDE = 35.6895;
            const DEFAULT_LONGITUDE = 139.6917;


            // Get skeleton containers
            const categoriesGridSkeleton = document.querySelector('.categories-grid-skeleton');
            const foodItemsGridSkeleton = document.querySelector('.food-items-grid-skeleton');
            const offersGridSkeleton = document.getElementById('offersGridSkeleton'); // Corrected ID selector
            const restaurantsGridSkeleton = document.getElementById('restaurantsGridSkeleton'); // Get the new restaurants skeleton grid


            // Get heading elements
            const categoriesHeading = document.getElementById('categoriesHeading');
            const offersHeading = document.getElementById('offersHeading');
            const restaurantsHeading = document.getElementById('restaurantsHeading'); // Get the new restaurants heading


            const allNavItems = [homeNavItem, cartNavItem, qrButtonNavItem, membershipNavItem, profileNavItem];

            // Placeholder for showMessageBox function (now empty as per user request)
            const showMessageBox = (message, options) => {
                // console.log("Message Box:", message, options); // Keep for debugging if needed
                // alert(message); // Removed alert as per user request
            };

            // Check authentication status using cookie-based system
            let skippedLogin = false;
            let loggedIn = false;
            let userSession = null;

            // Variable to store the name of the currently active category
            // Always default to 'Popular' as per user request
            let currentActiveCategoryName = 'Popular';

            // Check user authentication status from server cookies
            try {
                const response = await fetch('login.php?path=/api/check-user-status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                const data = await response.json();

                if (data.success) {
                    if (data.user_type === 'authenticated') {
                        loggedIn = true;
                        userSession = data.user;
                        console.log('✅ User is authenticated:', userSession.email);
                    } else if (data.user_type === 'guest') {
                        skippedLogin = true;
                        console.log('✅ User is in guest mode');
                    }
                }
            } catch (error) {
                console.error('Error checking authentication status:', error);
            }

            // Function to update the profile picture based on login status and fetched avatar
            const updateProfilePicture = async () => {
                const defaultAvatar = "https://res.cloudinary.com/dg3uhpunl/image/upload/v1753671944/avatars/default.webp";
                return new Promise(async (resolve) => {
                    if (loggedIn && userSession) {
                        try {
                            // Use avatar from user session or fetch from server
                            let profileImgSrc = userSession.avatar || defaultAvatar;
                            if (!userSession.avatar) {
                                const response = await fetch('/login.php', { method: 'GET' });
                                const data = await response.json();
                                profileImgSrc = (data.success && data.avatar) ? data.avatar : defaultAvatar;
                            }
                            profilePicture.src = profileImgSrc;
                            profilePicture.alt = "User Profile Picture";
                        } catch (error) {
                            profilePicture.src = defaultAvatar;
                            profilePicture.alt = "User Profile Picture";
                        }
                    } else {
                        profilePicture.src = defaultAvatar;
                        profilePicture.alt = "Guest Avatar";
                    }
                    resolve();
                });
            };



            // CATEGORY FOOD ITEMS CACHE FOR INSTANT SWITCHING
            const categoryFoodCache = new Map();
            let preloadingInProgress = false;

            // Categories will be loaded instantly from browser cache if preloaded from index.html

            // Preload all categories' food items for instant switching
            const preloadAllCategoriesFoodItems = async (categories) => {
                if (preloadingInProgress) return;
                preloadingInProgress = true;

                console.log(`🚀 Starting preload for ${categories.length} categories (using browser cache from index.html)...`);
                const preloadStartTime = performance.now();

                // Create parallel requests for all categories
                // OPTIMIZED: Smaller batch size to reduce server load and timeouts
                const batchSize = 2; // Reduced from 3 to 2 for better reliability
                const preloadPromises = [];

                for (let i = 0; i < categories.length; i += batchSize) {
                    const batch = categories.slice(i, i + batchSize);

                    const batchPromises = batch.map(async (category) => {
                        try {
                            // Remove AbortController to prevent "signal aborted" errors
                            const response = await fetch(`/home.php?action=getFoodItems&category=${encodeURIComponent(category.name)}`, {
                                headers: {
                                    'Cache-Control': 'max-age=600',
                                    'X-From-Home': 'true'
                                }
                            });

                            // Add a simple timeout using Promise.race instead
                            const timeoutPromise = new Promise((_, reject) =>
                                setTimeout(() => reject(new Error('Request timeout')), 15000)
                            );

                            const fetchPromise = response.ok ? response.json() : Promise.reject(new Error(`HTTP ${response.status}`));

                            // Use Promise.race to handle timeout
                            const foodItems = await Promise.race([fetchPromise, timeoutPromise]);

                            if (foodItems && Array.isArray(foodItems)) {
                                // Preload images for instant display
                                const imagePromises = foodItems.map(item => {
                                    if (item.image && item.image !== 'null') {
                                        return new Promise(resolve => {
                                            const img = new Image();
                                            img.onload = img.onerror = resolve;
                                            img.src = item.image;
                                        });
                                    }
                                    return Promise.resolve();
                                });

                                // Cache data immediately, images load in background
                                categoryFoodCache.set(category.name, foodItems);

                                // Wait for images to load
                                Promise.allSettled(imagePromises);

                                return { category: category.name, count: foodItems.length };
                            } else {
                                console.log(`⚠️ No items found for ${category.name}`);
                                return { category: category.name, count: 0 };
                            }
                    } catch (error) {
                        // More specific error handling
                        if (error.message === 'Request timeout') {
                            console.warn(`⏱️ Timeout preloading ${category.name} (15s limit)`);
                        } else if (error.message.includes('HTTP')) {
                            console.warn(`🌐 Network error preloading ${category.name}: ${error.message}`);
                        } else {
                            console.warn(`⚠️ Failed to preload ${category.name}: ${error.message}`);
                        }
                        return { category: category.name, error: error.message };
                    }
                });

                preloadPromises.push(...batchPromises);

                // Wait between batches to avoid overwhelming server
                if (i + batchSize < categories.length) {
                    await new Promise(resolve => setTimeout(resolve, 500)); // Increased from 200ms to 500ms
                }
            }

            // Execute all preload requests
            const results = await Promise.allSettled(preloadPromises);
            const loadTime = performance.now() - preloadStartTime;

                const successful = results.filter(r => r.status === 'fulfilled' && r.value && !r.value.error).length;
                console.log(`✅ Preloading complete: ${successful}/${categories.length} categories cached in ${loadTime.toFixed(0)}ms`);

                preloadingInProgress = false;
            };

            // Get cached food items or fetch if not cached
            const getCachedFoodItems = async (categoryName) => {
                if (categoryFoodCache.has(categoryName)) {
                    return categoryFoodCache.get(categoryName);
                }

                // If not cached, fetch it
                try {
                    const response = await fetch(`/home.php?action=getFoodItems&category=${encodeURIComponent(categoryName)}`);
                    if (response.ok) {
                        const foodItems = await response.json();
                        categoryFoodCache.set(categoryName, foodItems);
                        return foodItems;
                    }
                } catch (error) {
                    console.error(`Failed to fetch ${categoryName}:`, error);
                }
                return [];
            };

            // OPTIMIZED DISPLAY FUNCTIONS FOR SINGLE API RESPONSE

            // Display categories from single API response
            const displayCategories = (categories) => {
                const startTime = performance.now();

                // Sort and move active category to front
                categories.sort((a, b) => a.ranking - b.ranking);

                // ALWAYS ensure Popular is the active category if it exists
                const popularCategory = categories.find(c => c.name === 'Popular');
                if (popularCategory) {
                    currentActiveCategoryName = 'Popular';

                    // Move Popular to front
                    const popularIndex = categories.findIndex(c => c.name === 'Popular');
                    if (popularIndex > 0) {
                        const [popular] = categories.splice(popularIndex, 1);
                        categories.unshift(popular);
                    }
                } else if (categories.length > 0) {
                    // Fallback to first category if Popular doesn't exist
                    currentActiveCategoryName = categories[0].name;
                    console.log(`⚠️ Popular category not found, using: ${currentActiveCategoryName}`);
                }



                // Use DocumentFragment for fast rendering
                const fragment = document.createDocumentFragment();
                categories.forEach(category => {
                    const categoryElement = document.createElement('a');
                    categoryElement.href = `#category-${category.name.toLowerCase().replace(/\s/g, '-')}`;
                    categoryElement.className = `category-item ${category.name === currentActiveCategoryName ? 'active-category' : ''}`;
                    categoryElement.dataset.categoryName = category.name;
                    categoryElement.innerHTML = `<span class="emoji">${category.emoji}</span><span class="name">${category.name}</span>`;
                    fragment.appendChild(categoryElement);
                });

                categoriesGrid.innerHTML = '';
                categoriesGrid.appendChild(fragment);

                // Add click handlers
                addCategoryClickHandlers();

                console.log(`📂 Categories rendered in ${(performance.now() - startTime).toFixed(2)}ms`);
            };

            // Display food items with smooth transitions (no skeleton flash)
            const displayFoodItems = (foodItems, targetGrid) => {
                const startTime = performance.now();

                if (foodItems.length === 0) {
                    targetGrid.innerHTML = '<p style="text-align: center; width: 100%; color: var(--subtle-detail);">No food items found.</p>';
                    return;
                }

                // Add switching class for smooth transition
                targetGrid.classList.add('switching');

                const fragment = document.createDocumentFragment();
                foodItems.forEach(item => {
                    const foodItemCard = document.createElement('a');
                    foodItemCard.href = `#food-item-${item.id}`;
                    foodItemCard.classList.add('food-item-card');

                    const imageUrl = item.image && item.image !== 'null' ? item.image :
                        `https://placehold.co/150x120/FF6B35/FFFFFF?text=${encodeURIComponent(item.item_name)}&font=Montserrat`;

                    let priceContent = `<div class="food-item-price">${formatPrice(item.price)}</div>`;
                    if (item.original_price && item.original_price > item.price) {
                        priceContent = `
                            <span class="food-item-original-price">${formatPrice(item.original_price)}</span>
                            <div class="food-item-price">${formatPrice(item.price)}</div>
                        `;
                    }

                    const reviews = (Math.random() * (5.0 - 3.5) + 3.5).toFixed(1);
                    const distance = (Math.random() * (5.0 - 0.5) + 0.5).toFixed(1);

                    foodItemCard.innerHTML = `
                        <div class="food-item-content">
                            <img data-src="${imageUrl}" alt="${item.item_name}" class="food-item-image lazy-load"
                                 src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='150' height='120'%3E%3Crect width='100%25' height='100%25' fill='%23f0f0f0'/%3E%3C/svg%3E">
                            <div class="food-item-details">
                                <div class="food-item-name-wrapper">
                                    <div class="food-item-name">${item.item_name}</div>
                                </div>
                                <div class="food-item-reviews-wrapper">
                                    <svg class="star-icon-svg" viewBox="-2.1 -2.1 25.20 25.20" version="1.1" xmlns="http://www.w3.org/2000/svg">
                                        <g><path d="M60.556381,172.206 C60.1080307,172.639 59.9043306,173.263 60.0093306,173.875 L60.6865811,177.791 C60.8976313,179.01 59.9211306,180 58.8133798,180 C58.5214796,180 58.2201294,179.931 57.9282291,179.779 L54.3844766,177.93 C54.1072764,177.786 53.8038262,177.714 53.499326,177.714 C53.1958758,177.714 52.8924256,177.786 52.6152254,177.93 L49.0714729,179.779 C48.7795727,179.931 48.4782224,180 48.1863222,180 C47.0785715,180 46.1020708,179.01 46.3131209,177.791 L46.9903714,173.875 C47.0953715,173.263 46.8916713,172.639 46.443321,172.206 L43.575769,169.433 C42.4480682,168.342 43.0707186,166.441 44.6289197,166.216 L48.5916225,165.645 C49.211123,165.556 49.7466233,165.17 50.0227735,164.613 L51.7951748,161.051 C52.143775,160.35 52.8220755,160 53.499326,160 C54.1776265,160 54.855927,160.35 55.2045272,161.051 L56.9769285,164.613 C57.2530787,165.17 57.7885791,165.556 58.4080795,165.645 L62.3707823,166.216 C63.9289834,166.441 64.5516338,168.342 63.423933,169.433 L60.556381,172.206 Z" fill="#e1b105"></path></g>
                                    </svg>
                                    ${reviews} reviews • ${distance} km
                                </div>
                                <div class="food-item-price-row">
                                    <div class="food-item-price-section">${priceContent}</div>
                                </div>
                            </div>
                        </div>
                    `;

                    fragment.appendChild(foodItemCard);
                });

                // Smooth content replacement
                requestAnimationFrame(() => {
                    targetGrid.innerHTML = '';
                    targetGrid.appendChild(fragment);

                    // Remove switching class after content is updated
                    requestAnimationFrame(() => {
                        targetGrid.classList.remove('switching');
                    });
                });

                console.log(`🍽️ Food items rendered in ${(performance.now() - startTime).toFixed(2)}ms`);
            };

            // Show critical content
            const showCriticalContent = () => {
                categoriesGridSkeleton.classList.add('hidden');
                categoriesGrid.classList.remove('actual-content-hidden');
                categoriesGrid.classList.add('actual-content-visible');
                categoriesHeading.classList.remove('actual-content-hidden');
                categoriesHeading.classList.add('actual-content-visible');

                foodItemsGridSkeleton.classList.add('hidden');
                foodItemsGrid.classList.remove('actual-content-hidden');
                foodItemsGrid.classList.add('actual-content-visible');
            };

            // Display offers with transparent overlay
            const displayOffers = (offers) => {
                if (offers.length === 0) return;

                const fragment = document.createDocumentFragment();
                offers.forEach(offer => {
                    const offerElement = document.createElement('a');
                    offerElement.href = offer.link || '#';
                    offerElement.target = '_blank';
                    offerElement.className = 'offer-banner';

                    // Create discount text
                    let discountText = '';
                    if (offer.discount && offer.discount > 0) {
                        discountText = `${offer.discount}% OFF`;
                    } else if (offer.flat && offer.flat > 0) {
                        discountText = `${currencySymbol}${offer.flat}/- Off`;
                    }

                    // Create coupon text
                    const couponText = offer.code ? `Coupon: ${offer.code}` : '';

                    offerElement.innerHTML = `
                        <img src="${offer.banner}" alt="${offer.description || 'Special Offer'}" loading="lazy">
                        <div class="offer-overlay">
                            ${discountText ? `<div class="offer-discount">${discountText}</div>` : ''}
                            ${couponText ? `<div class="offer-coupon">${couponText}</div>` : ''}
                        </div>
                    `;
                    fragment.appendChild(offerElement);
                });

                offersGrid.innerHTML = '';
                offersGrid.appendChild(fragment);
            };

            // Display restaurants (simplified) - EXACT from old_home.html line 1997
            const displayRestaurants = (restaurants) => {
                if (restaurants.length === 0) return;

                const fragment = document.createDocumentFragment();
                restaurants.forEach(restaurant => {
                    const restaurantElement = document.createElement('div');
                    restaurantElement.className = 'restaurant-card';
                    restaurantElement.innerHTML = `
                        <img src="${restaurant.image || 'https://placehold.co/200x150/FF6B35/FFFFFF?text=' + encodeURIComponent(restaurant.name)}" alt="${restaurant.name}" loading="lazy">
                        <div class="restaurant-details">
                            <h3>${restaurant.name}</h3>
                            <p>${restaurant.description || 'Restaurant'}</p>
                            <div class="restaurant-meta">
                                <span>🏪 Restaurant</span>
                                <span>📍 Available</span>
                            </div>
                        </div>
                    `;
                    fragment.appendChild(restaurantElement);
                });

                restaurantsGrid.innerHTML = '';
                restaurantsGrid.appendChild(fragment);
            };

            // Calculate restaurant distance and delivery information
            const calculateRestaurantInfo = async (restaurant, element) => {
                try {
                    // Default values
                    let distance = '-•-';
                    let deliveryTime = '-•-';
                    let deliveryFee = '-•-';

                    // Try to get user location without geocoding dependency
                    let userLat = null;
                    let userLon = null;

                    // Use global variables if available
                    if (typeof userLatitude !== 'undefined' && typeof userLongitude !== 'undefined') {
                        userLat = userLatitude;
                        userLon = userLongitude;
                    }

                    // Check if restaurant has coordinates (data is flattened from backend)
                    let restaurantLat = null;
                    let restaurantLon = null;

                    // Try flattened structure first (from home.php)
                    if (restaurant.latitude && restaurant.longitude) {
                        restaurantLat = parseFloat(restaurant.latitude);
                        restaurantLon = parseFloat(restaurant.longitude);
                    }
                    // Fallback to nested structure (if any)
                    else if (restaurant.profile && restaurant.profile.latitude && restaurant.profile.longitude) {
                        restaurantLat = parseFloat(restaurant.profile.latitude);
                        restaurantLon = parseFloat(restaurant.profile.longitude);
                    }

                    console.log(`🔍 Restaurant ${restaurant.name}: lat=${restaurantLat}, lon=${restaurantLon}`);

                    // If we have both user and restaurant coordinates, calculate distance
                    if (userLat && userLon && restaurantLat && restaurantLon &&
                        !isNaN(restaurantLat) && !isNaN(restaurantLon)) {

                        const calculatedDistance = calculateDistance(userLat, userLon, restaurantLat, restaurantLon);
                        const distanceNum = parseFloat(calculatedDistance);

                        distance = `${distanceNum.toFixed(1)} km`;

                        // Calculate delivery time based on distance
                        const baseTime = 20; // Base delivery time in minutes
                        const timePerKm = 3; // Additional minutes per km
                        const estimatedTime = Math.round(baseTime + (distanceNum * timePerKm));
                        deliveryTime = `${estimatedTime} min`;

                        // Calculate delivery fee based on distance
                        let fee = 0;
                        if (distanceNum <= 2) {
                            fee = 2;
                        } else if (distanceNum <= 5) {
                            fee = 3;
                        } else {
                            fee = 5;
                        }
                        deliveryFee = `${currencySymbol}${fee}`;

                    } else {
                        // Generate realistic placeholder values for demo
                        const randomDistance = (Math.random() * 4 + 1).toFixed(1); // 1-5 km
                        const randomTime = Math.round(20 + (parseFloat(randomDistance) * 3));
                        const randomFee = parseFloat(randomDistance) <= 2 ? 2 : parseFloat(randomDistance) <= 5 ? 3 : 5;

                        distance = `${randomDistance} km`;
                        deliveryTime = `${randomTime} min`;
                        deliveryFee = `${currencySymbol}${randomFee}`;

                        console.log(`🎲 Generated demo values for ${restaurant.name} (no coordinates available)`);
                    }

                    // Update the restaurant card with calculated info
                    const distanceSpan = element.querySelector('.distance-info');
                    const timeSpan = element.querySelector('.delivery-time');
                    const feeSpan = element.querySelector('.delivery-fee');



                    if (distanceSpan) {
                        distanceSpan.textContent = `📍 ${distance}`;


                    }
                    if (timeSpan) {
                        timeSpan.textContent = `⏱️ ${deliveryTime}`;


                    }
                    if (feeSpan) {
                        feeSpan.textContent = `💰 ${deliveryFee}`;


                    }



                } catch (error) {
                    console.warn(`⚠️ Failed to calculate info for ${restaurant.name}:`, error);

                    // Fallback to demo values even on error
                    const randomDistance = (Math.random() * 4 + 1).toFixed(1);
                    const randomTime = Math.round(20 + (parseFloat(randomDistance) * 3));
                    const randomFee = parseFloat(randomDistance) <= 2 ? 2 : 3;

                    const distanceSpan = element.querySelector('.distance-info');
                    const timeSpan = element.querySelector('.delivery-time');
                    const feeSpan = element.querySelector('.delivery-fee');

                    if (distanceSpan) distanceSpan.textContent = `📍 ${randomDistance} km`;
                    if (timeSpan) timeSpan.textContent = `⏱️ ${randomTime} min`;
                    if (feeSpan) feeSpan.textContent = `💰 ${currencySymbol}${randomFee}`;
                }
            };

            // Show background content
            const showBackgroundContent = () => {
                offersGridSkeleton.classList.add('hidden');
                offersGrid.classList.remove('actual-content-hidden');
                offersGrid.classList.add('actual-content-visible');
                offersHeading.classList.remove('actual-content-hidden');
                offersHeading.classList.add('actual-content-visible');

                restaurantsGridSkeleton.classList.add('hidden');
                restaurantsGrid.classList.remove('actual-content-hidden');
                restaurantsGrid.classList.add('actual-content-visible');
                restaurantsHeading.classList.remove('actual-content-hidden');
                restaurantsHeading.classList.add('actual-content-visible');
            };

            // Add category click handlers
            const addCategoryClickHandlers = () => {
                categoriesGrid.querySelectorAll('.category-item').forEach(item => {
                    item.addEventListener('click', async (event) => {
                        event.preventDefault();
                        const clickedCategory = event.currentTarget.dataset.categoryName;

                        if (clickedCategory !== currentActiveCategoryName) {
                            currentActiveCategoryName = clickedCategory;

                            // Update active states
                            categoriesGrid.querySelectorAll('.category-item').forEach(el =>
                                el.classList.toggle('active-category', el.dataset.categoryName === clickedCategory)
                            );

                            // Move clicked item to first position
                            categoriesGrid.prepend(event.currentTarget);
                            categoriesGrid.scrollTo({ left: 0, behavior: 'smooth' });

                            // SEAMLESS category switching without skeleton flash
                            const switchStartTime = performance.now();

                            if (categoryFoodCache.has(clickedCategory)) {
                                // INSTANT: Use cached data with seamless transition
                                const cachedFoodItems = categoryFoodCache.get(clickedCategory);

                                // Use requestAnimationFrame for smooth transition
                                requestAnimationFrame(() => {
                                    displayFoodItems(cachedFoodItems, foodItemsGrid);

                                });
                            } else {
                                // For non-cached: Show skeleton only if loading takes time
                                let skeletonTimeout;

                                // Only show skeleton if loading takes more than 100ms
                                skeletonTimeout = setTimeout(() => {
                                    foodItemsGrid.classList.replace('actual-content-visible', 'actual-content-hidden');
                                    foodItemsGridSkeleton.style.display = 'flex';
                                }, 100);

                                const foodItems = await getCachedFoodItems(clickedCategory);

                                // Clear skeleton timeout if loading was fast
                                clearTimeout(skeletonTimeout);

                                // Update content
                                displayFoodItems(foodItems, foodItemsGrid);

                                // Hide skeleton if it was shown
                                foodItemsGridSkeleton.style.display = 'none';
                                foodItemsGrid.classList.replace('actual-content-hidden', 'actual-content-visible');


                            }
                        }
                    });
                });
            };

            // Fallback to individual API calls if single request fails
            const loadDataFallback = async () => {
                const allPromises = Promise.allSettled([
                    fetchCurrencySymbol(),
                    fetchCategoriesAndDisplay(),
                    updateProfilePicture(),
                    fetchAndDisplayFoodItems(currentActiveCategoryName, foodItemsGrid)
                ]);

                const [currencyResult, categoriesResult, profileResult, foodItemsResult] = await allPromises;

                if (categoriesResult.status === 'fulfilled') {
                    showCriticalContent();
                }
            };

            // Function to fetch currency symbol with timeout
            const fetchCurrencySymbol = async () => {
                const startTime = performance.now();
                try {
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 3000); // 3s timeout

                    const response = await fetch('/home.php?action=getCurrency', {
                        signal: controller.signal,
                        headers: { 'Cache-Control': 'max-age=300' } // 5min cache
                    });
                    clearTimeout(timeoutId);

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    const data = await response.json();
                    if (data.success && data.currency) {
                        currencySymbol = data.currency;
                        console.log(`💰 Currency loaded in ${(performance.now() - startTime).toFixed(2)}ms`);
                    } else {
                        console.warn("Failed to fetch currency symbol, using default.");
                    }
                } catch (error) {
                    console.error("Error fetching currency symbol:", error);
                    console.warn("Using default currency symbol.");
                }
            };

            // Function to format price with fetched currency symbol
            const formatPrice = (price) => {
                // Use toLocaleString with 'en-PK' locale for Pakistani Rupee formatting
                // This handles large numbers by adding commas as thousands separators
                // and now allows for 2 decimal places.
                return `${currencySymbol} ${price.toLocaleString('en-PK', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
            };

            // --- Distance Calculation Function (Haversine formula) ---
            const calculateDistance = (lat1, lon1, lat2, lon2) => {
                const R = 6371; // Radius of Earth in kilometers
                const dLat = (lat2 - lat1) * Math.PI / 180;
                const dLon = (lon2 - lon1) * Math.PI / 180;
                const a =
                    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                    Math.sin(dLon / 2) * Math.sin(dLon / 2);
                const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
                const distance = R * c; // Distance in kilometers
                return distance.toFixed(1); // Return distance rounded to 1 decimal place
            };

            // --- Estimated Delivery Time Calculation ---
            const calculateEstimatedDeliveryTime = (distanceKm) => {
                // Simple linear model: base time + time per km
                const BASE_TIME_MINUTES = 10; // Minimum delivery time
                const MINUTES_PER_KM = 3; // Minutes per kilometer
                const estimatedTime = BASE_TIME_MINUTES + (distanceKm * MINUTES_PER_KM);
                // Add some randomness for more realistic feel (e.g., +/- 5 minutes)
                const randomVariation = Math.floor(Math.random() * 11) - 5; // -5 to +5 minutes
                return Math.max(10, Math.round(estimatedTime + randomVariation)); // Ensure minimum 10 minutes
            };




            // Function to fetch and display food items based on category
            const fetchAndDisplayFoodItems = async (category, targetGrid) => {
                const foodItemImagePromises = []; // Local array for food item images
                try {
                    let foodItems = [];
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 8000); // 8s timeout

                    const response = await fetch(`/home.php?action=getFoodItems&category=${encodeURIComponent(category)}`, {
                        signal: controller.signal,
                        headers: { 'Cache-Control': 'max-age=300' } // 5min cache
                    });
                    clearTimeout(timeoutId);

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    foodItems = await response.json();
                    console.log(`🍽️ Food items for ${category} loaded: ${foodItems.length} items`);

                    targetGrid.innerHTML = ''; // Clear existing food items

                    if (foodItems.length === 0) {
                        targetGrid.innerHTML = '<p style="text-align: center; width: 100%; color: var(--subtle-detail);">No food items found for this category.</p>';
                        return foodItemImagePromises; // Return empty array if no items
                    }

                    // Create a DocumentFragment to minimize DOM manipulations
                    const fragment = document.createDocumentFragment();

                    foodItems.forEach(item => {
                        const foodItemCard = document.createElement('a');
                        foodItemCard.href = `#food-item-${item.id}`; // Example link
                        foodItemCard.classList.add('food-item-card');

                        const imageUrl = item.image && item.image !== 'null' ? item.image : `https://placehold.co/150x120/FF6B35/FFFFFF?text=${encodeURIComponent(item.item_name)}&font=Montserrat`;

                        let priceContent = `<div class="food-item-price">${formatPrice(item.price)}</div>`;
                        if (item.original_price && item.original_price > item.price) {
                            priceContent = `
                                <span class="food-item-original-price">${formatPrice(item.original_price)}</span>
                                <div class="food-item-price">${formatPrice(item.price)}</div>
                            `;
                        }

                        // Generate random reviews and distance for demo purposes
                        // Use actual average_rating and review_count from restaurant data (if available on food items)
                        // For food items, if no rating is provided, use a default or random for demo
                        const reviews = (Math.random() * (5.0 - 3.5) + 3.5).toFixed(1);
                        const distance = (Math.random() * (5.0 - 0.5) + 0.5).toFixed(1);

                        // Calculate discount percentage
                        let discountTag = '';
                        if (item.original_price && item.original_price > item.price) {
                            const discountAmount = item.original_price - item.price;
                            const discountPercentage = Math.round((discountAmount / item.original_price) * 100);
                            discountTag = `
                                <div class="discount-tag">
                                    <svg viewBox="0 0 24 24" fill="#ffffff" xmlns="http://www.w3.org/2000/svg" stroke="#000000" stroke-width="0.00024000000000000003">
                                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke="#0d0c0c" stroke-width="2.112"></g>
                                        <g id="SVGRepo_iconCarrier">
                                            <path d="M21.5289 10.8689L20.0089 9.34891C19.7489 9.08891 19.5389 8.57891 19.5389 8.21891V6.05891C19.5389 5.17891 18.8189 4.45891 17.9389 4.45891H15.7889C15.4289 4.45891 14.9189 4.24891 14.6589 3.98891L13.1389 2.46891C12.5189 1.84891 11.4989 1.84891 10.8789 2.46891L9.33891 3.98891C9.08891 4.24891 8.57891 4.45891 8.20891 4.45891H6.05891C5.17891 4.45891 4.45891 5.17891 4.45891 6.05891V8.20891C4.45891 8.56891 4.24891 9.07891 3.98891 9.33891L2.46891 10.8589C1.84891 11.4789 1.84891 12.4989 2.46891 13.1189L3.98891 14.6389C4.24891 14.8989 4.45891 15.4089 4.45891 15.7689V17.9189C4.45891 18.7989 5.17891 19.5189 6.05891 19.5189H8.20891C8.56891 19.5189 9.07891 19.7289 9.33891 19.9889L10.8589 21.5089C11.4789 22.1289 12.4989 22.1289 13.1189 21.5089L14.6389 19.9889C14.8989 19.7289 15.4089 19.5189 15.7689 19.5189H17.9189C18.7989 18.7989 19.5189 19.5189 19.5189 17.9189V15.7689C19.5189 15.4089 19.7289 14.8989 19.9889 14.6389L21.5089 13.1189C22.1589 12.5089 22.1589 11.4889 21.5289 10.8689ZM7.99891 8.99891C7.99891 8.44891 8.44891 7.99891 8.99891 7.99891C9.54891 7.99891 9.99891 8.44891 9.99891 8.99891C9.99891 9.54891 9.55891 9.99891 8.99891 9.99891C8.44891 9.99891 7.99891 9.54891 7.99891 8.99891ZM9.52891 15.5289C9.37891 15.6789 9.18891 15.7489 8.99891 15.7489C8.80891 15.7489 8.61891 15.6789 8.46891 15.5289C8.17891 15.2389 8.17891 14.7589 8.46891 14.4689L14.4689 8.46891C14.7589 8.17891 15.2389 8.17891 15.5289 8.46891C15.8189 8.75891 15.8189 9.23891 15.5289 9.52891L9.52891 15.5289ZM14.9989 15.9989C14.4389 15.9989 13.9889 15.5489 13.9889 14.9989C13.9889 14.4489 14.4389 13.9989 14.9889 13.9989C15.5389 13.9989 15.9889 14.4489 15.9889 14.9989C15.9889 15.5489 15.5489 15.9989 14.9989 15.9989Z" fill="#ffffff"></path>
                                        </g>
                                    </svg>
                                    ${discountPercentage}% OFF
                                </div>
                            `;
                        }

                        // Build the card structure with all elements in their respective divs
                        foodItemCard.innerHTML = `
                            ${discountTag}
                            <div class="food-item-content">
                                <img data-src="${imageUrl}" alt="${item.item_name}" class="food-item-image lazy-load" src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='150' height='120'%3E%3Crect width='100%25' height='100%25' fill='%23f0f0f0'/%3E%3C/svg%3E" onerror="this.onerror=null;this.src='https://placehold.co/150x120/FF6B35/FFFFFF?text=${encodeURIComponent(item.item_name)}&font=Montserrat';">
                                <div class="food-item-details">
                                    <div class="food-item-name-wrapper">
                                        <div class="food-item-name">${item.item_name}</div>
                                    </div>
                                    <div class="food-item-reviews-wrapper">
                                        <svg class="star-icon-svg" viewBox="-2.1 -2.1 25.20 25.20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#f45c1a" stroke="#f45c1a">
                                            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke="#ff0000" stroke-width="0.546"></g>
                                            <g id="SVGRepo_iconCarrier">
                                                <title>star_favorite [#e1b105]</title>
                                                <desc>Created with Sketch.</desc>
                                                <defs> </defs>
                                                <g id="Page-1" stroke-width="0.273" fill="none" fill-rule="evenodd">
                                                    <g id="Dribbble-Light-Preview" transform="translate(-99.000000, -320.000000)" fill="#e1b105">
                                                        <g id="icons" transform="translate(56.000000, 160.000000)">
                                                            <path d="M60.556381,172.206 C60.1080307,172.639 59.9043306,173.263 60.0093306,173.875 L60.6865811,177.791 C60.8976313,179.01 59.9211306,180 58.8133798,180 C58.5214796,180 58.2201294,179.931 57.9282291,179.779 L54.3844766,177.93 C54.1072764,177.786 53.8038262,177.714 53.499326,177.714 C53.1958758,177.714 52.8924256,177.786 52.6152254,177.93 L49.0714729,179.779 C48.7795727,179.931 48.4782224,180 48.1863222,180 C47.0785715,180 46.1020708,179.01 46.3131209,177.791 L46.9903714,173.875 C47.0953715,173.263 46.8916713,172.639 46.443321,172.206 L43.575769,169.433 C42.4480682,168.342 43.0707186,166.441 44.6289197,166.216 L48.5916225,165.645 C49.211123,165.556 49.7466233,165.17 50.0227735,164.613 L51.7951748,161.051 C52.143775,160.35 52.8220755,160 53.499326,160 C54.1776265,160 54.855927,160.35 55.2045272,161.051 L56.9769285,164.613 C57.2530787,165.17 57.7885791,165.556 58.4080795,165.645 L62.3707823,166.216 C63.9289834,166.441 64.5516338,168.342 63.423933,169.433 L60.556381,172.206 Z" id="star_favorite-[#e1b105]"> </path>
                                                        </g>
                                                    </g>
                                                </g>
                                            </svg>
                                        ${reviews} reviews &bull; ${distance} km
                                    </div>
                                    <div class="food-item-price-row">
                                        <div class="food-item-price-section">
                                            ${priceContent}
                                        </div>
                                        <div class="food-item-favorite-wrapper">
                                            <!-- Default (unliked) SVG icon -->
                                            <svg class="heart-icon-outline" viewBox="0 0 24.00 24.00" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#808080">
                                                <path d="M15.7 4C18.87 4 21 6.98 21 9.76C21 15.39 12.16 20 12 20C11.84 20 3 15.39 3 9.76C3 6.98 5.13 4 8.3 4C10.12 4 11.31 4.91 12 5.71C12.69 4.91 13.88 4 15.7 4Z" stroke="#808080" stroke-width="1.656" stroke-linecap="round" stroke-linejoin="round"></path>
                                            </svg>
                                            <!-- Liked (filled) SVG icon - updated -->
                                            <svg class="heart-icon-filled" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="display: none;">
                                                <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                                <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke="#ffffff" stroke-width="0.048"></g>
                                                <g id="SVGRepo_iconCarrier">
                                                    <path d="M2 9.1371C2 14 6.01943 16.5914 8.96173 18.9109C10 19.7294 11 20.5 12 20.5C13 20.5 14 19.7294 15.0383 18.9109C17.9806 16.5914 22 14 22 9.1371C22 4.27416 16.4998 0.825464 12 5.50063C7.50016 0.825464 2 4.27416 2 9.1371Z" fill="#ff0000"></path>
                                                </g>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;

                        // OPTIMIZED: Batch DOM queries and event handling
                        fragment.appendChild(foodItemCard);

                        // Defer image loading and event handling until after DOM insertion
                        requestAnimationFrame(() => {
                            const imgElement = foodItemCard.querySelector('.food-item-image');
                            const imgLoadPromise = new Promise((resolve) => {
                                if (imgElement.complete) {
                                    resolve();
                                } else {
                                    imgElement.onload = imgElement.onerror = resolve;
                                }
                            });
                            foodItemImagePromises.push(imgLoadPromise);

                            // Optimized event delegation for heart icons
                            const heartWrapper = foodItemCard.querySelector('.food-item-favorite-wrapper');
                            heartWrapper.addEventListener('click', (e) => {
                                e.preventDefault();
                                e.stopPropagation();

                                const isLiked = heartWrapper.classList.toggle('liked');
                                const outlineIcon = heartWrapper.querySelector('.heart-icon-outline');
                                const filledIcon = heartWrapper.querySelector('.heart-icon-filled');

                                outlineIcon.style.display = isLiked ? 'none' : 'block';
                                filledIcon.style.display = isLiked ? 'block' : 'none';
                            });
                        });
                    });
                    targetGrid.appendChild(fragment); // Append the fragment to the DOM once
                    return foodItemImagePromises; // Return the promises for these specific images
                } catch (error) {
                    console.error("Error fetching food items:", error);
                    targetGrid.innerHTML = '<p style="text-align: center; width: 100%; color: var(--subtle-detail);">No food items found for this category.</p>';
                    return foodItemImagePromises; // Return even on error
                }
            };

            // Function to fetch and display categories with optimizations
            const fetchCategoriesAndDisplay = async () => {
                const startTime = performance.now();
                try {
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5s timeout

                    const response = await fetch('/home.php?action=getCategories', {
                        signal: controller.signal,
                        headers: { 'Cache-Control': 'max-age=600' } // 10min cache
                    });
                    clearTimeout(timeoutId);

                    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

                    let categories = await response.json();
                    console.log(`📂 Categories loaded in ${(performance.now() - startTime).toFixed(2)}ms`);
                    categories.sort((a, b) => a.ranking - b.ranking);

                    // Move active category to front
                    const activeIndex = categories.findIndex(c => c.name === currentActiveCategoryName);
                    if (activeIndex > 0) {
                        const [activeCategory] = categories.splice(activeIndex, 1);
                        categories.unshift(activeCategory);
                    } else if (activeIndex === -1 && categories.length > 0) {
                        currentActiveCategoryName = categories[0].name;
                    }

                    // OPTIMIZED: Use DocumentFragment for faster DOM updates
                    const fragment = document.createDocumentFragment();
                    categories.forEach(category => {
                        const categoryElement = document.createElement('a');
                        categoryElement.href = `#category-${category.name.toLowerCase().replace(/\s/g, '-')}`;
                        categoryElement.className = `category-item ${category.name === currentActiveCategoryName ? 'active-category' : ''}`;
                        categoryElement.dataset.categoryName = category.name;
                        categoryElement.innerHTML = `<span class="emoji">${category.emoji}</span><span class="name">${category.name}</span>`;
                        fragment.appendChild(categoryElement);
                    });

                    // Single DOM update
                    categoriesGrid.innerHTML = '';
                    categoriesGrid.appendChild(fragment);

                    // Add click handlers to all category items
                    categoriesGrid.querySelectorAll('.category-item').forEach(item => {
                        item.addEventListener('click', async (event) => {
                            event.preventDefault();
                            const clickedCategory = event.currentTarget.dataset.categoryName;

                            if (clickedCategory !== currentActiveCategoryName) {
                                currentActiveCategoryName = clickedCategory;

                                // Update active states
                                categoriesGrid.querySelectorAll('.category-item').forEach(el =>
                                    el.classList.toggle('active-category', el.dataset.categoryName === clickedCategory)
                                );

                                // Move clicked item to first position
                                categoriesGrid.prepend(event.currentTarget);
                                categoriesGrid.scrollTo({ left: 0, behavior: 'smooth' });

                                // Update food items
                                foodItemsGrid.classList.replace('actual-content-visible', 'actual-content-hidden');
                                foodItemsGridSkeleton.style.display = 'flex';

                                await fetchAndDisplayFoodItems(currentActiveCategoryName, foodItemsGrid);

                                foodItemsGridSkeleton.style.display = 'none';
                                foodItemsGrid.classList.replace('actual-content-hidden', 'actual-content-visible');
                            }
                        });
                    });
                } catch (error) {
                    console.error("Error fetching categories:", error);
                    categoriesGrid.innerHTML = '<p>Failed to load categories. Please try again later.</p>';
                }
            };

            // Function to fetch and display offers
            const fetchAndDisplayOffers = async (targetGrid, targetSkeleton, targetHeading) => { // Removed targetHeadingSkeleton
                const offerImagePromises = []; // Local array for offer images
                try {
                    const response = await fetch('/home.php?action=getOffers'); // Assuming a new endpoint for offers
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    allOffers = await response.json(); // Store all offers globally

                    // Clear existing offers for the single grid
                    targetGrid.innerHTML = '';

                    if (allOffers.length === 0) {
                        targetGrid.innerHTML = '<p style="text-align: center; width: 100%; color: var(--subtle-detail);">No offers available at the moment.</p>';
                        return offerImagePromises; // Return empty array if no items
                    }

                    // Create a DocumentFragment for offers
                    const fragment = document.createDocumentFragment();
                    allOffers.forEach(offer => {
                        // Only display offers that have a banner and link for the main offers section
                        if (offer.banner && offer.banner !== 'null' && offer.link) {
                            const offerBanner = document.createElement('a');
                            offerBanner.href = offer.link; // Use the link from the offer data
                            offerBanner.target = "_blank"; // Open in new tab
                            offerBanner.classList.add('offer-banner');

                            const bannerImageUrl = offer.banner;

                            // Create an image element and a promise for its load
                            const imgElement = new Image();
                            imgElement.src = bannerImageUrl;
                            imgElement.alt = "Offer Banner";
                            imgElement.onerror = function() {
                                this.src = `https://placehold.co/300x150/FF6B35/FFFFFF?text=Offer&font=Montserrat`;
                            };

                            const imgLoadPromise = new Promise((resolve) => {
                                imgElement.onload = resolve;
                                imgElement.onerror = resolve; // Resolve even on error
                            });
                            offerImagePromises.push(imgLoadPromise); // Push to the local array

                            let discountText = '';
                            if (offer.flat) {
                                discountText = `${currencySymbol} ${parseFloat(offer.flat).toFixed(2)} OFF`;
                            } else if (offer.discount) {
                                discountText = `${offer.discount}% OFF`;
                            }

                            offerBanner.innerHTML = `
                                <div class="overlay">
                                    <div class="discount-text">${discountText}</div>
                                    <div class="code-text">Code: ${offer.code}</div>
                                </div>
                            `;
                            offerBanner.prepend(imgElement); // Prepend the image element
                            fragment.appendChild(offerBanner); // Append to fragment
                        }
                    });
                    targetGrid.appendChild(fragment); // Append the fragment to the DOM once
                    return offerImagePromises; // Return the promises for these specific images
                } catch (error) {
                    console.error("Error fetching offers:", error);
                    targetGrid.innerHTML = '<p style="text-align: center; width: 100%; color: var(--subtle-detail);">No offers available at the moment.</p>';
                    return offerImagePromises; // Return even on error
                }
            };

            // Function to fetch and display restaurants
            const fetchAndDisplayRestaurants = async () => {
                const restaurantImagePromises = [];
                try {
                    // Show skeleton and hide actual content before fetching
                    restaurantsGridSkeleton.classList.remove('hidden');
                    restaurantsGrid.classList.add('actual-content-hidden');
                    restaurantsHeading.classList.add('actual-content-hidden');

                    // userLatitude and userLongitude are guaranteed to be set by getUserLocation
                    // either to actual coordinates or to DEFAULT_LATITUDE/DEFAULT_LONGITUDE.
                    // No need for an explicit check here.

                    const response = await fetch('/home.php?action=getRestaurants');
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    const restaurants = await response.json();

                    restaurantsGrid.innerHTML = ''; // Clear existing content

                    if (restaurants.length === 0) {
                        restaurantsGrid.innerHTML = '<p style="text-align: center; width: 100%; color: var(--subtle-detail);">No restaurants found.</p>';
                        // Hide skeleton and show message immediately if no restaurants
                        restaurantsGridSkeleton.classList.add('hidden');
                        restaurantsGrid.classList.remove('actual-content-hidden');
                        restaurantsGrid.classList.add('actual-content-visible');
                        restaurantsHeading.classList.remove('actual-content-hidden');
                        restaurantsHeading.classList.add('actual-content-visible');
                        return restaurantImagePromises;
                    }

                    const fragment = document.createDocumentFragment();

                    restaurants.forEach(restaurant => {
                        // Check if restaurant has valid coordinates
                        if (restaurant.latitude && restaurant.longitude) {
                            const restaurantLat = parseFloat(restaurant.latitude);
                            const restaurantLon = parseFloat(restaurant.longitude);

                            // Calculate distance using user's (or default) location
                            // Ensure distance is a float for accurate multiplication
                            let distance = parseFloat(calculateDistance(userLatitude, userLongitude, restaurantLat, restaurantLon));
                            let displayDistance;

                            // Convert to meters if distance is less than 1 km
                            if (distance < 1) {
                                displayDistance = `${Math.round(distance * 1000)} m away`;
                            } else {
                                displayDistance = `${distance.toFixed(1)} km away`;
                            }

                            // Calculate estimated delivery time based on distance
                            const estimatedDeliveryTime = calculateEstimatedDeliveryTime(distance);

                            // Calculate total delivery price based on distance and delivery_fee_per_km
                            // Ensure deliveryFeePerKm is a float
                            const deliveryFeePerKm = parseFloat(restaurant.delivery_fee); // This is the rate per km
                            let totalDeliveryPrice = 0;
                            let deliveryPriceText = '';

                            if (deliveryFeePerKm === 0 || isNaN(deliveryFeePerKm) || distance === 0) {
                                deliveryPriceText = 'Free delivery';
                            } else {
                                totalDeliveryPrice = deliveryFeePerKm * distance; // Direct multiplication
                                deliveryPriceText = formatPrice(totalDeliveryPrice) + ' delivery';
                            }

                            // Use actual average_rating and review_count from restaurant data
                            const rating = restaurant.average_rating ? parseFloat(restaurant.average_rating).toFixed(1) : 'N/A';
                            const reviewCount = restaurant.review_count ? parseInt(restaurant.review_count) : 0;

                            // Find ALL matching offers for the current restaurant, or "all" offers
                            const matchingOffers = allOffers.filter(offer =>
                                (offer.restaurant_name && offer.restaurant_name.toLowerCase() === restaurant.name.toLowerCase()) ||
                                (offer.restaurant_name && offer.restaurant_name.toLowerCase() === 'all')
                            );

                            let couponTagsHtml = ''; // Initialize as empty string
                            if (matchingOffers.length > 0) {
                                matchingOffers.forEach(offer => {
                                    let couponText = '';
                                    if (offer.flat) {
                                        couponText = `${currencySymbol}${parseFloat(offer.flat).toFixed(0)}`;
                                    } else if (offer.discount) {
                                        couponText = `${offer.discount}%`;
                                    }
                                    if (couponText && offer.code) {
                                        couponTagsHtml += `
                                            <div class="restaurant-coupon-tag">
                                                <svg viewBox="0 0 24 24" fill="#ff0000" xmlns="http://www.w3.org/2000/svg" stroke="#ff0000" stroke-width="0.00024000000000000003">
                                                    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke="#0d0c0c" stroke-width="2.112"></g>
                                                    <g id="SVGRepo_iconCarrier">
                                                        <path d="M21.5289 10.8689L20.0089 9.34891C19.7489 9.08891 19.5389 8.57891 19.5389 8.21891V6.05891C19.5389 5.17891 18.8189 4.45891 17.9389 4.45891H15.7889C15.4289 4.45891 14.9189 4.24891 14.6589 3.98891L13.1389 2.46891C12.5189 1.84891 11.4989 1.84891 10.8789 2.46891L9.33891 3.98891C9.08891 4.24891 8.57891 4.45891 8.20891 4.45891H6.05891C5.17891 4.45891 4.45891 5.17891 4.45891 6.05891V8.20891C4.45891 8.56891 4.24891 9.07891 3.98891 9.33891L2.46891 10.8589C1.84891 11.4789 1.84891 12.4989 2.46891 13.1189L3.98891 14.6389C4.24891 14.8989 4.45891 15.4089 4.45891 15.7689V17.9189C4.45891 18.7989 5.17891 19.5189 6.05891 19.5189H8.20891C8.56891 19.5189 9.07891 19.7289 9.33891 19.9889L10.8589 21.5089C11.4789 22.1289 12.4989 22.1289 13.1189 21.5089L14.6389 19.9889C14.8989 19.7289 15.4089 19.5189 15.7689 19.5189H17.9189C18.7989 18.7989 19.5189 19.5189 19.5189 17.9189V15.7689C19.5189 15.4089 19.7289 14.8989 19.9889 14.6389L21.5089 13.1189C22.1589 12.5089 22.1589 11.4889 21.5289 10.8689ZM7.99891 8.99891C7.99891 8.44891 8.44891 7.99891 8.99891 7.99891C9.54891 7.99891 9.99891 8.44891 9.99891 8.99891C9.99891 9.54891 9.55891 9.99891 8.99891 9.99891C8.44891 9.99891 7.99891 9.54891 7.99891 8.99891ZM9.52891 15.5289C9.37891 15.6789 9.18891 15.7489 8.99891 15.7489C8.80891 15.7489 8.61891 15.6789 8.46891 15.5289C8.17891 15.2389 8.17891 14.7589 8.46891 14.4689L14.4689 8.46891C14.7589 8.17891 15.2389 8.17891 15.5289 8.46891C15.8189 8.75891 15.8189 9.23891 15.5289 9.52891L9.52891 15.5289ZM14.9989 15.9989C14.4389 15.9989 13.9889 15.5489 13.9889 14.9989C13.9889 14.4489 14.4389 13.9989 14.9889 13.9989C15.5389 13.9989 15.9889 14.4489 15.9889 14.9989C15.9889 15.5489 15.5489 15.9989 14.9989 15.9989Z" fill="#ff0000"></path>
                                                    </g>
                                                </svg>
                                                ${couponText}: ${offer.code}
                                            </div>
                                        `;
                                    }
                                });
                            }


                            const restaurantCard = document.createElement('a');
                            restaurantCard.href = `#restaurant-${restaurant.id}`; // Example link
                            restaurantCard.classList.add('restaurant-card');

                            const imageUrl = restaurant.image && restaurant.image !== 'null' ? restaurant.image : `https://placehold.co/300x180/FF6B35/FFFFFF?text=${encodeURIComponent(restaurant.name)}&font=Montserrat`;

                            restaurantCard.innerHTML = `
                                <div class="restaurant-image-wrapper">
                                    <img data-src="${imageUrl}" alt="${restaurant.name}" class="restaurant-image lazy-load" src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='180'%3E%3Crect width='100%25' height='100%25' fill='%23f0f0f0'/%3E%3C/svg%3E" onerror="this.onerror=null;this.src='https://placehold.co/300x180/FF6B35/FFFFFF?text=${encodeURIComponent(restaurant.name)}&font=Montserrat';">
                                    <div class="restaurant-favorite-wrapper">
                                        <!-- Default (unliked) SVG icon -->
                                        <svg class="heart-icon-outline" viewBox="0 0 24.00 24.00" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#808080">
                                            <path d="M15.7 4C18.87 4 21 6.98 21 9.76C21 15.39 12.16 20 12 20C11.84 20 3 15.39 3 9.76C3 6.98 5.13 4 8.3 4C10.12 4 11.31 4.91 12 5.71C12.69 4.91 13.88 4 15.7 4Z" stroke="#808080" stroke-width="1.656" stroke-linecap="round" stroke-linejoin="round"></path>
                                        </svg>
                                        <!-- Liked (filled) SVG icon - updated -->
                                        <svg class="heart-icon-filled" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="display: none;">
                                            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke="#ffffff" stroke-width="0.048"></g>
                                            <g id="SVGRepo_iconCarrier">
                                                <path d="M2 9.1371C2 14 6.01943 16.5914 8.96173 18.9109C10 19.7294 11 20.5 12 20.5C13 20.5 14 19.7294 15.0383 18.9109C17.9806 16.5914 22 14 22 9.1371C22 4.27416 16.4998 0.825464 12 5.50063C7.50016 0.825464 2 4.27416 2 9.1371Z" fill="#ff0000"></path>
                                            </g>
                                        </svg>
                                    </div>
                                </div>
                                <div class="restaurant-details">
                                    <div class="restaurant-details-main">
                                        <div class="restaurant-name-rating-row">
                                            <div class="restaurant-name">${restaurant.name}</div>
                                            <div class="restaurant-rating">
                                                <svg class="star-icon-svg" viewBox="-2.1 -2.1 25.20 25.20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#f45c1a" stroke="#f45c1a">
                                                    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke="#ff0000" stroke-width="0.546"></g>
                                                    <g id="SVGRepo_iconCarrier">
                                                        <title>star_favorite [#e1b105]</title>
                                                        <desc>Created with Sketch.</desc>
                                                        <defs> </defs>
                                                        <g id="Page-1" stroke-width="0.273" fill="none" fill-rule="evenodd">
                                                            <g id="Dribbble-Light-Preview" transform="translate(-99.000000, -320.000000)" fill="#e1b105">
                                                                <g id="icons" transform="translate(56.000000, 160.000000)">
                                                                    <path d="M60.556381,172.206 C60.1080307,172.639 59.9043306,173.263 60.0093306,173.875 L60.6865811,177.791 C60.8976313,179.01 59.9211306,180 58.8133798,180 C58.5214796,180 58.2201294,179.931 57.9282291,179.779 L54.3844766,177.93 C54.1072764,177.786 53.8038262,177.714 53.499326,177.714 C53.1958758,177.714 52.8924256,177.786 52.6152254,177.93 L49.0714729,179.779 C48.7795727,179.931 48.4782224,180 48.1863222,180 C47.0785715,180 46.1020708,179.01 46.3131209,177.791 L46.9903714,173.875 C47.0953715,173.263 46.8916713,172.639 46.443321,172.206 L43.575769,169.433 C42.4480682,168.342 43.0707186,166.441 44.6289197,166.216 L48.5916225,165.645 C49.211123,165.556 49.7466233,165.17 50.0227735,164.613 L51.7951748,161.051 C52.143775,160.35 52.8220755,160 53.499326,160 C54.1776265,160 54.855927,160.35 55.2045272,161.051 L56.9769285,164.613 C57.2530787,165.17 57.7885791,165.556 58.4080795,165.645 L62.3707823,166.216 C63.9289834,166.441 64.5516338,168.342 63.423933,169.433 L60.556381,172.206 Z" id="star_favorite-[#e1b105]"> </path>
                                                                </g>
                                                            </g>
                                                        </g>
                                                    </svg>
                                                ${rating} <span class="review-count">(${reviewCount})</span>
                                            </div>
                                        </div>
                                        <!-- First line: Time and Cuisine Type -->
                                        <div class="restaurant-time-cuisine-row">
                                            <div class="restaurant-delivery-time">
                                                <!-- Replaced time icon SVG -->
                                                <svg fill="#949494" width="16px" height="16px" viewBox="0 0 24.00 24.00" id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" stroke="#949494" stroke-width="0.00024000000000000003"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M12,0A12,12,0,1,0,24,12,12.013,12.013,0,0,0,12,0Zm0,22A10,10,0,1,1,22,12,10.011,10.011,0,0,1,12,22Zm2-10a2,2,0,1,1-3-1.723V7a1,1,0,0,1,2,0v3.277A1.994,1.994,0,0,1,14,12Z"></path></g></svg>
                                                Est. ${estimatedDeliveryTime} min
                                            </div>
                                            <span class="dot-separator">&bull;</span>
                                            <div class="restaurant-cuisine-type">
                                                ${restaurant.cuisine_type || 'Various'}
                                            </div>
                                        </div>
                                        <!-- Second line: Distance and Delivery Charges -->
                                        <div class="restaurant-distance-delivery-row">
                                            <div class="restaurant-distance-info">
                                                <!-- Replaced distance icon SVG -->
                                                <svg class="distance-icon-svg" viewBox="-3 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="currentColor">
                                                    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                                                    <g id="SVGRepo_iconCarrier">
                                                        <title>pin_rounded_circle [#9c9c9c]</title>
                                                        <desc>Created with Sketch.</desc>
                                                        <defs> </defs>
                                                        <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                            <g id="Dribbble-Light-Preview" transform="translate(-423.000000, -5439.000000)" fill="currentColor">
                                                                <g id="icons" transform="translate(56.000000, 160.000000)">
                                                                    <path d="M376,5286.219 C376,5287.324 375.105,5288.219 374,5288.219 C372.895,5288.219 372,5287.324 372,5286.219 C372,5285.114 372.895,5284.219 374,5284.219 C375.105,5284.219 376,5285.114 376,5286.219 M374,5297 C372.178,5297 369,5290.01 369,5286 C369,5283.243 371.243,5281 374,5281 C376.757,5281 379,5283.243 379,5286 C379,5290.01 375.822,5297 374,5297 M374,5279 C370.134,5279 367,5282.134 367,5286 C367,5289.866 370.134,5299 374,5299 C377.866,5299 381,5289.866 381,5286 C381,5282.134 377.866,5279 374,5279" id="pin_rounded_circle-[#9c9c9c]"> </path>
                                                                </g>
                                                            </g>
                                                        </g>
                                                    </g>
                                                </svg>
                                                ${displayDistance}
                                            </div>
                                            <span class="dash-separator">-</span>
                                            <div class="restaurant-delivery-charges">
                                                <!-- Replaced delivery price icon SVG -->
                                                <svg class="delivery-icon-svg" width="16px" height="16px" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                                                    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                                                    <g id="SVGRepo_iconCarrier">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M12 2.75C6.89137 2.75 2.75 6.89137 2.75 12C2.75 17.1086 6.89137 21.25 12 21.25C17.1086 21.25 21.25 17.1086 21.25 12C21.25 6.89137 17.1086 2.75 12 2.75ZM1.25 12C1.25 6.06294 6.06294 1.25 12 1.25C17.9371 1.25 22.75 6.06294 22.75 12C22.75 17.9371 17.9371 22.75 12 22.75C6.06294 22.75 1.25 17.9371 1.25 12ZM12 5.25C12.4142 5.25 12.75 5.58579 12.75 6V6.31673C14.3804 6.60867 15.75 7.83361 15.75 9.5C15.75 9.91421 15.4142 10.25 15 10.25C14.5858 10.25 14.25 9.91421 14.25 9.5C14.25 8.65573 13.3765 7.75 12 7.75C10.6235 7.75 9.75 8.65573 9.75 9.5C9.75 10.3443 10.6235 11.25 12 11.25C13.9372 11.25 15.75 12.5828 15.75 14.5C15.75 16.1664 14.3804 17.3913 12.75 17.6833V18C12.75 18.4142 12.4142 18.75 12 18.75C11.5858 18.75 11.25 18.4142 11.25 18V17.6833C9.61957 17.3913 8.25 16.1664 8.25 14.5C8.25 14.0858 8.58579 13.75 9 13.75C9.41421 13.75 9.75 14.0858 9.75 14.5C9.75 15.3443 10.6235 16.25 12 16.25C13.3765 16.25 14.25 15.3443 14.25 14.5C14.25 13.6557 13.3765 12.75 12 12.75C10.0628 12.75 8.25 11.4172 8.25 9.5C8.25 7.83361 9.61957 6.60867 11.25 6.31673V6C11.25 5.58579 11.5858 5.25 12 5.25Z" fill="currentColor"></path>
                                                    </g>
                                                </svg>
                                                ${deliveryPriceText}
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Separator -->
                                    <div class="restaurant-info-separator"></div>
                                    <!-- Coupon Tags Container -->
                                    <div class="restaurant-offers-container">
                                        ${couponTagsHtml}
                                    </div>
                                </div>
                            `;

                            const imgElement = restaurantCard.querySelector('.restaurant-image');
                            const imgLoadPromise = new Promise((resolve) => {
                                if (imgElement.complete) {
                                    resolve();
                                } else {
                                    imgElement.onload = resolve;
                                    imgElement.onerror = resolve;
                                }
                            });
                            restaurantImagePromises.push(imgLoadPromise);
                            fragment.appendChild(restaurantCard);

                            // Add event listener for the heart icon on restaurant cards
                            const heartWrapper = restaurantCard.querySelector('.restaurant-favorite-wrapper');
                            const outlineIcon = heartWrapper.querySelector('.heart-icon-outline');
                            const filledIcon = heartWrapper.querySelector('.heart-icon-filled');

                            heartWrapper.addEventListener('click', (e) => {
                                e.preventDefault(); // Prevent navigating to food item link
                                e.stopPropagation(); // Prevent card click event from firing

                                if (heartWrapper.classList.contains('liked')) {
                                    heartWrapper.classList.remove('liked');
                                    outlineIcon.style.display = 'block'; // Show outline
                                    filledIcon.style.display = 'none'; // Hide filled
                                } else {
                                    heartWrapper.classList.add('liked');
                                    outlineIcon.style.display = 'none'; // Hide outline
                                    filledIcon.style.display = 'block'; // Show filled
                                }
                            });
                        }
                    });
                    restaurantsGrid.appendChild(fragment);

                    // Wait for all restaurant images to load before hiding skeleton and showing content
                    Promise.all(restaurantImagePromises).then(() => {
                        restaurantsGridSkeleton.classList.add('hidden');
                        restaurantsGrid.classList.remove('actual-content-hidden');
                        restaurantsGrid.classList.add('actual-content-visible');
                        restaurantsHeading.classList.remove('actual-content-hidden');
                        restaurantsHeading.classList.add('actual-content-visible');
                    }).catch(error => {
                        console.error("Error loading restaurant images:", error);
                        // Still hide skeleton and show content even if some images fail
                        restaurantsGridSkeleton.classList.add('hidden');
                        restaurantsGrid.classList.remove('actual-content-hidden');
                        restaurantsGrid.classList.add('actual-content-visible');
                        restaurantsHeading.classList.remove('actual-content-hidden');
                        restaurantsHeading.classList.add('actual-content-visible');
                    });

                    return restaurantImagePromises; // Return even on error
                } catch (error) {
                    console.error("Error fetching restaurants:", error);
                    restaurantsGrid.innerHTML = '<p style="text-align: center; width: 100%; color: var(--subtle-detail);">Failed to load restaurants. Please try again later.</p>';
                    // Ensure skeleton is hidden and content is visible even on fetch error
                    restaurantsGridSkeleton.classList.add('hidden');
                    restaurantsGrid.classList.remove('actual-content-hidden');
                    restaurantsGrid.classList.add('actual-content-visible');
                    restaurantsHeading.classList.remove('actual-content-hidden');
                    restaurantsHeading.classList.add('actual-content-visible');
                    return restaurantImagePromises; // Return even on error
                }
            };

            // Function to hide all skeletons
            const hideAllSkeletons = () => {
                categoriesGridSkeleton.classList.add('hidden');
                foodItemsGridSkeleton.classList.add('hidden');
                offersGridSkeleton.classList.add('hidden');
                restaurantsGridSkeleton.classList.add('hidden'); // Hide restaurants skeleton
            };

            // Function to show all actual content
            const showAllContent = () => {
                categoriesGrid.classList.remove('actual-content-hidden');
                categoriesGrid.classList.add('actual-content-visible');

                foodItemsGrid.classList.remove('actual-content-hidden');
                foodItemsGrid.classList.add('actual-content-visible');

                offersGrid.classList.remove('actual-content-hidden');
                offersGrid.classList.add('actual-content-visible');

                restaurantsGrid.classList.remove('actual-content-hidden'); // Show restaurants grid
                restaurantsGrid.classList.add('actual-content-visible');


                // Show actual headings
                categoriesHeading.classList.remove('actual-content-hidden');
                categoriesHeading.classList.add('actual-content-visible');
                offersHeading.classList.remove('actual-content-hidden');
                offersHeading.classList.add('actual-content-visible');
                restaurantsHeading.classList.remove('actual-content-hidden'); // Show restaurants heading
                restaurantsHeading.classList.add('actual-content-visible');
            };

            // Standard loading with skeletons

            // Initial state: hide actual content and show skeletons
            categoriesGrid.classList.add('actual-content-hidden');
            foodItemsGrid.classList.add('actual-content-hidden');
            offersGrid.classList.add('actual-content-hidden');
            restaurantsGrid.classList.add('actual-content-hidden'); // Hide restaurants grid

            // Hide actual headings
            categoriesHeading.classList.add('actual-content-hidden');
            offersHeading.classList.add('actual-content-hidden');
            restaurantsHeading.classList.add('actual-content-hidden'); // Hide restaurants heading

            // OPTIMIZED: Use DocumentFragment for faster DOM manipulation
            const createSkeletonFragment = (html, count) => {
                const fragment = document.createDocumentFragment();
                const temp = document.createElement('div');
                temp.innerHTML = html.repeat(count);
                while (temp.firstChild) {
                    fragment.appendChild(temp.firstChild);
                }
                return fragment;
            };

            // Use fragments for faster skeleton creation
            categoriesGridSkeleton.appendChild(createSkeletonFragment('<div class="skeletor-line skeletor-pulse" style="width: 80%; height: 40px; margin: 10px;"></div>', 4));
            foodItemsGridSkeleton.appendChild(createSkeletonFragment('<div class="skeletor-card skeletor-pulse" style="width: 150px; height: 200px; margin: 10px;"></div>', 4));
            offersGridSkeleton.appendChild(createSkeletonFragment('<div class="skeletor-line skeletor-pulse" style="width: 100%; height: 120px; margin: 10px;"></div>', 2));
            restaurantsGridSkeleton.appendChild(createSkeletonFragment('<div class="skeletor-card skeletor-pulse" style="width: 200px; height: 150px; margin: 10px;"></div>', 4));

            // SINGLE REQUEST: Load all home page data in one API call
            const startTime = performance.now();


            try {
                // Single API call for all data
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 10000); // 10s timeout

                const response = await fetch('/home_data_minimal.php', {
                    signal: controller.signal,
                    headers: { 'Cache-Control': 'max-age=600' }
                });
                clearTimeout(timeoutId);

                if (!response.ok) throw new Error(`HTTP ${response.status}`);

                const homeData = await response.json();
                const loadTime = performance.now() - startTime;



                if (homeData.success) {
                    // Update currency
                    currencySymbol = homeData.data.currency;

                    // Display categories
                    displayCategories(homeData.data.categories);

                    // ENSURE Popular category food items are displayed

                    // Always fetch Popular category food items to ensure proper filtering
                    const loadPopularFoodItems = async () => {
                        try {
                            const response = await fetch(`/home.php?action=getFoodItems&category=Popular`);
                            if (response.ok) {
                                const popularItems = await response.json();
                                if (popularItems && popularItems.length > 0) {
                                    displayFoodItems(popularItems, foodItemsGrid);

                                    return true;
                                }
                            }
                        } catch (error) {
                            console.error('Failed to load Popular food items:', error);
                        }
                        return false;
                    };

                    // Try to load Popular items first
                    const popularLoaded = await loadPopularFoodItems();

                    // If Popular items failed to load, use the items from minimal API as fallback
                    if (!popularLoaded && homeData.data.foodItems && homeData.data.foodItems.length > 0) {
                        displayFoodItems(homeData.data.foodItems, foodItemsGrid);

                    }

                    // Update profile picture
                    updateProfilePicture();

                    // Show critical content immediately
                    showCriticalContent();

                    // RENDER ALL ELEMENTS FIRST - Load offers and restaurants immediately
                    const renderAllContent = async () => {
                        try {
                            console.log('🎨 Rendering all page elements...');

                            // Load offers and restaurants in parallel for immediate display
                            const [offersResponse, restaurantsResponse] = await Promise.allSettled([
                                fetch('/home.php?action=getOffers'),
                                fetch('/home.php?action=getRestaurants')
                            ]);

                            // Handle offers
                            if (offersResponse.status === 'fulfilled' && offersResponse.value.ok) {
                                const offers = await offersResponse.value.json();
                                displayOffers(offers);

                            }

                            // Handle restaurants
                            if (restaurantsResponse.status === 'fulfilled' && restaurantsResponse.value.ok) {
                                const restaurants = await restaurantsResponse.value.json();
                                displayRestaurants(restaurants);

                            }

                            // Show all background content
                            showBackgroundContent();



                            // NOW START PRELOADING - After all elements are visible
                            setTimeout(() => {

                                preloadAllCategoriesFoodItems(homeData.data.categories);
                            }, 100);

                        } catch (error) {
                            console.warn('Content rendering failed:', error);
                            showBackgroundContent(); // Show sections anyway
                        }
                    };

                    // Start rendering all content immediately
                    renderAllContent();


                } else {
                    throw new Error(homeData.message || 'Failed to load data');
                }
            } catch (error) {
                console.error('❌ Failed to load home data:', error);
                // Fallback to individual API calls
                await loadDataFallback();
            }

                // Load offers and restaurants in the background (non-blocking)
                Promise.allSettled([
                    fetchAndDisplayOffers(offersGrid, offersGridSkeleton, offersHeading),
                    fetchAndDisplayRestaurants()
                ]).then(([offersResult, restaurantsResult]) => {
                    // Handle offers result
                    if (offersResult.status === 'fulfilled') {
                        Promise.all(offersResult.value || []).then(() => {
                            offersGridSkeleton.classList.add('hidden');
                            offersGrid.classList.remove('actual-content-hidden');
                            offersGrid.classList.add('actual-content-visible');
                            offersHeading.classList.remove('actual-content-hidden');
                            offersHeading.classList.add('actual-content-visible');
                        }).catch(() => {
                            // Show offers section even if images fail
                            offersGridSkeleton.classList.add('hidden');
                            offersGrid.classList.remove('actual-content-hidden');
                            offersGrid.classList.add('actual-content-visible');
                            offersHeading.classList.remove('actual-content-hidden');
                            offersHeading.classList.add('actual-content-visible');
                        });
                    }
                }).catch(error => {
                    console.warn("Background content loading failed:", error);
                    // Still show the sections even if background loading fails
                    hideAllSkeletons();
                    showAllContent();
                });

            if (!skippedLogin && loggedIn === null) {
                window.location.href = '/login.html';
                return;
            }

            // Unified navigation item click handler
            allNavItems.forEach(item => {
                item.addEventListener('click', (event) => {
                    event.preventDefault(); // Prevent default link behavior
                    allNavItems.forEach(nav => nav.classList.remove('active-link')); // Remove active from ALL nav items
                    item.classList.add('active-link'); // Add active to clicked item

                    // No message box calls here as per user request
                });
            });

            // Handle Profile Nav Item click (kept separate for login check)
            profileNavItem.addEventListener('click', (event) => {
                event.preventDefault(); // Prevent default link behavior
                allNavItems.forEach(nav => nav.classList.remove('active-link')); // Ensure all others are inactive
                profileNavItem.classList.add('active-link'); // Set profile as active

                if (loggedIn === 'true') {
                    showMessageBox('Welcome to your profile!', [{ text: 'OK', callback: null, className: 'btn-primary-popup'}]);
                } else {
                    window.location.href = '/login.html';
                }
            });

            // Service worker registration (kept as it's a core PWA feature)
            if ('serviceWorker' in navigator) {
                window.addEventListener('load', () => {
                    navigator.serviceWorker.register('/service-worker.js')
                        .then(registration => {
                        })
                        .catch(error => {
                        });
                });
            }

            // Get initial computed styles for header animation
            const initialHeaderHeight = appHeader.offsetHeight;
            const initialGreetingFontSize = parseFloat(getComputedStyle(headerGreeting).fontSize);
            const initialGreetingMarginTop = parseFloat(getComputedStyle(headerGreeting).marginTop);
            const initialGreetingMarginBottom = parseFloat(getComputedStyle(headerGreeting).marginBottom);
            const initialSearchBarPaddingTop = parseFloat(getComputedStyle(headerSearchBar).paddingTop);
            const initialSearchBarPaddingBottom = parseFloat(getComputedStyle(headerSearchBar).paddingBottom);

            // Define target values for fully scrolled state (in px or unit-less for scale/opacity)
            const targetHeaderHeight = 120; // px
            const targetGreetingFontSize = 0; // px
            const targetGreetingMarginTop = 0; // px
            const targetGreetingMarginBottom = 0; // px
            const targetGreetingTransformY = -20; // px
            const targetSearchBarPaddingTop = 0; // px
            const targetSearchBarPaddingBottom = 0; // px
            const targetSearchBarTransformY = -20; // px
            const targetIconScaleHidden = 0.8;
            const targetIconScaleVisible = 1;

            const scrollAnimationEnd = 400; // Increased for smoother, longer transition

            // Helper function for linear interpolation
            const lerp = (start, end, progress) => start + (end - start) * progress;

            window.addEventListener('scroll', () => {
                const currentScrollY = window.scrollY;
                // Calculate scroll progress (0 to 1), clamped to prevent values outside the range
                const scrollProgress = Math.min(1, currentScrollY / scrollAnimationEnd);

                // Interpolate header properties
                const newHeaderHeight = lerp(initialHeaderHeight, targetHeaderHeight, scrollProgress);

                appHeader.style.height = `${newHeaderHeight}px`;
                // Apply final border-radius when fully scrolled
                if (scrollProgress === 1) {
                    appHeader.classList.add('header-scrolled');
                } else {
                    appHeader.classList.remove('header-scrolled');
                }

                // Interpolate greeting properties
                const newGreetingFontSize = lerp(initialGreetingFontSize, targetGreetingFontSize, scrollProgress);
                const newGreetingOpacity = lerp(1, 0, scrollProgress);
                const newGreetingTransformY = lerp(0, targetGreetingTransformY, scrollProgress);
                const newGreetingMarginTop = lerp(initialGreetingMarginTop, targetGreetingMarginTop, scrollProgress);
                const newGreetingMarginBottom = lerp(initialGreetingMarginBottom, targetGreetingMarginBottom, scrollProgress);


                headerGreeting.style.fontSize = `${newGreetingFontSize}px`;
                headerGreeting.style.opacity = newGreetingOpacity;
                headerGreeting.style.transform = `translateY(${newGreetingTransformY}px)`;
                headerGreeting.style.marginTop = `${newGreetingMarginTop}px`;
                headerGreeting.style.marginBottom = `${newGreetingMarginBottom}px`;
                // Hide completely if font size is tiny or opacity is very low
                if (newGreetingFontSize <= 1 || newGreetingOpacity <= 0.05) {
                    headerGreeting.style.display = 'none';
                } else {
                    headerGreeting.style.display = 'block';
                }


                // Interpolate search bar properties
                const newSearchBarPaddingTop = lerp(initialSearchBarPaddingTop, targetSearchBarPaddingTop, scrollProgress);
                const newSearchBarPaddingBottom = lerp(initialSearchBarPaddingBottom, targetSearchBarPaddingBottom, scrollProgress);
                const newSearchBarOpacity = lerp(1, 0, scrollProgress);
                const newSearchBarTransformY = lerp(0, targetSearchBarTransformY, scrollProgress);

                headerSearchBar.style.paddingTop = `${newSearchBarPaddingTop}px`;
                headerSearchBar.style.paddingBottom = `${newSearchBarPaddingBottom}px`;
                headerSearchBar.style.opacity = newSearchBarOpacity;
                headerSearchBar.style.transform = `translateY(${newSearchBarTransformY}px)`;
                if (newSearchBarOpacity <= 0.05) {
                    headerSearchBar.style.display = 'none';
                } else {
                    headerSearchBar.style.display = 'flex'; // It's a flex container
                }


                // Interpolate icon properties
                const newNotificationIconOpacity = lerp(1, 0, scrollProgress);
                const newNotificationIconScale = lerp(targetIconScaleVisible, targetIconScaleHidden, scrollProgress); // From 1 to 0.8
                const newSearchIconOpacity = lerp(0, 1, scrollProgress);
                const newSearchIconScale = lerp(targetIconScaleHidden, targetIconScaleVisible, scrollProgress); // From 0.8 to 1

                notificationIcon.style.opacity = newNotificationIconOpacity;
                notificationIcon.style.transform = `scale(${newNotificationIconScale})`;

                // Adjust z-index to prevent flickering during icon transition
                if (newNotificationIconOpacity < 0.1 && newSearchIconOpacity > 0.1) {
                    // Notification icon is mostly hidden, search icon is mostly visible
                    notificationIcon.style.zIndex = 5;
                    smallSearchIcon.style.zIndex = 10;
                } else {
                    // Notification icon is visible, or both are in transition (default to notification on top)
                    notificationIcon.style.zIndex = 10;
                    smallSearchIcon.style.zIndex = 5;
                }

                smallSearchIcon.style.opacity = newSearchIconOpacity;
                smallSearchIcon.style.transform = `scale(${newSearchIconScale})`;

                // Manage pointer-events to disable clicks on hidden icons
                if (newNotificationIconOpacity <= 0.05) {
                    notificationIcon.style.pointerEvents = 'none';
                } else {
                    notificationIcon.style.pointerEvents = 'auto';
                }

                if (newSearchIconOpacity >= 0.95) {
                    smallSearchIcon.style.pointerEvents = 'auto';
                } else {
                    smallSearchIcon.style.pointerEvents = 'none';
                }

            });



            // Function to get user's current location
            const getUserLocation = () => {
                return new Promise((resolve, reject) => {
                    if (navigator.geolocation) {
                        navigator.geolocation.getCurrentPosition(
                            async (position) => {
                                userLatitude = position.coords.latitude; // Store globally
                                userLongitude = position.coords.longitude; // Store globally
                                // Set temporary location while geocoding
                                locationDetails.textContent = "Getting location...";

                                // Try multiple geocoding services with fallbacks
                                try {
                                    // First try: Use a CORS-enabled geocoding service
                                    let response;
                                    let geocodingData;

                                    // Try BigDataCloud (free, CORS-enabled)
                                    try {
                                        const bigDataCloudUrl = `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${userLatitude}&longitude=${userLongitude}&localityLanguage=en`;
                                        response = await fetch(bigDataCloudUrl);

                                        if (response.ok) {
                                            geocodingData = await response.json();
                                            if (geocodingData) {
                                                // Format: Subdistrict, City, District, PostalCode
                                                const locationParts = [];

                                                // Add subdistrict/neighborhood
                                                if (geocodingData.locality) {
                                                    locationParts.push(geocodingData.locality);
                                                }

                                                // Add city
                                                if (geocodingData.city) {
                                                    locationParts.push(geocodingData.city);
                                                }

                                                // Add district/state
                                                if (geocodingData.principalSubdivision) {
                                                    locationParts.push(geocodingData.principalSubdivision);
                                                }

                                                // Add postal code
                                                if (geocodingData.postcode) {
                                                    locationParts.push(geocodingData.postcode);
                                                }

                                                const locationText = locationParts.length > 0 ? locationParts.join(', ') :
                                                    `${geocodingData.city || 'Unknown City'}${geocodingData.principalSubdivision ? ', ' + geocodingData.principalSubdivision : ''}`;

                                                locationDetails.textContent = locationText;
                                                resolve({
                                                    latitude: userLatitude,
                                                    longitude: userLongitude,
                                                    location: locationText
                                                });
                                                return;
                                            }
                                        }
                                    } catch (bigDataError) {
                                        console.warn('BigDataCloud geocoding failed:', bigDataError);
                                    }

                                    // Fallback: Try OpenStreetMap Nominatim (may have CORS issues)
                                    const nominatimUrl = `https://nominatim.openstreetmap.org/reverse?format=json&lat=${userLatitude}&lon=${userLongitude}&zoom=14&addressdetails=1`;
                                    response = await fetch(nominatimUrl, {
                                        headers: {
                                            'User-Agent': 'MIKO Food Delivery App/1.0'
                                        }
                                    });

                                    if (response.ok) {
                                        const data = await response.json();

                                        if (data && data.address) {
                                            // Format: Subdistrict, City, District, PostalCode
                                            const address = data.address;
                                            const locationParts = [];

                                            // Add subdistrict/neighborhood (most specific area)
                                            if (address.suburb) {
                                                locationParts.push(address.suburb);
                                            } else if (address.neighbourhood) {
                                                locationParts.push(address.neighbourhood);
                                            } else if (address.quarter) {
                                                locationParts.push(address.quarter);
                                            }

                                            // Add city/town/village
                                            if (address.city) {
                                                locationParts.push(address.city);
                                            } else if (address.town) {
                                                locationParts.push(address.town);
                                            } else if (address.village) {
                                                locationParts.push(address.village);
                                            }

                                            // Add district/county/state
                                            if (address.state_district) {
                                                locationParts.push(address.state_district);
                                            } else if (address.county) {
                                                locationParts.push(address.county);
                                            } else if (address.state) {
                                                locationParts.push(address.state);
                                            }

                                            // Add postal code
                                            if (address.postcode) {
                                                locationParts.push(address.postcode);
                                            }

                                            const cleanLocation = locationParts.length > 0 ? locationParts.join(', ') : data.display_name;
                                            locationDetails.textContent = cleanLocation;

                                            resolve({
                                                latitude: userLatitude,
                                                longitude: userLongitude,
                                                location: cleanLocation,
                                                city: address.city || address.town || address.village || 'Unknown City',
                                                country: address.country || 'Unknown Country',
                                                address: address
                                            });
                                            return;
                                        }
                                    }

                                    throw new Error('Geocoding failed');

                                } catch (error) {
                                    console.warn('Geocoding failed, using default location:', error);
                                    // Fallback to default location if geocoding fails
                                    locationDetails.textContent = "Location unavailable";
                                }

                                // Fallback if geocoding fails
                                resolve({
                                    latitude: userLatitude,
                                    longitude: userLongitude,
                                    location: "Location unavailable"
                                });
                            },
                            (error) => {
                                console.error("Error getting location:", error);
                                switch (error.code) {
                                    case error.PERMISSION_DENIED:
                                        locationDetails.textContent = "Location access denied.";
                                        // Set a default location if permission is denied
                                        userLatitude = DEFAULT_LATITUDE;
                                        userLongitude = DEFAULT_LONGITUDE;
                                        break;
                                    case error.POSITION_UNAVAILABLE:
                                        locationDetails.textContent = "Location information unavailable.";
                                        // Also set default if location is unavailable
                                        userLatitude = DEFAULT_LATITUDE;
                                        userLongitude = DEFAULT_LONGITUDE;
                                        break;
                                    case error.TIMEOUT:
                                        locationDetails.textContent = "Location request timed out.";
                                        // Also set default if location times out
                                        userLatitude = DEFAULT_LATITUDE;
                                        userLongitude = DEFAULT_LONGITUDE;
                                        break;
                                    default:
                                        locationDetails.textContent = "Location error.";
                                        // Also set default for any other error
                                        userLatitude = DEFAULT_LATITUDE;
                                        userLongitude = DEFAULT_LONGITUDE;
                                        break;
                                }
                                resolve({ lat: userLatitude, lon: userLongitude }); // Always resolve, providing default if needed
                            }
                        );
                    } else {
                        locationDetails.textContent = "Geolocation not supported.";
                        userLatitude = DEFAULT_LATITUDE; // Set default if geolocation is not supported
                        userLongitude = DEFAULT_LONGITUDE;
                        resolve({ lat: userLatitude, lon: userLongitude }); // Resolve with default
                    }
                });
            };

            // Initial call to set styles based on current scroll position (useful on page refresh)
            window.dispatchEvent(new Event('scroll'));

            // Initialize user location for restaurant distance calculations
            getUserLocation().then(() => {
                // Location is now available for restaurant distance calculations
                // Restaurants are already loaded via the main content loading system
            }).catch(error => {
                console.warn("Error during initial getUserLocation call, but proceeding with default location:", error);
                // Use default location for calculations
            });

            // Prevent elastic scroll on iOS
            function preventElasticScroll() {
                let startY = 0;

                document.addEventListener('touchstart', function(e) {
                    startY = e.touches[0].pageY;
                }, { passive: false });

                document.addEventListener('touchmove', function(e) {
                    const y = e.touches[0].pageY;
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                    const scrollHeight = document.documentElement.scrollHeight;
                    const clientHeight = window.innerHeight;

                    // Prevent scrolling past the top
                    if (scrollTop <= 0 && y > startY) {
                        e.preventDefault();
                    }

                    // Prevent scrolling past the bottom
                    if (scrollTop + clientHeight >= scrollHeight && y < startY) {
                        e.preventDefault();
                    }
                }, { passive: false });
            }

            // Prevent iOS Safari auto-scroll on input focus
            function preventAutoScroll() {
                const inputs = document.querySelectorAll('input, textarea');

                inputs.forEach(input => {
                    input.addEventListener('focus', function(e) {
                        // Store current scroll position
                        const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;

                        // Small delay to ensure the keyboard is shown, then restore position
                        setTimeout(() => {
                            // Restore the scroll position
                            window.scrollTo(0, currentScrollTop);
                        }, 100);
                    }, { passive: true });

                    input.addEventListener('blur', function() {
                        // Ensure scroll position stays controlled when input loses focus
                        setTimeout(() => {
                            const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
                            window.scrollTo(0, currentScrollTop);
                        }, 100);
                    });
                });
            }

            // Initialize prevention functions
            preventElasticScroll();
            preventAutoScroll();

            // Re-initialize when new inputs are added (for dynamic content)
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        preventAutoScroll();
                    }
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            // Lazy loading implementation for images
            const lazyLoadImages = () => {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            const src = img.getAttribute('data-src');
                            if (src) {
                                img.src = src;
                                img.classList.remove('lazy-load');
                                observer.unobserve(img);
                            }
                        }
                    });
                }, {
                    rootMargin: '50px 0px', // Start loading 50px before the image comes into view
                    threshold: 0.1
                });

                // Observe all lazy-load images
                document.querySelectorAll('.lazy-load').forEach(img => {
                    imageObserver.observe(img);
                });

                // Re-observe new images when content is added
                const contentObserver = new MutationObserver(mutations => {
                    mutations.forEach(mutation => {
                        mutation.addedNodes.forEach(node => {
                            if (node.nodeType === 1) { // Element node
                                const lazyImages = node.querySelectorAll ? node.querySelectorAll('.lazy-load') : [];
                                lazyImages.forEach(img => imageObserver.observe(img));

                                // Also check if the node itself is a lazy-load image
                                if (node.classList && node.classList.contains('lazy-load')) {
                                    imageObserver.observe(node);
                                }
                            }
                        });
                    });
                });

                contentObserver.observe(document.body, {
                    childList: true,
                    subtree: true
                });
            };

            // Initialize lazy loading
            lazyLoadImages();

            // Performance monitoring (optional - can be removed in production)
            if (window.performance && window.performance.timing) {
                window.addEventListener('load', () => {
                    setTimeout(() => {
                        const timing = window.performance.timing;
                        const loadTime = timing.loadEventEnd - timing.navigationStart;
                        const domReady = timing.domContentLoadedEventEnd - timing.navigationStart;
                        console.log(`Page load time: ${loadTime}ms, DOM ready: ${domReady}ms`);
                    }, 0);
                });
            }
        });
    </script>
    <script src="/js/qr.js"></script>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>
</body>
</html>
