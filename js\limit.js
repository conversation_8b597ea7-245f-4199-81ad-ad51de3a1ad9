const OTP_COOLDOWN_SECONDS = 60;
const LOGIN_LINK_COOLDOWN_SECONDS = 120;
const PASSWORD_RESET_COOLDOWN_SECONDS = 180; 
const LAST_OTP_REQUEST_KEY = 'lastOtpRequestTimestamp';
const LAST_LOGIN_LINK_REQUEST_KEY = 'lastLoginLinkRequestTimestamp';
const LAST_PASSWORD_RESET_REQUEST_KEY = 'lastPasswordResetRequestTimestamp';
/**
 * @param {string} key -
 * @param {number} cooldownSeconds -
 * @returns {boolean}
 */
function canRequest(key, cooldownSeconds) {
    const lastRequestTimestamp = sessionStorage.getItem(key);
    if (!lastRequestTimestamp) {
        return true;
    }
    const currentTime = Date.now();
    const lastRequestTime = parseInt(lastRequestTimestamp, 10);
    const elapsedTimeInSeconds = (currentTime - lastRequestTime) / 1000;
    return elapsedTimeInSeconds >= cooldownSeconds;
}
/**
 * @param {string} key 
 */
function recordRequest(key) {
    sessionStorage.setItem(key, Date.now().toString());
}
/**
 * 
 * @param {string} key 
 * @param {number} cooldownSeconds
 * @returns {number}
 */
function getRemainingTime(key, cooldownSeconds) {
    const lastRequestTimestamp = sessionStorage.getItem(key);
    if (!lastRequestTimestamp) {
        return 0;
    }
    const currentTime = Date.now();
    const lastRequestTime = parseInt(lastRequestTimestamp, 10);
    const elapsedTimeInSeconds = (currentTime - lastRequestTime) / 1000;
    const remaining = cooldownSeconds - elapsedTimeInSeconds;
    return Math.max(0, Math.ceil(remaining));
}
const countdownIntervals = {};
/**
@param {HTMLElement} element
@param {string} key
@param {number} cooldownSeconds
@param {string} originalText
 */
function updateCooldownUI(element, key, cooldownSeconds, originalText) {
    if (!element) {
        console.warn(`[limit.js] updateCooldownUI: element for key "${key}" is null or undefined.`);
        return;
    }
    if (countdownIntervals[key]) {
        clearInterval(countdownIntervals[key]);
        delete countdownIntervals[key];
    }
    const initialRemainingTime = getRemainingTime(key, cooldownSeconds);
    if (initialRemainingTime > 0) {
        setElementDisabled(element, true);
        element.textContent = `${originalText} (${formatTime(initialRemainingTime)})`;
        let currentRemainingTime = initialRemainingTime;
        countdownIntervals[key] = setInterval(() => {
            currentRemainingTime--;
            if (currentRemainingTime <= 0) {
                clearInterval(countdownIntervals[key]);
                delete countdownIntervals[key];
                setElementDisabled(element, false);
                element.textContent = originalText;

                // Notify all elements with the same key that countdown is complete
                notifyCountdownComplete(key, originalText);
            } else {
                element.textContent = `${originalText} (${formatTime(currentRemainingTime)})`;

                // Update all elements with the same key
                updateAllElementsWithKey(key, currentRemainingTime);
            }
        }, 1000);
    } else {
        setElementDisabled(element, false);
        element.textContent = originalText;
    }
}

/**
 * Updates all elements that share the same cooldown key
 * @param {string} key
 * @param {number} remainingTime
 */
function updateAllElementsWithKey(key, remainingTime) {
    const elements = getElementsForKey(key);
    elements.forEach(({element, originalText}) => {
        if (element && element.textContent.includes('(')) {
            element.textContent = `${originalText} (${formatTime(remainingTime)})`;
        }
    });
}

/**
 * Notifies all elements when countdown is complete
 * @param {string} key
 */
function notifyCountdownComplete(key) {
    const elements = getElementsForKey(key);
    elements.forEach(({element, originalText}) => {
        if (element) {
            setElementDisabled(element, false);
            element.textContent = originalText;
        }
    });
}

/**
 * Gets all elements associated with a cooldown key
 * @param {string} key
 * @returns {Array}
 */
function getElementsForKey(key) {
    const elements = [];

    if (key === LAST_OTP_REQUEST_KEY) {
        const sendBtn = document.getElementById('sendVerificationEmailBtn');
        const resendLink = document.getElementById('resendOtpLink');
        if (sendBtn) elements.push({element: sendBtn, originalText: 'Send An OTP'});
        if (resendLink) elements.push({element: resendLink, originalText: 'Request Again'});
    } else if (key === LAST_LOGIN_LINK_REQUEST_KEY) {
        const sendBtn = document.getElementById('sendLoginLinkBtn');
        const confirmBtn = document.getElementById('confirmAndSendLoginLinkBtn');
        if (sendBtn) elements.push({element: sendBtn, originalText: 'Send me a login link'});
        if (confirmBtn) elements.push({element: confirmBtn, originalText: 'Send a login link'});
    } else if (key === LAST_PASSWORD_RESET_REQUEST_KEY) {
        const resetBtn = document.getElementById('forgotPasswordSendBtn');
        if (resetBtn) elements.push({element: resetBtn, originalText: 'RESET PASSWORD'});
    }

    return elements;
}

/**
 * Sets the disabled state of an element with proper styling
 * @param {HTMLElement} element
 * @param {boolean} disabled
 */
function setElementDisabled(element, disabled) {
    if (disabled) {
        element.style.pointerEvents = 'none';
        element.disabled = true;

        // Add disabled class based on element type
        if (element.tagName === 'A') {
            element.classList.add('link-disabled');
        } else {
            element.classList.add('btn-disabled');
        }
    } else {
        element.style.pointerEvents = 'auto';
        element.disabled = false;

        // Remove disabled classes
        element.classList.remove('link-disabled', 'btn-disabled');
    }
}

/**
 * Formats time in a more readable format
 * @param {number} seconds
 * @returns {string}
 */
function formatTime(seconds) {
    if (seconds >= 60) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${seconds}s`;
}

/**
 * Starts cooldown from server response
 * @param {HTMLElement} element
 * @param {string} key
 * @param {number} remainingTime
 * @param {string} originalText
 */
function startCooldownFromServer(element, key, remainingTime, originalText) {
    if (!element || remainingTime <= 0) {
        return;
    }

    // Record the request time based on remaining time
    const currentTime = Date.now();
    const requestTime = currentTime - ((getCooldownSecondsForKey(key) - remainingTime) * 1000);
    sessionStorage.setItem(key, requestTime.toString());

    // Update UI
    updateCooldownUI(element, key, getCooldownSecondsForKey(key), originalText);
}

/**
 * Starts cooldown for all buttons of a specific type immediately
 * @param {string} key
 */
function startCooldownForAllButtons(key) {
    // Record the request time
    recordRequest(key);

    // Get cooldown duration
    const cooldownSeconds = getCooldownSecondsForKey(key);

    // Update all related buttons immediately
    const elements = getElementsForKey(key);
    elements.forEach(({element, originalText}) => {
        if (element) {
            setElementDisabled(element, true);
            element.textContent = `${originalText} (${formatTime(cooldownSeconds)})`;
        }
    });

    // Start the countdown interval
    if (countdownIntervals[key]) {
        clearInterval(countdownIntervals[key]);
    }

    let currentRemainingTime = cooldownSeconds;
    countdownIntervals[key] = setInterval(() => {
        currentRemainingTime--;
        if (currentRemainingTime <= 0) {
            clearInterval(countdownIntervals[key]);
            delete countdownIntervals[key];
            notifyCountdownComplete(key);
        } else {
            updateAllElementsWithKey(key, currentRemainingTime);
        }
    }, 1000);
}

/**
 * Gets cooldown seconds for a given key
 * @param {string} key
 * @returns {number}
 */
function getCooldownSecondsForKey(key) {
    switch (key) {
        case LAST_OTP_REQUEST_KEY:
            return OTP_COOLDOWN_SECONDS;
        case LAST_LOGIN_LINK_REQUEST_KEY:
            return LOGIN_LINK_COOLDOWN_SECONDS;
        case LAST_PASSWORD_RESET_REQUEST_KEY:
            return PASSWORD_RESET_COOLDOWN_SECONDS;
        default:
            return 60; // Default fallback
    }
}
window.limit = {
    OTP_COOLDOWN_SECONDS,
    LOGIN_LINK_COOLDOWN_SECONDS,
    PASSWORD_RESET_COOLDOWN_SECONDS,
    LAST_OTP_REQUEST_KEY,
    LAST_LOGIN_LINK_REQUEST_KEY,
    LAST_PASSWORD_RESET_REQUEST_KEY,
    canRequest,
    recordRequest,
    getRemainingTime,
    updateCooldownUI,
    setElementDisabled,
    formatTime,
    startCooldownFromServer,
    getCooldownSecondsForKey,
    startCooldownForAllButtons,
    updateAllElementsWithKey,
    notifyCountdownComplete,
    getElementsForKey
};
