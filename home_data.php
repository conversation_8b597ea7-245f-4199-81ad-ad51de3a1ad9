<?php
/**
 * Single API endpoint for all home page data
 * Returns: currency, categories, popular food items, offers, restaurants
 */

// Suppress PHP errors from being output as HTML
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Debug mode
$debug = isset($_GET['debug']) && $_GET['debug'] === '1';
if ($debug) {
    ini_set('display_errors', 1);
}

// Supabase configuration (same as home.php)
$supabaseUrl = "https://xswrokjllrkdyepluztn.supabase.co";
$supabaseServiceRoleKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inhzd3Jva2psbHJrZHllcGx1enRuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjczMDU4MywiZXhwIjoyMDY4MzA2NTgzfQ.5Ek5Rlj3Iozc476u9ebVTlREp6RtTRYqmK8Sq_1uzrA";

// Function to make Supabase API requests
function supabaseRequest($endpoint, $method = 'GET', $data = null, $select = null) {
    global $supabaseUrl, $supabaseServiceRoleKey;

    $url = $supabaseUrl . "/rest/v1/" . $endpoint;

    // Add select parameter if provided
    if ($select) {
        $url .= "?select=" . urlencode($select);
    }

    $headers = [
        "apikey: $supabaseServiceRoleKey",
        "Authorization: Bearer $supabaseServiceRoleKey",
        "Content-Type: application/json",
        "Accept: application/json",
        "Prefer: return=minimal"
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_2_0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        throw new Exception("cURL Error: " . $error);
    }

    if ($httpCode !== 200) {
        throw new Exception("HTTP Error: " . $httpCode . " - " . $response);
    }

    return json_decode($response, true);
}

try {
    $response = [
        'success' => true,
        'timestamp' => time(),
        'data' => []
    ];

    // PARALLEL REQUESTS: Use cURL multi-handle for simultaneous requests
    $multiHandle = curl_multi_init();
    $curlHandles = [];

    // Prepare all requests
    $requests = [
        'currency' => 'currency?is_active=eq.1&select=symbol&limit=1',
        'categories' => 'categories?select=name,emoji,ranking&order=ranking.asc',
        'food_items' => 'food_items?category=eq.Popular&select=id,item_name,price,original_price,image,description,category&order=id.desc&limit=20',
        'offers' => 'offers?order=expiry_date.asc&select=id,banner,link,discount,flat,restaurant_name,code,expiry_date,created_at,description&limit=10',
        'restaurants' => 'restaurants?select=id,name,image,description&order=id&limit=20'
    ];

    // Create cURL handles for parallel execution
    foreach ($requests as $key => $endpoint) {
        $url = $supabaseUrl . "/rest/v1/" . $endpoint;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "apikey: $supabaseServiceRoleKey",
            "Authorization: Bearer $supabaseServiceRoleKey",
            "Content-Type: application/json",
            "Accept: application/json"
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 2);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_2_0);

        $curlHandles[$key] = $ch;
        curl_multi_add_handle($multiHandle, $ch);
    }

    // Execute all requests in parallel
    $running = null;
    do {
        curl_multi_exec($multiHandle, $running);
        curl_multi_select($multiHandle);
    } while ($running > 0);

    // Collect results
    $results = [];
    foreach ($curlHandles as $key => $ch) {
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $responseData = curl_multi_getcontent($ch);

        if ($httpCode === 200 && $responseData) {
            $results[$key] = json_decode($responseData, true);
        } else {
            $results[$key] = [];
        }

        curl_multi_remove_handle($multiHandle, $ch);
        curl_close($ch);
    }
    curl_multi_close($multiHandle);

    // Process results
    $response['data']['currency'] = !empty($results['currency']) ? $results['currency'][0]['symbol'] : 'Rs.';
    $response['data']['categories'] = $results['categories'] ?: [];
    $response['data']['foodItems'] = $results['food_items'] ?: [];
    $response['data']['offers'] = $results['offers'] ?: [];
    $response['data']['restaurants'] = $results['restaurants'] ?: [];

    // Handle fallback for food items if Popular category is empty
    if (empty($response['data']['foodItems']) && !empty($response['data']['categories'])) {
        try {
            $firstCategoryName = $response['data']['categories'][0]['name'];
            $fallbackFoodItems = supabaseRequest("food_items?category=eq.$firstCategoryName&select=id,item_name,price,original_price,image,description,category&order=id.desc&limit=20");
            $response['data']['foodItems'] = $fallbackFoodItems ?: [];
        } catch (Exception $e) {
            error_log("Fallback food items fetch error: " . $e->getMessage());
        }
    }

    // 6. Add metadata
    $response['data']['metadata'] = [
        'categoriesCount' => count($response['data']['categories']),
        'foodItemsCount' => count($response['data']['foodItems']),
        'offersCount' => count($response['data']['offers']),
        'restaurantsCount' => count($response['data']['restaurants']),
        'loadTime' => round((microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']) * 1000, 2) // in milliseconds
    ];

    // Set cache headers for better performance
    header('Cache-Control: public, max-age=300'); // 5 minutes cache
    header('ETag: "' . md5(json_encode($response)) . '"');

    echo json_encode($response, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    // Clear any previous output
    if (ob_get_level()) {
        ob_clean();
    }

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error',
        'message' => $e->getMessage(),
        'timestamp' => time()
    ], JSON_UNESCAPED_SLASHES);

    error_log("Home data API error: " . $e->getMessage());
}
?>
