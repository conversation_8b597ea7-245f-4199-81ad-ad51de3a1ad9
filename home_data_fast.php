<?php
/**
 * ULTRA-FAST Single API endpoint for all home page data
 * Uses parallel cURL requests for maximum speed
 */

// Suppress PHP errors and set headers
error_reporting(0);
ini_set('display_errors', 0);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Cache-Control: public, max-age=300');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Supabase configuration
$supabaseUrl = "https://xswrokjllrkdyepluztn.supabase.co";
$supabaseKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inhzd3Jva2psbHJrZHllcGx1enRuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjczMDU4MywiZXhwIjoyMDY4MzA2NTgzfQ.5Ek5Rlj3Iozc476u9ebVTlREp6RtTRYqmK8Sq_1uzrA";

try {
    $startTime = microtime(true);
    
    // Prepare parallel requests
    $multiHandle = curl_multi_init();
    $curlHandles = [];
    
    $endpoints = [
        'currency' => 'currency?is_active=eq.1&select=symbol&limit=1',
        'categories' => 'categories?select=name,emoji,ranking&order=ranking.asc&limit=50',
        'food_items' => 'food_items?category=eq.Popular&select=id,item_name,price,original_price,image,description&order=id.desc&limit=10',
        'offers' => 'offers?select=id,banner,restaurant_name,description&order=expiry_date.asc&limit=5',
        'restaurants' => 'restaurants?select=id,name,image,description&order=id&limit=10'
    ];

    // Create cURL handles
    foreach ($endpoints as $key => $endpoint) {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => "$supabaseUrl/rest/v1/$endpoint",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 3,
            CURLOPT_CONNECTTIMEOUT => 1,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_2_0,
            CURLOPT_HTTPHEADER => [
                "apikey: $supabaseKey",
                "Authorization: Bearer $supabaseKey",
                "Content-Type: application/json"
            ]
        ]);
        
        $curlHandles[$key] = $ch;
        curl_multi_add_handle($multiHandle, $ch);
    }

    // Execute all requests in parallel
    $running = null;
    do {
        curl_multi_exec($multiHandle, $running);
        curl_multi_select($multiHandle);
    } while ($running > 0);

    // Collect results
    $data = [];
    foreach ($curlHandles as $key => $ch) {
        $response = curl_multi_getcontent($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if ($httpCode === 200 && $response) {
            $decoded = json_decode($response, true);
            $data[$key] = $decoded ?: [];
        } else {
            $data[$key] = [];
        }
        
        curl_multi_remove_handle($multiHandle, $ch);
        curl_close($ch);
    }
    curl_multi_close($multiHandle);

    // Process and format response
    $result = [
        'success' => true,
        'timestamp' => time(),
        'data' => [
            'currency' => !empty($data['currency']) ? $data['currency'][0]['symbol'] : 'Rs.',
            'categories' => $data['categories'],
            'foodItems' => $data['food_items'],
            'offers' => $data['offers'],
            'restaurants' => $data['restaurants'],
            'metadata' => [
                'categoriesCount' => count($data['categories']),
                'foodItemsCount' => count($data['food_items']),
                'offersCount' => count($data['offers']),
                'restaurantsCount' => count($data['restaurants']),
                'loadTime' => round((microtime(true) - $startTime) * 1000, 2)
            ]
        ]
    ];

    echo json_encode($result, JSON_UNESCAPED_SLASHES);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Server error',
        'timestamp' => time()
    ]);
}
?>
