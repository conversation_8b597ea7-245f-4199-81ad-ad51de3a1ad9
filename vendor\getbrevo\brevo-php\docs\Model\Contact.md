# Contact

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**virtualNextTask** | **object** | Next incomplete task of contact | [optional] 
**email** | **string** | Contact email | 
**assignToId** | **string** | User id to whom contact is assigned | [optional] 
**contactsId** | **int** | Contact id | [optional] 
**crmLists** | **object** | CRM lists in which contact is added | 
**attributes** | **object** | Contact attributes e.g firstname / lastname / SMS etc. | [optional] 
**createdAt** | [**\DateTime**] | Contact created date/time | [optional] 
**updatedAt** | [**\DateTime**] | Contact updated date/time | [optional] 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


