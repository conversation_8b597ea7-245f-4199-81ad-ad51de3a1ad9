<?php
/**
 * This file is part of the Cloudinary PHP package.
 *
 * (c) Cloudinary
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Cloudinary\Transformation;

/**
 * Interface KitchenObjectGravityInterface
 *
 * @api
 */
interface KitchenObjectGravityInterface
{
    //Kitchen Category
    public const KITCHEN = 'kitchen';
    public const BOTTLE  = 'bottle';
    public const WINE_GLASS = 'wine_glass';
    public const CUP        = 'cup';
    public const FORK = 'fork';
    public const KNIFE = 'knife';
    public const SPOON = 'spoon';
    public const BOWL  = 'bowl';
}
