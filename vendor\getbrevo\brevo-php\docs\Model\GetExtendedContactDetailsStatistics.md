# GetExtendedContactDetailsStatistics

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**messagesSent** | [**\Brevo\Client\Model\GetExtendedContactDetailsStatisticsMessagesSent[]**](GetExtendedContactDetailsStatisticsMessagesSent.md) | Listing of the sent campaign for the contact | [optional] 
**hardBounces** | [**\Brevo\Client\Model\GetExtendedContactDetailsStatisticsMessagesSent[]**](GetExtendedContactDetailsStatisticsMessagesSent.md) | Listing of the hardbounes generated by the contact | [optional] 
**softBounces** | [**\Brevo\Client\Model\GetExtendedContactDetailsStatisticsMessagesSent[]**](GetExtendedContactDetailsStatisticsMessagesSent.md) | Listing of the softbounes generated by the contact | [optional] 
**complaints** | [**\Brevo\Client\Model\GetExtendedContactDetailsStatisticsMessagesSent[]**](GetExtendedContactDetailsStatisticsMessagesSent.md) | Listing of the complaints generated by the contact | [optional] 
**unsubscriptions** | [**\Brevo\Client\Model\GetExtendedContactDetailsStatisticsUnsubscriptions**](GetExtendedContactDetailsStatisticsUnsubscriptions.md) |  | [optional] 
**opened** | [**\Brevo\Client\Model\GetExtendedContactDetailsStatisticsOpened[]**](GetExtendedContactDetailsStatisticsOpened.md) | Listing of the openings generated by the contact | [optional] 
**clicked** | [**\Brevo\Client\Model\GetExtendedContactDetailsStatisticsClicked[]**](GetExtendedContactDetailsStatisticsClicked.md) | Listing of the clicks generated by the contact | [optional] 
**transacAttributes** | **object[]** | Listing of the transactional attributes for the contact | [optional] 
**delivered** | [**\Brevo\Client\Model\GetExtendedContactDetailsStatisticsMessagesSent[]**](GetExtendedContactDetailsStatisticsMessagesSent.md) | Listing of the delivered campaign for the contact | [optional] 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


