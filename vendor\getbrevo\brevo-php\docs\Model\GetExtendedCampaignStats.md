# GetExtendedCampaignStats

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**globalStats** | [**\Brevo\Client\Model\GetExtendedCampaignStatsGlobalStats**](GetExtendedCampaignStatsGlobalStats.md) |  | 
**campaignStats** | **object[]** | List-wise statistics of the campaign. | 
**mirrorClick** | **int** | Number of clicks on mirror link | 
**remaining** | **int** | Number of remaning emails to send | 
**linksStats** | **object** | Statistics about the number of clicks for the links | 
**statsByDomain** | [**\Brevo\Client\Model\GetStatsByDomain**](GetStatsByDomain.md) |  | 
**statsByDevice** | [**\Brevo\Client\Model\GetStatsByDevice**](GetStatsByDevice.md) | Statistics about the campaign on the basis of various devices | 
**statsByBrowser** | [**\Brevo\Client\Model\GetStatsByBrowser**](GetStatsByBrowser.md) | Statistics about the campaign on the basis of various browsers | 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


