<?php
require 'vendor/autoload.php';

use Cloudinary\Configuration\Configuration;
use Cloudinary\Api\Upload\UploadApi;

// Configure Cloudinary once
Configuration::instance([
    'cloud' => [
        'cloud_name' => 'dg3uhpunl',
        'api_key'    => '464723357316554',
        'api_secret' => 'HwvT_JgSnWQCnbiqLvX_jt7nZHI',
    ],
    'url' => ['secure' => true]
]);

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['avatars'])) {
    $allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    $maxFileSize = 2 * 1024 * 1024; // 2MB
    $uploadApi = new UploadApi();

    // Normalize files array for multiple uploads
    $files = $_FILES['avatars'];
    $fileCount = count($files['name']);
    
    echo "<h3>Upload Results:</h3>";

    for ($i = 0; $i < $fileCount; $i++) {
        $name = $files['name'][$i];
        $type = $files['type'][$i];
        $tmpName = $files['tmp_name'][$i];
        $size = $files['size'][$i];

        // Validate type and size
        if (!in_array($type, $allowedTypes)) {
            echo "❌ File '$name': Invalid file type.<br>";
            continue;
        }
        if ($size > $maxFileSize) {
            echo "❌ File '$name': Exceeds max size of 2MB.<br>";
            continue;
        }

        // Clean filename (remove spaces, special chars) for Cloudinary public_id
        $cleanName = pathinfo($name, PATHINFO_FILENAME);
        $extension = pathinfo($name, PATHINFO_EXTENSION);

        try {
            $response = $uploadApi->upload($tmpName, [
                'folder' => 'avatars',
                // Use original filename (without extension) as public_id
                // Cloudinary auto-adds extension based on file
                'public_id' => $cleanName,
                'overwrite' => true,  // Overwrite existing with same name
                'resource_type' => 'image',
            ]);

            echo "✅ Uploaded '$name' as <a href='{$response['secure_url']}' target='_blank'>{$response['secure_url']}</a><br>";
        } catch (Exception $e) {
            echo "❌ Failed to upload '$name': " . $e->getMessage() . "<br>";
        }
    }
}
?>

<!-- Bulk upload HTML form -->
<form method="post" enctype="multipart/form-data">
    <label for="avatars">Choose avatars to upload (multiple allowed):</label><br>
    <input type="file" name="avatars[]" id="avatars" multiple required><br><br>
    <input type="submit" value="Upload Avatars to Cloudinary">
</form>
