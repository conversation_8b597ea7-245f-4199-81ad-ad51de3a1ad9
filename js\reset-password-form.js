        const messageBox = document.getElementById('messageBox');
        const resetPasswordBtn = document.getElementById('resetPasswordBtn');
        const newPasswordInput = document.getElementById('newPassword');
        const confirmPasswordInput = document.getElementById('confirmPassword');
        const emailDisplay = document.getElementById('emailDisplay');

        let email = '';
        let token = '';

        // Function to toggle password visibility
        function togglePasswordVisibility(inputId, buttonElement) {
            const passwordInput = document.getElementById(inputId);
            const icon = buttonElement.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            }
        }

        // Function to display messages
        function showMessage(message, type) {
            messageBox.textContent = message;
            messageBox.className = `message-box ${type}`; // Apply class for styling (success/error)
            messageBox.style.display = 'block';
        }

        // Function to hide messages
        function hideMessage() {
            messageBox.style.display = 'none';
            messageBox.textContent = '';
            messageBox.className = 'message-box';
        }

        // Function to disable/enable form elements
        function setFormEnabled(enabled) {
            newPasswordInput.disabled = !enabled;
            confirmPasswordInput.disabled = !enabled;
            resetPasswordBtn.disabled = !enabled;
        }

        // Function to handle password reset submission
        async function handleResetPassword() {
            hideMessage(); // Clear previous messages

            const newPassword = newPasswordInput.value;
            const confirmPassword = confirmPasswordInput.value;

            if (!newPassword || !confirmPassword) {
                showMessage('Please fill in both password fields.', 'error');
                return;
            }

            if (newPassword !== confirmPassword) {
                showMessage('Passwords do not match.', 'error');
                return;
            }

            if (newPassword.length < 6) {
                showMessage('Password must be at least 6 characters long.', 'error');
                return;
            }

            // Show loading indicator
            resetPasswordBtn.innerHTML = '<div class="loading-dots"><span></span><span></span><span></span></div>';
            setFormEnabled(false); // Disable form during submission

            try {
                // Call the backend API to reset the password
                const response = await fetch(`server.php?path=/api/reset-password`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email,
                        token: token,
                        new_password: newPassword
                    })
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    showMessage('Your password has been successfully reset! Redirecting to login...', 'success');
                    // Redirect to login page after a short delay
                    setTimeout(() => {
                        window.location.href = '/login.html';
                    }, 3000);
                } else {
                    showMessage(`Password reset failed: ${data.message || 'Unknown error'}`, 'error');
                    setFormEnabled(true); // Re-enable form on failure (unless token expired)
                }
            } catch (error) {
                console.error('Error during password reset:', error);
                showMessage(`Network error during password reset: ${error.message}. Please check your server.`, 'error');
                setFormEnabled(true); // Re-enable form on network error
            } finally {
                // Reset button text
                resetPasswordBtn.innerHTML = 'RESET PASSWORD';
            }
        }

        // Event listener for the reset password button
        resetPasswordBtn.addEventListener('click', handleResetPassword);

        // On page load, extract token and email from URL and verify
        document.addEventListener('DOMContentLoaded', async () => {
            const urlParams = new URLSearchParams(window.location.search);
            token = urlParams.get('token');
            email = urlParams.get('email');

            // Initial state: disable form until token is verified
            setFormEnabled(false);
            emailDisplay.textContent = 'Verifying reset link...';

            if (!token || !email) {
                showMessage('Invalid or missing password reset link. Please request a new one.', 'error');
                emailDisplay.textContent = 'Invalid link.';
                return; // Stop execution if link is bad
            }

            // Verify the token with the backend immediately
            try {
                const response = await fetch(`server.php?path=/api/verify-token`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email: email, token: token, type: 'password_reset' })
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    emailDisplay.innerHTML = `Enter a new password for <span style="font-weight: bold; color: var(--text-dark-elements);">${email}</span>.`;
                    setFormEnabled(true); // Enable form if token is valid
                    hideMessage(); // Clear "Verifying..." message
                } else {
                    showMessage(`Invalid or expired reset link: ${data.message || 'Please request a new password reset link.'}`, 'error');
                    emailDisplay.textContent = 'Invalid link.'; // Keep email display as invalid
                }
            } catch (error) {
                console.error('Error verifying token on load:', error);
                showMessage(`Failed to verify reset link due to a network error. Please try again.`, 'error');
                emailDisplay.textContent = 'Error verifying link.';
            }
        });
