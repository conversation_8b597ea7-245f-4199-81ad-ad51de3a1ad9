# GetAttributesAttributes

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **string** | Name of the attribute | 
**category** | **string** | Category of the attribute | 
**type** | **string** | Type of the attribute | [optional] 
**enumeration** | [**\Brevo\Client\Model\GetAttributesEnumeration[]**](GetAttributesEnumeration.md) | Parameter only available for \&quot;category\&quot; type attributes. | [optional] 
**calculatedValue** | **string** | Calculated value formula | [optional] 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


