<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0,viewport-fit=cover">
    <meta name="theme-color" content="#FF6B35">
    <title>MIKO - Login</title>

    <link rel="manifest" href="manifest.json">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@700;800&family=Montserrat:wght@400;600;700&display=swap" rel="stylesheet">
    <script src="js/google.js"></script>
    <script src="js/mail.js"></script>
    <script src="js/limit.js"></script>
    <script src="js/validator.js"></script>
    <style>
        /* Base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Montserrat', sans-serif; /* Updated font-family */

            /* Prevent touch gestures on all elements */
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;
        }

        /* Prevent zoom and gestures globally */
        html {
            touch-action: manipulation;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;

            /* Prevent iOS Safari auto-scroll and viewport shifts */
            position: fixed;
            width: 100%;
            height: 100%;
        }

        /* Root CSS Variables */
        :root {
            --primary-accent: #ff5018;    /* Updated Vibrant Orange/Red */
            --secondary-accent: #FF9F1C;
            --neutral-base: #F7F7F7;
            --text-dark-elements: #333333;
            --subtle-detail: #A7A7A7;
            --white: #FFFFFF;
            --border-gray: #E0E0E0;
            --shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
            --toast-success: #4CAF50;
            --toast-error: #F44336;
            --toast-info: #2196F3;
            --toast-warning: #ff9800;
        }



        /* Body styles */
        body {
            color: var(--text-dark-elements);
            height: 100vh;
            position: fixed;
            width: 100%;
            overflow: hidden;
            transition: background-color 0.3s ease;

            /* Prevent touch gestures and zoom */
            touch-action: manipulation;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-tap-highlight-color: transparent;

            /* Prevent elastic scroll on iOS */
            overscroll-behavior: none;
            -webkit-overflow-scrolling: touch;
        }

        /* Background overlay - true overlay behind content */
        #backgroundOverlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--primary-accent);
            background-image: url("data:image/svg+xml,%3Csvg width='200' height='200' viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='foodPattern' x='0' y='0' width='200' height='200' patternUnits='userSpaceOnUse'%3E%3Cg fill='none' stroke='white' stroke-width='1.5' opacity='0.2'%3E%3C!-- Tomato/Round Item --%3E%3Ccircle cx='40' cy='40' r='18'/%3E%3Cpath d='M40 22 L40 58 M22 40 L58 40'/%3E%3C!-- Chili Pepper 1 --%3E%3Cpath d='M160 30 Q175 15, 185 30 T190 50 T180 70 T160 60 Z'/%3E%3C!-- Cucumber Slice --%3E%3Ccircle cx='140' cy='150' r='15'/%3E%3Cpath d='M140 135 L140 165 M125 150 L155 150'/%3E%3C!-- Leaf --%3E%3Cpath d='M60 110 Q70 100, 80 110 T90 120 T80 130 T70 120 T60 110 Z'/%3E%3C!-- Chili Pepper 2 (rotated) --%3E%3Cg transform='translate(20, 100) rotate(90)'%3E%3Cpath d='M160 30 Q175 15, 185 30 T190 50 T180 70 T160 60 Z'/%3E%3C/g%3E%3C/g%3E%3C/pattern%3E%3C/defs%3E%3Crect x='0' y='0' width='100%25' height='100%25' fill='url(%23foodPattern)'/%3E%3C/svg%3E");
            background-size: 200px 200px;
            z-index: -1;
            pointer-events: none;

            /* Prevent elastic scroll without breaking layout */
            overscroll-behavior: none;
        }


        /* Body state when modal is open */
        body.modal-open {
            background-color: var(--white) !important; /* Change background to modal's color */
            overflow: hidden; /* Disable scrolling */
        }

        /* Loading state */
        body.loading-state {
            background-color: var(--white) !important;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            overflow: hidden;
            flex-direction: column;
        }

        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: var(--text-dark-elements);
            gap: 1rem;
        }

        .loading-icon-spin {
            animation: spin 2s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-message {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--subtle-detail);
        }

        .loading-dots span {
            background-color: var(--primary-accent);
        }

        /* Skip button */
        .skip-button {
            position: absolute;
            top: 3rem;
            right: 1rem;
            color: var(--white);
            font-size: 0.9rem;
            font-weight: 600;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.2);
            transition: background-color 0.2s ease;
            z-index: 10;
        }

        .skip-button:hover {
            background: rgba(255, 255, 255, 0.3);
        }



        /* Main content container */
        .main-content {
            position: relative;
            height: 100vh;
            display: flex;
            flex-direction: column;
            z-index: 1;
            overflow-y: auto;
            overflow-x: hidden;
            -webkit-overflow-scrolling: touch;
            overscroll-behavior: none;
        }

        /* Top section */
        .top-section {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem 1.5rem;
            text-align: center;
            color: var(--white);
            position: relative;
            z-index: 1;
        }

        .top-section h1 {
            font-size: 2.2rem;
            font-weight: 700;
            line-height: 1.3;
            margin-bottom: 2rem;
            max-width: 350px;
        }

        .illustration {
            background-color: transparent;
            margin-bottom: 2rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .illustration-icon-wrapper {
            width: 120px;
            height: 120px;
            background: #FFFFFF; /* Always white, even in dark mode */
            border-radius: 30px;
            margin-bottom: 0px;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 10px 30px rgba(255, 107, 53, 0.3);
            z-index: 2;
            opacity: 1;
            pointer-events: auto;
            transition: opacity 0.5s ease-out;
        }

        .illustration-icon-wrapper.logo-fade-out {
            opacity: 0;
        }

        .illustration-icon-wrapper i {
            font-size: 70px;
            color: var(--primary-accent);
        }

        body.loading-state .illustration-icon-wrapper {
            background: linear-gradient(45deg, var(--primary-accent), var(--secondary-accent));
        }

        body.loading-state .illustration-icon-wrapper i {
            color: var(--white);
        }

        .miko-text {
            font-size: 2.5rem;
            font-weight: 800;
            line-height: 1;
            font-family: 'Poppins', sans-serif;
        }

        .miko-text .mi {
            color: var(--white);
        }

        .miko-text .ko {
            color: var(--white);
        }

        /* Login section */
        .login-section {
            background-color: var(--white);
            border-radius: 50px 50px 0 0;
            padding: 1.5rem 1.5rem 1rem;
            box-shadow: 0 -8px 20px rgba(0, 0, 0, 0.15);
            width: 100%;
            max-width: 500px;
            margin: 0 auto;
            z-index: 2;
            flex-shrink: 0;
        }

        /* Responsive width adjustments */
        @media (max-width: 480px) {
            .login-section {
                max-width: 100%;
                margin: 0;
                border-radius: 20px 20px 0 0;
                padding: 1.2rem 1rem 1rem;
            }
        }

        @media (min-width: 481px) and (max-width: 768px) {
            .login-section {
                max-width: 90%;
                margin: 0 auto;
                padding: 1.5rem 1.2rem 1rem;
            }
        }

        @media (min-width: 769px) and (max-width: 1024px) {
            .login-section {
                max-width: 70%;
                margin: 0 auto;
                padding: 1.5rem 1.5rem 1rem;
            }
        }

        @media (min-width: 1025px) {
            .login-section {
                max-width: 500px;
                margin: 0 auto;
                padding: 1.5rem 1.5rem 1rem;
            }
        }

        .login-section h2 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--text-dark-elements);
        }

        .login-section p {
            font-size: 0.9rem;
            color: var(--subtle-detail);
            margin-bottom: 1.5rem;
        }

        .divider {
            display: flex;
            align-items: center;
            text-align: center;
            margin: 1.5rem 0;
            color: var(--subtle-detail);
            font-size: 0.9rem;
        }

        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            border-bottom: 1px solid var(--border-gray);
        }

        .divider:not(:empty)::before {
            margin-right: .5em;
        }

        .divider:not(:empty)::after {
            margin-left: .5em;
        }

        /* Buttons */
        .email-btn, .google-btn-custom {
            padding: 1rem;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            width: 100%;
            border: none;
            cursor: pointer;
            transition: var(--transition);
            margin-bottom: 0.8rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .google-btn-custom {
            background-color: var(--white);
            color: var(--primary-accent);
            border: 1px solid var(--primary-accent);
        }

        .google-btn-custom:hover {
            background-color: var(--primary-accent);
            color: var(--white);
            border-color: var(--primary-accent);
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(255, 107, 53, 0.3);
        }

        .email-btn {
            background-color: var(--primary-accent);
            color: var(--white);
            border: none;
        }

        .email-btn:hover {
            background-color: var(--secondary-accent);
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(255, 107, 53, 0.3);
        }

        .email-btn i, .google-btn-custom i {
            margin-right: 0.75rem;
            font-size: 1.2rem;
        }

        .phone-btn {
            background-color: var(--white);
            color: var(--primary-accent);
            border: 1px solid var(--primary-accent);
            padding: 1rem;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            width: 100%;
            cursor: pointer;
            transition: var(--transition);
            margin-top: 0.8rem;
        }

        .phone-btn:hover {
            background-color: var(--primary-accent);
            color: var(--white);
            border-color: var(--primary-accent);
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(255, 107, 53, 0.3);
        }

        /* Modal form steps */
        .modal-form-step {
            display: flex;
            flex-direction: column;
            gap: 0.8rem;
            align-items: flex-start;
        }

        .modal-form-step h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--text-dark-elements);
            text-align: left;
        }

        .modal-form-step p {
            font-size: 0.9rem;
            color: var(--subtle-detail);
            margin-bottom: 1.5rem;
            text-align: left;
        }

        .modal-form-step input {
            padding: 1rem;
            border: 1px solid var(--border-gray);
            border-radius: 100px;
            font-size: 1rem;
            color: var(--text-dark-elements);
            background-color: var(--white);
            width: 100%;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.08);
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;

            /* Allow text selection in inputs but prevent other gestures */
            -webkit-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
            touch-action: manipulation;

            /* Prevent iOS Safari auto-scroll on focus */
            transform: translateZ(0);
            -webkit-transform: translateZ(0);
        }

        .modal-form-step input:focus {
            outline: none;
            border-color: var(--primary-accent);
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.08), 0 0 0 2px rgba(255, 107, 53, 0.2);
        }

        .modal-form-step input::placeholder {
            color: var(--subtle-detail);
        }

        .password-input-container {
            position: relative;
            width: 100%;
        }

        .password-input-container input {
            padding-right: 3rem;
        }

        /* Style password input to show larger dots */
        .password-input-container input[type="password"] {
            font-family: 'Montserrat', sans-serif;
            font-size: 1rem; /* Smaller text size */
            font-weight: 600;
            line-height: 1.4;
            letter-spacing: 0.2em; /* Increase spacing between dots */
        }

        /* Ensure consistent styling when password is shown as text */
        .password-input-container input[type="text"] {
            font-family: 'Montserrat', sans-serif;
            font-size: 1rem; /* Smaller text size for visible text */
            font-weight: 600;
            line-height: 1.4;
            letter-spacing: normal; /* Normal spacing for visible text */
        }

        /* Enhanced password dot styling for better cross-browser support */
        .password-input-container input[type="password"] {
            font-family: 'Montserrat', sans-serif;
            -webkit-text-security: disc;
        }

        /* Webkit browsers - larger password dots */
        .password-input-container input[type="password"]::-webkit-input-placeholder {
            font-size: 1rem;
            letter-spacing: normal;
        }

        /* Focus state for password inputs */
        .password-input-container input[type="password"]:focus,
        .password-input-container input[type="text"]:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        /* Loading dots animation */
        .loading-dots {
            display: none; /* Hidden by default */
            align-items: center;
            gap: 4px;
            margin-left: 8px;
        }

        .loading-dots .dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: currentColor;
            animation: bounce 1.4s infinite ease-in-out both;
        }

        .loading-dots .dot:nth-child(1) {
            animation-delay: -0.32s;
        }

        .loading-dots .dot:nth-child(2) {
            animation-delay: -0.16s;
        }

        .loading-dots .dot:nth-child(3) {
            animation-delay: 0s;
        }

        @keyframes bounce {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1.2);
                opacity: 1;
            }
        }

        /* Show loading dots when button is in loading state */
        .btn-loading .loading-dots {
            display: inline-flex;
        }

        /* Optionally dim the button text when loading */
        .btn-loading .button-text {
            opacity: 0.7;
        }

        .password-toggle-btn {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--subtle-detail);
            cursor: pointer;
            font-size: 1.2rem;
            padding: 0.2rem;
        }

        .password-toggle-btn:hover {
            color: var(--text-dark-elements);
        }

        .terms-privacy {
            font-size: 0.7rem;
            color: var(--primary-accent);
            text-decoration: none;
            font-weight: 600;
            line-height: 1.4;
        }

        .terms-privacy a {
            color: var(--primary-accent);
            text-decoration: none;
            font-weight: 600;
        }

        .terms-privacy a:hover {
            text-decoration: underline;
        }

        /* Modal Overlays and Containers */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none; /* Hidden by default */
        }

        #forgotPasswordModalOverlay, #sendLoginLinkModalOverlay {
            background-color: rgba(60, 60, 60, 0.9);
        }

        /* Full Screen Modals */
        .full-screen-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--white);
            padding: 2rem 1.5rem;
            border-radius: 0;
            box-shadow: none;
            z-index: 1000;
            display: flex; /* Default display for flex container */
            flex-direction: column;
            opacity: 0; /* Default hidden state */
            pointer-events: none; /* Default no interaction */
            transition: opacity 0.3s ease-out; /* Transition for opacity */
        }

        /* Active full-screen modal state */
        .full-screen-modal.modal-state-active {
            opacity: 1;
            pointer-events: auto;
        }

        /* Bottom Sheet Modals */
        .bottom-sheet-modal {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            max-height: 70%;
            background-color: var(--white);
            border-top-left-radius: 20px;
            border-top-right-radius: 20px;
            padding: 1rem;
            box-shadow: 0 -8px 20px rgba(0, 0, 0, 0.15);
            transform: translateY(100%); /* Default hidden state (off-screen) */
            transition: transform 0.3s ease-out; /* Transition for transform */
            z-index: 1001;
            display: flex; /* Default display for flex container */
            flex-direction: column;
            opacity: 0; /* Default hidden state */
            pointer-events: none; /* Default no interaction */
        }

        /* Active bottom-sheet modal state */
        .bottom-sheet-modal.modal-state-active {
            transform: translateY(0); /* Bring into view */
            opacity: 1;
            pointer-events: auto;
        }

        /* This class will be applied to hide elements completely */
        .modal-state-hidden {
            display: none !important; /* Force hide */
        }

        /* Hide the old modal-header */
        .modal-header {
            display: none;
        }

        /* Modal Body */
        .modal-body {
            flex-grow: 1;
            padding-top: 0; /* Adjusted padding as header is removed */
            padding-bottom: 2rem;
            overflow-y: auto;
            position: relative; /* For positioning back button */
        }

        /* New container for back button and title */
        .modal-top-bar {
            display: flex;
            align-items: center;
            justify-content: space-between; /* Distribute items */
            padding: 1rem 0; /* Adjust padding as needed */
            margin-bottom: 1.5rem; /* Space below the top bar */
            border-bottom: 1px solid var(--border-gray); /* Optional: add a separator */
            position: sticky; /* Keep it sticky if modal content scrolls */
            top: 0;
            background-color: var(--white);
            z-index: 10;
        }

        .modal-top-bar .modal-back-btn {
            position: static; /* Remove absolute positioning */
            margin-right: auto; /* Push content to right, effectively centering h3 */
            padding: 0.5rem; /* Keep padding for touch target */
            background: none; /* Remove background */
            border: none; /* Remove border */
            color: black; /* Set icon color to black */
            font-size: 1.5rem; /* Adjust size as needed */
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .modal-top-bar .modal-back-btn:hover {
            color: var(--subtle-detail); /* Lighter on hover */
        }

        .modal-top-bar .modal-title-center {
            flex-grow: 1; /* Allow h3 to take available space */
            text-align: center; /* Center the text */
            margin: 0; /* Reset margin */
            font-size: 1.5rem; /* Keep original h3 size */
            font-weight: 700;
            color: var(--text-dark-elements);
        }

        /* Adjust content padding to avoid overlap with new top bar */
        .modal-body .modal-form-step,
        .modal-body .email-verification-needed,
        .modal-body .email-verification-sent,
        .modal-body .forgot-password-content,
        .modal-body .send-link-done {
            padding-top: 0; /* Reset padding as the top-bar handles spacing */
        }


        /* Continue Action Button */
        .continue-action-btn {
            background-color: var(--primary-accent);
            color: var(--white);
            border: none;
            padding: 1rem;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            width: 100%;
            cursor: pointer;
            transition: var(--transition);
            margin-top: 1.5rem;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15); /* Adjusted shadow for consistency */
        }

        .continue-action-btn:hover {
            background-color: var(--secondary-accent);
            color: var(--white);
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2); /* Adjusted shadow for consistency */
        }

        .font-bold {
            font-weight: 700;
            color: black;
        }

        /* Specific Bottom Sheet Modal styles */
        .bottom-sheet-modal .modal-header {
            border-bottom: none;
            padding-bottom: 1rem;
            margin-bottom: 1rem;
            position: relative;
            justify-content: center;
            align-items: center;
            background-color: transparent; /* No background for bottom sheet header */
        }

        .bottom-sheet-modal .modal-header h3 {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--text-dark-elements);
            flex-grow: 1;
            text-align: center;
            margin: 0;
        }

        .bottom-sheet-modal .modal-header .modal-back-btn {
            display: none; /* Back button hidden in bottom sheets */
        }

        .bottom-sheet-modal .modal-body {
            padding-top: 0;
            padding-bottom: 0;
            flex-grow: 1;
        }

        .bottom-sheet-modal .input-container {
            position: relative;
            width: 100%;
            margin-bottom: 1.5rem;
        }

        .bottom-sheet-modal .forgot-password-input {
            border: 1px solid var(--border-gray);
            box-shadow: none;
            padding: 1rem;
            border-radius: 100px;
            font-size: 1rem;
            color: var(--text-dark-elements);
            background-color: var(--white);
            width: 100%;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        .bottom-sheet-modal .forgot-password-input:focus {
            outline: none;
            border-color: var(--primary-accent);
            box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.2);
        }

        .bottom-sheet-modal .continue-action-btn {
            margin-top: 0;
            background-color: var(--primary-accent);
            box-shadow: 0 4px 10px rgba(255, 107, 53, 0.3);
        }

        .bottom-sheet-modal .continue-action-btn:hover {
            background-color: var(--secondary-accent);
            box-shadow: 0 6px 15px rgba(255, 107, 53, 0.4);
        }

        /* Content specific styles for modals */
        .forgot-password-content, .send-link-done, .email-verification-needed, .email-verification-sent {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            padding: 2rem 1rem;
            gap: 1rem;
        }

        .forgot-password-content .icon-wrapper, .send-link-done .icon-wrapper, .email-verification-needed .icon-wrapper, .email-verification-sent .icon-wrapper {
            background-color: #ffe0e0;
            border-radius: 50%;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
        }

        .forgot-password-content .icon-wrapper i, .send-link-done .icon-wrapper i, .email-verification-needed .icon-wrapper i, .email-verification-sent .icon-wrapper i {
            font-size: 3rem;
            color: var(--primary-accent);
        }



        @keyframes bounce {
            0% {
                transform: translateY(0);
            }
            100% {
                transform: translateY(-8px);
            }
        }

        /* Other animations */
        @keyframes breathing-pulse-strong {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.05); opacity: 0.9; }
            100% { transform: scale(1); opacity: 1; }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* OTP input */
        .otp-input-container {
            display: flex;
            gap: 10px;
            justify-content: center;
            width: 100%;
            margin: 0 auto 1rem auto;
        }

        .otp-input-box {
            width: 50px;
            height: 50px;
            text-align: center;
            font-size: 1.5rem;
            font-weight: bold;
            border: 1px solid #000;
            border-radius: 8px;
            background-color: var(--white);
            color: var(--text-dark-elements);
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.08);
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }

        .otp-input-box:focus {
            outline: none;
            border-color: var(--primary-accent);
            box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.2);
        }

        /* Button row */
        .button-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            margin-top: 1.5rem;
            gap: 10px;
        }

        .button-row .continue-action-btn {
            flex: 1;
            margin-top: 0;
        }

        .button-row .continue-action-btn:first-child {
            margin-right: 5px;
        }
        .button-row .continue-action-btn:last-child {
            margin-left: 5px;
        }

        .resend-otp-text {
            font-size: 0.9rem;
            color: var(--subtle-detail);
            margin-top: 1rem;
        }

        .resend-otp-text a {
            color: var(--primary-accent);
            text-decoration: none;
            font-weight: 600;
        }

        .resend-otp-text a:hover {
            text-decoration: underline;
        }

        /* Disabled button states for cooldown */
        .btn-disabled {
            background-color: var(--subtle-detail) !important;
            color: var(--white) !important;
            cursor: not-allowed !important;
            pointer-events: none !important;
            opacity: 0.6 !important;
            transform: none !important;
            box-shadow: none !important;
        }

        .btn-disabled:hover {
            background-color: var(--subtle-detail) !important;
            color: var(--white) !important;
            transform: none !important;
            box-shadow: none !important;
        }

        /* Disabled link states for cooldown */
        .link-disabled {
            color: var(--subtle-detail) !important;
            cursor: not-allowed !important;
            pointer-events: none !important;
            text-decoration: none !important;
        }

        .link-disabled:hover {
            color: var(--subtle-detail) !important;
            text-decoration: none !important;
        }

        /* Timer text styling */
        .timer-text {
            font-weight: 600;
            color: var(--subtle-detail);
        }

        /* Enhanced button disabled state for phone-btn */
        .phone-btn.btn-disabled {
            background-color: var(--subtle-detail) !important;
            color: var(--white) !important;
            border-color: var(--subtle-detail) !important;
        }

        /* Fast transitions for login link modals */
        #sendLoginLinkConfirmModal, #sendLoginLinkDoneModal {
            transition: transform 0.1s ease-out, opacity 0.1s ease-out !important;
        }

        /* Instant transition when going from confirm to done */
        .instant-transition {
            transition: none !important;
        }

        /* Modal State Management CSS */
        /* All modal state containers are hidden by default */
        .modal-state-hidden {
            display: none !important; /* Force hide */
        }

        /* Active full-screen modal state */
        .full-screen-modal.modal-state-active {
            display: flex !important; /* Override display: none from modal-state-hidden */
            opacity: 1;
            pointer-events: auto;
        }

        /* Active bottom-sheet modal state */
        .bottom-sheet-modal.modal-state-active {
            display: flex !important; /* Override display: none from modal-state-hidden */
            transform: translateY(0); /* Bring into view */
            opacity: 1;
            pointer-events: auto;
        }

        /* Content for Terms and Privacy pages */
        .policy-page {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--white);
            z-index: 1000;
            display: flex;
            flex-direction: column;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease-out;
            padding: 2rem 1.5rem;
            overflow-y: auto; /* Enable scrolling for content */
        }

        .policy-page.modal-state-active {
            opacity: 1;
            pointer-events: auto;
        }

        .policy-page .policy-header {
            display: flex;
            align-items: center;
            padding: 1rem 0;
            margin-bottom: 1.5rem;
            border-bottom: 1px solid var(--border-gray);
            position: sticky;
            top: 0;
            background-color: var(--white);
            z-index: 10;
            justify-content: space-between; /* Arrange items */
        }

        .policy-page .policy-header .modal-back-btn {
            margin-right: auto; /* Push title to center */
            padding: 0.5rem; /* Keep padding */
        }

        .policy-page .policy-header h2 {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-dark-elements);
            flex-grow: 1;
            text-align: center;
            margin: 0;
        }

        .policy-page .policy-content {
            flex-grow: 1;
            padding-bottom: 2rem;
        }

        .policy-page .policy-content h4 {
            font-size: 1.2rem;
            margin-top: 1.5rem;
            margin-bottom: 0.5rem;
            color: var(--text-dark-elements);
        }

        .policy-page .policy-content p {
            font-size: 0.9rem;
            line-height: 1.5;
            color: var(--text-dark-elements);
            margin-bottom: 1rem;
        }

        .policy-page .policy-content ul {
            list-style-type: disc;
            margin-left: 1.5rem;
            margin-bottom: 1rem;
        }

        .policy-page .policy-content ul li {
            font-size: 0.9rem;
            line-height: 1.5;
            color: var(--text-dark-elements);
            margin-bottom: 0.5rem;
        }

        /* Toast Notifications */
        #toast-container {
            position: fixed;
            top: max(60px, env(safe-area-inset-top, 20px) + 40px); /* Avoid notch and status bar */
            left: 50%;
            transform: translateX(-50%);
            z-index: 10000;
            display: flex;
            flex-direction: column;
            gap: 10px;
            width: 90%;
            max-width: 400px;
            pointer-events: none; /* Allow clicks to pass through container */
        }

        .toast {
            background-color: #333;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            opacity: 0;
            visibility: hidden;
            transform: translateY(-20px); /* Adjusted for top position */
            transition: opacity 0.3s ease-out, transform 0.3s ease-out, visibility 0.3s ease-out;
            display: flex;
            align-items: center;
            font-size: 0.95rem;
            pointer-events: auto; /* Re-enable pointer events for the toast itself */
        }

        .toast.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .toast.hide {
            opacity: 0;
            transform: translateY(-20px); /* Adjusted for top position */
        }

        .toast.success {
            background-color: var(--toast-success);
        }

        .toast.error {
            background-color: var(--toast-error);
        }

        .toast.info {
            background-color: var(--toast-info);
        }

        .toast.warning {
            background-color: var(--toast-warning);
        }

        .toast-icon {
            margin-right: 10px;
            font-size: 1.2em;
        }

        /* Media queries for responsiveness */
        @media (max-width: 480px) {
            .top-section h1 {
                font-size: 1.8rem;
            }
            .login-section {
                padding: 1.5rem 1.5rem 1rem;
            }
            .full-screen-modal, .policy-page {
                padding: 1.5rem 1rem;
            }
            .bottom-sheet-modal {
                padding: 1rem;
            }
            .bottom-sheet-modal .modal-header {
                padding-bottom: 0.5rem;
                margin-bottom: 0.5rem;
            }
            .otp-input-box {
                width: 40px;
                height: 40px;
                font-size: 1.2rem;
            }
            .otp-input-container {
                width: 100%;
            }

            /* Toast positioning for mobile */
            #toast-container {
                top: max(80px, env(safe-area-inset-top, 20px) + 60px);
                width: 95%;
            }
        }

        /* Landscape orientation adjustments */
        @media (orientation: landscape) and (max-height: 500px) {
            #toast-container {
                top: max(40px, env(safe-area-inset-top, 10px) + 30px);
            }
        }

        /* iPhone X and newer with notch */
        @media (max-width: 480px) and (min-height: 800px) {
            #toast-container {
                top: max(100px, env(safe-area-inset-top, 44px) + 56px);
            }
        }
    </style>
</head>
<body>
    <!-- Background Overlay -->
    <div id="backgroundOverlay"></div>

    <!-- Main Content -->
    <div class="main-content">
        <a href="#" class="skip-button" id="skipButton">Skip</a>

        <div class="top-section">
        <div class="illustration">
            <div class="illustration-icon-wrapper">
                <i class="fas fa-utensils"></i>
            </div>
        </div>
    </div>

    <div class="login-section" id="mainLoginSection">
        <h2>Sign up or log in</h2>
        <p>Sign up to get your discount</p>

        <button type="button" class="google-btn-custom" id="googleLoginToggle">
            <i class="fab fa-google"></i>
            <span id="buttonText">Continue with Google</span>
            <div class="loading-dots">
                <div class="dot"></div>
                <div class="dot"></div>
                <div class="dot"></div>
            </div>
        </button>

        <div class="divider">or</div>

        <button class="email-btn" id="emailLoginToggle">
            <i class="fas fa-envelope"></i> Continue with email
            <div class="loading-dots">
                <div class="dot"></div>
                <div class="dot"></div>
                <div class="dot"></div>
            </div>
        </button>

        <p class="terms-privacy">
            By signing up you agree to our <a href="/terms-and-conditions" id="termsAndConditionsLink">Terms and Conditions</a> and <a href="/privacy-policy" id="privacyPolicyLink">Privacy Policy</a>.
        </p>
    </div>
    </div> <!-- End main-content -->

    <!-- Full Screen Modals -->
    <div id="modalOverlay" class="modal-overlay">
        <!-- Email Input Modal State -->
        <div id="emailInputModal" class="full-screen-modal modal-state-hidden">
            <div class="modal-body">
                <div class="modal-top-bar">
                    <button class="modal-back-btn" data-back-target="initial-login">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <div></div> <!-- Empty div for spacing -->
                    <div></div> <!-- Empty div for spacing -->
                </div>
                <div class="modal-form-step">
                    <div class="illustration-icon-wrapper" style="width: 90px; height: 90px; margin-bottom: 1rem; background: none; box-shadow: none;">
                        <i class="fas fa-envelope" style="font-size: 4rem; color: #FF6B35; animation: none;"></i>
                    </div>
                    <h4 class="modal-title-center" style="font-size: 1.2rem; margin-bottom: 0.5rem;">What's your email?</h4>
                    <p>We'll check if you have an account</p>
                    <input type="email" id="modalEmailInput" placeholder="Email">
                    <button class="continue-action-btn" id="emailInputContinueBtn">
                        Continue
                        <div class="loading-dots">
                            <div class="dot"></div>
                            <div class="dot"></div>
                            <div class="dot"></div>
                        </div>
                    </button>
                </div>
            </div>
        </div>

        <!-- Email Password Modal State -->
        <div id="emailPasswordModal" class="full-screen-modal modal-state-hidden">
            <div class="modal-body">
                <div class="modal-top-bar">
                    <button class="modal-back-btn" data-back-target="email-input">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <div></div> <!-- Empty div for spacing -->
                    <div></div> <!-- Empty div for spacing -->
                </div>
                <div class="modal-form-step">
                    <div class="illustration-icon-wrapper" style="width: 90px; height: 90px; margin-bottom: 1rem; background: none; box-shadow: none;">
                        <i class="fas fa-lock" style="font-size: 4rem; color: #FF6B35; animation: none;"></i>
                    </div>
                    <h4 class="modal-title-center" style="font-size: 1.2rem; margin-bottom: 0.5rem;">Log in with your email</h4>
                    <p>Log in with your password to <span class="font-bold" id="emailPasswordDisplay"></span>. Or get a login link via email.</p>
                    <div class="password-input-container">
                        <input type="password" id="modalPasswordInput" placeholder="Password">
                        <button type="button" class="password-toggle-btn" data-password-input="modalPasswordInput">
                            <i class="fas fa-eye-slash"></i>
                        </button>
                    </div>
                    <a href="#" class="terms-privacy" id="forgotPasswordLink">I forgot my password</a>
                    <button class="continue-action-btn" id="emailLoginWithPasswordBtn">
                        Log in with password
                        <div class="loading-dots">
                            <div class="dot"></div>
                            <div class="dot"></div>
                            <div class="dot"></div>
                        </div>
                    </button>
                    <button class="phone-btn" id="sendLoginLinkBtn">Send me a login link</button>
                </div>
            </div>
        </div>

        <!-- Email Signup Modal State -->
        <div id="emailSignupModal" class="full-screen-modal modal-state-hidden">
            <div class="modal-body">
                <div class="modal-top-bar">
                    <button class="modal-back-btn" data-back-target="email-input">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <div></div> <!-- Empty div for spacing -->
                    <div></div> <!-- Empty div for spacing -->
                </div>
                <div class="modal-form-step">
                    <h4 class="modal-title-center" style="font-size: 1.2rem; margin-bottom: 0.5rem;">Create your password</h4>
                    <p>Enter a password to create your account.</p>
                    <div class="password-input-container">
                        <input type="password" id="modalSignupPasswordInput" placeholder="Password">
                        <button type="button" class="password-toggle-btn" data-password-input="modalSignupPasswordInput">
                            <i class="fas fa-eye-slash"></i>
                        </button>
                    </div>
                    <button class="continue-action-btn" id="emailSignupBtn">
                        Sign Up with Email
                        <div class="loading-dots">
                            <div class="dot"></div>
                            <div class="dot"></div>
                            <div class="dot"></div>
                        </div>
                    </button>
                </div>
            </div>
        </div>

        <!-- Email Verification Needed Modal State -->
        <div id="emailVerificationNeededModal" class="full-screen-modal modal-state-hidden">
            <div class="modal-body">
                <div class="modal-top-bar">
                    <button class="modal-back-btn" data-back-target="email-input">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <div></div> <!-- Empty div for spacing -->
                    <div></div> <!-- Empty div for spacing -->
                </div>
                <div class="modal-form-step">
                    <div class="illustration-icon-wrapper" style="width: 90px; height: 90px; margin-bottom: 1rem; background: none; box-shadow: none;">
                        <i class="fas fa-envelope" style="font-size: 4rem; color: #FF6B35; animation: none;"></i>
                    </div>
                    <h4 class="modal-title-center" style="font-size: 1.2rem; margin-bottom: 0.5rem;">Verify your email address to get started</h4>
                    <p>This helps us mitigate fraud and keep your personal data safe</p>
                    <button class="continue-action-btn" id="sendVerificationEmailBtn">
                        Send An OTP
                        <div class="loading-dots">
                            <div class="dot"></div>
                            <div class="dot"></div>
                            <div class="dot"></div>
                        </div>
                    </button>
                </div>
            </div>
        </div>

        <!-- Email Verification Sent Modal State -->
        <div id="emailVerificationSentModal" class="full-screen-modal modal-state-hidden">
            <div class="modal-body">
                <div class="modal-top-bar">
                    <button class="modal-back-btn" data-back-target="email-verification-needed">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <div></div> <!-- Empty div for spacing -->
                    <div></div> <!-- Empty div for spacing -->
                </div>
                <div class="icon-wrapper" style="background-color: rgba(255, 107, 53, 0.1);">
                    <i class="fas fa-envelope" style="color: var(--primary-accent);"></i>
                </div>
                <h4 class="modal-title-center" style="font-size: 1.2rem; margin-bottom: 0.5rem;">Enter the OTP sent to <span class="font-bold" id="otpEmailDisplay"></span></h4>
                <p>Please enter the 6-digit OTP sent to your email address.</p>
                <div class="otp-input-container" id="otpInputContainer" style="margin-bottom: 1rem;">
                    <input type="text" class="otp-input-box" maxlength="1" inputmode="numeric" pattern="[0-9]*" data-otp-index="0">
                    <input type="text" class="otp-input-box" maxlength="1" inputmode="numeric" pattern="[0-9]*" data-otp-index="1">
                    <input type="text" class="otp-input-box" maxlength="1" inputmode="numeric" pattern="[0-9]*" data-otp-index="2">
                    <input type="text" class="otp-input-box" maxlength="1" inputmode="numeric" pattern="[0-9]*" data-otp-index="3">
                    <input type="text" class="otp-input-box" maxlength="1" inputmode="numeric" pattern="[0-9]*" data-otp-index="4">
                    <input type="text" class="otp-input-box" maxlength="1" inputmode="numeric" pattern="[0-9]*" data-otp-index="5">
                </div>
                <div class="button-row">
                    <button class="continue-action-btn" id="changeEmailBtn" style="background-color: var(--white); color: var(--primary-accent); border: 1px solid var(--primary-accent);">Change email</button>
                    <button class="continue-action-btn" id="verifyOtpBtn">
                        Verify OTP
                        <div class="loading-dots">
                            <div class="dot"></div>
                            <div class="dot"></div>
                            <div class="dot"></div>
                        </div>
                    </button>
                </div>
                <p class="resend-otp-text">Didn't receive the code? <a href="#" id="resendOtpLink">Request Again</a></p>
            </div>
        </div>
    </div>

    <!-- Bottom Sheet Modals -->
    <div id="forgotPasswordModalOverlay" class="modal-overlay">
        <div id="forgotPasswordModal" class="bottom-sheet-modal modal-state-hidden">
            <div class="modal-body send-link-done">
                <div class="icon-wrapper">
                    <i class="fas fa-lock"></i>
                </div>
                <h4 class="modal-title-center" style="font-size: 1.2rem; margin-bottom: 0.5rem;">Forgot your password?</h4>
                <p>Confirm your email and we'll send you a link to set up a new one.</p>
                <div class="input-container">
                    <input type="email" id="forgotPasswordEmailInput" class="forgot-password-input" placeholder="Email" readonly>
                </div>
                <button class="continue-action-btn" id="forgotPasswordSendBtn">RESET PASSWORD</button>
            </div>
        </div>
    </div>

    <div id="sendLoginLinkModalOverlay" class="modal-overlay">
        <div id="sendLoginLinkConfirmModal" class="bottom-sheet-modal modal-state-hidden">
            <div class="modal-body send-link-done">
                <div class="icon-wrapper">
                    <i class="fas fa-lock"></i>
                </div>
                <h4 style="font-size: 1.1rem; margin-bottom: 0.5rem;">Good to see you again!</h4>
                <p style="margin-bottom: 1.5rem;">We'll send a login link to <span class="font-bold" id="loginLinkEmailDisplayConfirm"></span>. Check your inbox, click the link, and you're in.</p>
                <button class="continue-action-btn" id="confirmAndSendLoginLinkBtn">Send a login link</button>
            </div>
        </div>

        <div id="sendLoginLinkDoneModal" class="bottom-sheet-modal modal-state-hidden">
            <div class="modal-body send-link-done">
                <div class="modal-top-bar">
                    <button class="modal-back-btn" data-back-target="send-login-link-confirm">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <div></div> <!-- Empty div for spacing -->
                    <div></div> <!-- Empty div for spacing -->
                </div>
                <div class="icon-wrapper">
                    <i class="fas fa-lock"></i>
                </div>
                <h4 class="modal-title-center" style="font-size: 1.2rem; margin-bottom: 0.5rem;">Log in with your email</h4>
                <h4 style="font-size: 1.1rem; margin-bottom: 0.5rem;">Done</h4>
                <p>Check your email for the login link. If it is not there, please check your junk or spam mail folder.</p>
                <button class="continue-action-btn" id="closeSendLoginLinkDoneBtn">Close</button>
            </div>
        </div>
    </div>

    <!-- Privacy Policy Page -->
    <div id="privacyPolicyPage" class="policy-page modal-state-hidden">
        <div class="policy-header">
            <button class="modal-back-btn" data-back-target="initial-login">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h2>Privacy Policy</h2>
            <div></div> <!-- Empty div for spacing -->
        </div>
        <div class="policy-content">
            <h4>1. Introduction</h4>
            <p>Welcome to MIKO. We are committed to protecting your privacy and handling your data in an open and transparent manner. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you visit our website and use our services.</p>

            <h4>2. Information We Collect</h4>
            <p>We may collect personal information that you voluntarily provide to us when you register on the website, express an interest in obtaining information about us or our products and services, when you participate in activities on the website, or otherwise when you contact us.</p>
            <ul>
                <li><strong>Personal Data:</strong> Email address, password (hashed), and any other information you provide during registration or use of services.</li>
                <li><strong>Usage Data:</strong> Information about how you access and use the website, such as your IP address, browser type, pages viewed, and time spent on pages.</li>
            </ul>

            <h4>3. How We Use Your Information</h4>
            <p>We use the information we collect or receive:</p>
            <ul>
                <li>To facilitate account creation and login process.</li>
                <li>To send you marketing and promotional communications.</li>
                <li>To respond to your inquiries and offer support.</li>
                <li>To improve our website and services.</li>
                <li>To enforce our terms, conditions, and policies.</li>
            </ul>

            <h4>4. Disclosure of Your Information</h4>
            <p>We may share information with third parties when we have your consent to do so. We do not sell your personal data to third parties.</p>

            <h4>5. Security of Your Information</h4>
            <p>We use administrative, technical, and physical security measures to help protect your personal information. While we have taken reasonable steps to secure the personal information you provide to us, please be aware that despite our efforts, no security measures are perfect or impenetrable.</p>

            <h4>6. Your Privacy Rights</h4>
            <p>You have certain rights regarding your personal information, including the right to access, correct, or delete your data. To exercise these rights, please contact us using the information provided below.</p>

            <h4>7. Changes to This Privacy Policy</h4>
            <p>We may update this Privacy Policy from time to time. The updated version will be indicated by an updated "Revised" date and the updated version will be effective as soon as it is accessible. We encourage you to review this Privacy Policy frequently to be informed of how we are protecting your information.</p>

            <h4>8. Contact Us</h4>
            <p>If you have questions or comments about this Privacy Policy, you may contact us at: <EMAIL></p>
        </div>
    </div>

    <!-- Terms and Conditions Page -->
    <div id="termsAndConditionsPage" class="policy-page modal-state-hidden">
        <div class="policy-header">
            <button class="modal-back-btn" data-back-target="initial-login">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h2>Terms and Conditions</h2>
            <div></div> <!-- Empty div for spacing -->
        </div>
        <div class="policy-content">
            <h4>1. Acceptance of Terms</h4>
            <p>By accessing or using the MIKO website and services, you agree to be bound by these Terms and Conditions and all terms incorporated by reference. If you do not agree to all of these terms, do not use our website or services.</p>

            <h4>2. Eligibility</h4>
            <p>You must be at least 13 years old to use our services. By using the website, you represent and warrant that you are at least 13 years old.</p>
            <h4>3. User Accounts</h4>
            <p>When you create an account with us, you must provide information that is accurate, complete, and current at all times. Failure to do so constitutes a breach of the Terms, which may result in immediate termination of your account on our service.</p>
            <ul>
                <li>You are responsible for safeguarding the password that you use to access the Service and for any activities or actions under your password.</li>
                <li>You agree not to disclose your password to any third party.</li>
            </ul>

            <h4>4. Prohibited Activities</h4>
            <p>You may not access or use the website for any purpose other than that for which we make the website available. The website may not be used in connection with any commercial endeavors except those that are specifically endorsed or approved by us.</p>
            <ul>
                <li>Systematically retrieve data or other content from the website to create or compile, directly or indirectly, a collection, compilation, database, or directory without written permission from us.</li>
                <li>Make any unauthorized use of the website, including collecting usernames and/or email addresses of users by electronic or other means for the purpose of sending unsolicited email.</li>
                <li>Circumvent, disable, or otherwise interfere with security-related features of the website.</li>
            </ul>

            <h4>5. Intellectual Property</h4>
            <p>All content on the website, including text, graphics, logos, images, as well as the trademarks, service marks, and logos contained therein, are owned by or licensed to MIKO and are subject to copyright and other intellectual property rights under law.</p>

            <h4>6. Termination</h4>
            <p>We may terminate or suspend your account immediately, without prior notice or liability, for any reason whatsoever, including without limitation if you breach the Terms.</p>

            <h4>7. Governing Law</h4>
            <p>These Terms shall be governed and construed in accordance with the laws of [Your Country/State], without regard to its conflict of law provisions.</p>

            <h4>8. Changes to Terms</h4>
            <p>We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days' notice prior to any new terms taking effect. What constitutes a material change will be determined at our sole discretion.</p>

            <h4>9. Contact Us</h4>
            <p>If you have any questions about these Terms, please contact us at: <EMAIL></p>
        </div>
    </div>

    <div id="toast-container"></div> <!-- Toast container -->
    <script>
        const mainLoginSection = document.getElementById('mainLoginSection');
        const modalOverlay = document.getElementById('modalOverlay');
        const forgotPasswordModalOverlay = document.getElementById('forgotPasswordModalOverlay');
        const sendLoginLinkModalOverlay = document.getElementById('sendLoginLinkModalOverlay');

        // References to all modal containers
        const emailInputModal = document.getElementById('emailInputModal');
        const emailPasswordModal = document.getElementById('emailPasswordModal');
        const emailSignupModal = document.getElementById('emailSignupModal');
        const emailVerificationNeededModal = document.getElementById('emailVerificationNeededModal');
        const emailVerificationSentModal = document.getElementById('emailVerificationSentModal');
        const forgotPasswordModal = document.getElementById('forgotPasswordModal');
        const sendLoginLinkConfirmModal = document.getElementById('sendLoginLinkConfirmModal');
        const sendLoginLinkDoneModal = document.getElementById('sendLoginLinkDoneModal');

        // New references for policy pages
        const privacyPolicyPage = document.getElementById('privacyPolicyPage');
        const termsAndConditionsPage = document.getElementById('termsAndConditionsPage');

        // References to dynamic content elements
        const modalEmailInput = document.getElementById('modalEmailInput');
        const emailPasswordDisplay = document.getElementById('emailPasswordDisplay');
        const modalPasswordInput = document.getElementById('modalPasswordInput');
        const modalSignupPasswordInput = document.getElementById('modalSignupPasswordInput');
        const otpEmailDisplay = document.getElementById('otpEmailDisplay');
        const forgotPasswordEmailInput = document.getElementById('forgotPasswordEmailInput');
        const loginLinkEmailDisplayConfirm = document.getElementById('loginLinkEmailDisplayConfirm');

        // Store current email for password reset/login link
        window.currentEmailForPasswordReset = sessionStorage.getItem('miko_current_email') || '';

        /**
         * Displays a toast notification.
         * @param {string} message - The message to display in the toast.
         * @param {string} type - The type of toast (e.g., 'success', 'error', 'info', 'warning').
         */
        function showToast(message, type = 'info') {
            const toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                console.error('Toast container not found!');
                return;
            }

            const toast = document.createElement('div');
            toast.classList.add('toast', type);
            
            let iconClass = '';
            switch (type) {
                case 'success': iconClass = 'fas fa-check-circle'; break;
                case 'error': iconClass = 'fas fa-times-circle'; break;
                case 'warning': iconClass = 'fas fa-exclamation-triangle'; break;
                case 'info': default: iconClass = 'fas fa-info-circle'; break;
            }

            toast.innerHTML = `<i class="toast-icon ${iconClass}"></i><span>${message}</span>`;
            
            toastContainer.appendChild(toast);

            // Show toast
            requestAnimationFrame(() => {
                toast.classList.add('show');
            });

            // Hide toast after 3 seconds
            setTimeout(() => {
                toast.classList.remove('show');
                toast.classList.add('hide'); // Add hide class for fade-out animation
                toast.addEventListener('transitionend', () => {
                    toast.remove();
                }, { once: true });
            }, 3000);
        }
        window.showToast = showToast; // Make it globally accessible


        // Function to hide all modals and overlays
        function hideAllModals() {
            mainLoginSection.classList.remove('modal-state-hidden'); // Ensure main login is visible
            document.body.classList.remove('modal-open'); // Remove body class to enable scrolling and revert background

            // Hide overlays
            modalOverlay.style.display = 'none';
            forgotPasswordModalOverlay.style.display = 'none';
            sendLoginLinkModalOverlay.style.display = 'none';

            // Deactivate all full-screen modals
            emailInputModal.classList.remove('modal-state-active');
            emailPasswordModal.classList.remove('modal-state-active');
            emailSignupModal.classList.remove('modal-state-active');
            emailVerificationNeededModal.classList.remove('modal-state-active');
            emailVerificationSentModal.classList.remove('modal-state-active');

            // Deactivate all bottom-sheet modals
            forgotPasswordModal.classList.remove('modal-state-active');
            sendLoginLinkConfirmModal.classList.remove('modal-state-active');
            sendLoginLinkDoneModal.classList.remove('modal-state-active');

            // Deactivate policy pages
            privacyPolicyPage.classList.remove('modal-state-active');
            termsAndConditionsPage.classList.remove('modal-state-active');


            // Add modal-state-hidden to truly hide them and remove them from flow
            const allModals = [
                emailInputModal, emailPasswordModal, emailSignupModal,
                emailVerificationNeededModal, emailVerificationSentModal,
                forgotPasswordModal, sendLoginLinkConfirmModal, sendLoginLinkDoneModal,
                privacyPolicyPage, termsAndConditionsPage
            ];
            allModals.forEach(modal => {
                if (modal) {
                    modal.classList.add('modal-state-hidden');
                }
            });

            // Reset input values when modals are hidden
            if (modalEmailInput) modalEmailInput.value = '';
            if (modalPasswordInput) modalPasswordInput.value = '';
            if (modalSignupPasswordInput) modalSignupPasswordInput.value = '';
            if (forgotPasswordEmailInput) forgotPasswordEmailInput.value = '';
            document.querySelectorAll('.otp-input-box').forEach(input => input.value = '');
        }

        // Functions to show specific modal states
        function showEmailInputModal() {
            hideAllModals(); // First, hide everything
            mainLoginSection.classList.add('modal-state-hidden'); // Hide main login section
            modalOverlay.style.display = 'block'; // Show the main overlay
            document.body.classList.add('modal-open'); // Add body class for background and no-scroll

            // Remove hidden class and add active class for the specific modal
            emailInputModal.classList.remove('modal-state-hidden');
            emailInputModal.classList.add('modal-state-active');

            if (modalEmailInput) modalEmailInput.value = window.currentEmailForPasswordReset || '';
        }

        function showEmailPasswordModal() {
            hideAllModals();
            mainLoginSection.classList.add('modal-state-hidden');
            modalOverlay.style.display = 'block';
            document.body.classList.add('modal-open'); // Add body class for background and no-scroll
            emailPasswordModal.classList.remove('modal-state-hidden'); // Crucial
            emailPasswordModal.classList.add('modal-state-active');
            if (emailPasswordDisplay) emailPasswordDisplay.textContent = window.currentEmailForPasswordReset;
            if (modalPasswordInput) modalPasswordInput.value = '';
        }

        function showEmailSignupModal() {
            hideAllModals();
            mainLoginSection.classList.add('modal-state-hidden');
            modalOverlay.style.display = 'block';
            document.body.classList.add('modal-open'); // Add body class for background and no-scroll
            emailSignupModal.classList.remove('modal-state-hidden'); // Crucial
            emailSignupModal.classList.add('modal-state-active');
            if (modalSignupPasswordInput) modalSignupPasswordInput.value = '';
        }

        function showEmailVerificationNeededModal() {
            hideAllModals();
            mainLoginSection.classList.add('modal-state-hidden');
            modalOverlay.style.display = 'block';
            document.body.classList.add('modal-open'); // Add body class for background and no-scroll
            emailVerificationNeededModal.classList.remove('modal-state-hidden'); // Crucial
            emailVerificationNeededModal.classList.add('modal-state-active');
        }

        function showEmailVerificationSentModal() {
            hideAllModals();
            mainLoginSection.classList.add('modal-state-hidden');
            modalOverlay.style.display = 'block';
            document.body.classList.add('modal-open'); // Add body class for background and no-scroll
            emailVerificationSentModal.classList.remove('modal-state-hidden'); // Crucial
            emailVerificationSentModal.classList.add('modal-state-active');
            if (otpEmailDisplay) otpEmailDisplay.textContent = window.currentEmailForPasswordReset;
            document.querySelectorAll('.otp-input-box').forEach(input => input.value = '');
        }

        // New functions for policy pages
        function showPrivacyPolicyPage() {
            hideAllModals();
            mainLoginSection.classList.add('modal-state-hidden');
            document.body.classList.add('modal-open'); // Add body class for background and no-scroll
            privacyPolicyPage.classList.remove('modal-state-hidden');
            privacyPolicyPage.classList.add('modal-state-active');
            privacyPolicyPage.scrollTop = 0; // Scroll to top on open
        }

        function showTermsAndConditionsPage() {
            hideAllModals();
            mainLoginSection.classList.add('modal-state-hidden');
            document.body.classList.add('modal-open'); // Add body class for background and no-scroll
            termsAndConditionsPage.classList.remove('modal-state-hidden');
            termsAndConditionsPage.classList.add('modal-state-active');
            termsAndConditionsPage.scrollTop = 0; // Scroll to top on open
        }


        // Main function to render modal state based on hash
        function renderModalState(hash) {
            // Special handling for bottom sheet modals that layer on email-password modal
            const bottomSheetHashes = ['#forgot-password', '#send-login-link-confirm', '#send-login-link-done'];

            if (bottomSheetHashes.includes(hash)) {
                // Ensure emailPasswordModal is visible as the parent
                // We don't call hideAllModals here to preserve emailPasswordModal state
                mainLoginSection.classList.add('modal-state-hidden');
                modalOverlay.style.display = 'block';
                document.body.classList.add('modal-open');
                emailPasswordModal.classList.remove('modal-state-hidden');
                emailPasswordModal.classList.add('modal-state-active');

                // Then show the specific bottom sheet
                if (hash === '#forgot-password') {
                    forgotPasswordModalOverlay.style.display = 'block';
                    forgotPasswordModal.classList.remove('modal-state-hidden');
                    requestAnimationFrame(() => {
                        forgotPasswordModal.classList.add('modal-state-active');
                    });
                    if (forgotPasswordEmailInput) forgotPasswordEmailInput.value = window.currentEmailForPasswordReset || '';
                } else if (hash === '#send-login-link-confirm') {
                    sendLoginLinkModalOverlay.style.display = 'block';
                    sendLoginLinkConfirmModal.classList.remove('modal-state-hidden');
                    requestAnimationFrame(() => {
                        sendLoginLinkConfirmModal.classList.add('modal-state-active');
                    });
                    if (loginLinkEmailDisplayConfirm) loginLinkEmailDisplayConfirm.textContent = window.currentEmailForPasswordReset;
                } else if (hash === '#send-login-link-done') {
                    sendLoginLinkModalOverlay.style.display = 'block';
                    sendLoginLinkDoneModal.classList.remove('modal-state-hidden');
                    requestAnimationFrame(() => {
                        sendLoginLinkDoneModal.classList.add('modal-state-active');
                    });
                }
            } else {
                // For all other full-screen modals or initial state, hide everything first
                hideAllModals(); // This also shows the main login section if hash is empty
                switch (hash) {
                    case '#email-input':
                        mainLoginSection.classList.add('modal-state-hidden');
                        modalOverlay.style.display = 'block';
                        document.body.classList.add('modal-open');
                        emailInputModal.classList.remove('modal-state-hidden');
                        emailInputModal.classList.add('modal-state-active');
                        if (modalEmailInput) modalEmailInput.value = window.currentEmailForPasswordReset || '';
                        break;
                    case '#email-password': // This case will now only be hit if directly navigated to, not from bottom sheets
                        mainLoginSection.classList.add('modal-state-hidden');
                        modalOverlay.style.display = 'block';
                        document.body.classList.add('modal-open');
                        emailPasswordModal.classList.remove('modal-state-hidden');
                        emailPasswordModal.classList.add('modal-state-active');
                        if (emailPasswordDisplay) emailPasswordDisplay.textContent = window.currentEmailForPasswordReset;
                        if (modalPasswordInput) modalPasswordInput.value = '';
                        break;
                    case '#email-signup':
                        mainLoginSection.classList.add('modal-state-hidden');
                        modalOverlay.style.display = 'block';
                        document.body.classList.add('modal-open');
                        emailSignupModal.classList.remove('modal-state-hidden');
                        emailSignupModal.classList.add('modal-state-active');
                        if (modalSignupPasswordInput) modalSignupPasswordInput.value = '';
                        break;
                    case '#email-verification-needed':
                        mainLoginSection.classList.add('modal-state-hidden');
                        modalOverlay.style.display = 'block';
                        document.body.classList.add('modal-open');
                        emailVerificationNeededModal.classList.remove('modal-state-hidden');
                        emailVerificationNeededModal.classList.add('modal-state-active');
                        break;
                    case '#email-verification-sent':
                        mainLoginSection.classList.add('modal-state-hidden');
                        modalOverlay.style.display = 'block';
                        document.body.classList.add('modal-open');
                        emailVerificationSentModal.classList.remove('modal-state-hidden');
                        emailVerificationSentModal.classList.add('modal-state-active');
                        if (otpEmailDisplay) otpEmailDisplay.textContent = window.currentEmailForPasswordReset;
                        document.querySelectorAll('.otp-input-box').forEach(input => input.value = '');
                        break;
                    case '#privacy-policy':
                        mainLoginSection.classList.add('modal-state-hidden');
                        document.body.classList.add('modal-open');
                        privacyPolicyPage.classList.remove('modal-state-hidden');
                        privacyPolicyPage.classList.add('modal-state-active');
                        privacyPolicyPage.scrollTop = 0;
                        break;
                    case '#terms-and-conditions':
                        mainLoginSection.classList.add('modal-state-hidden');
                        document.body.classList.add('modal-open');
                        termsAndConditionsPage.classList.remove('modal-state-hidden');
                        termsAndConditionsPage.classList.add('modal-state-active');
                        termsAndConditionsPage.scrollTop = 0;
                        break;
                    default:
                        // This case is handled by hideAllModals() already
                        break;
                }
            }
        }
        window.renderModalState = renderModalState; // Make it globally accessible

        // Back button logic
        function handleBack() {
            const currentHash = window.location.hash;
            let targetHash = '';

            // Determine the previous state based on the current hash
            switch (currentHash) {
                case '#email-password':
                case '#email-signup':
                case '#email-verification-needed':
                case '#email-verification-sent':
                    targetHash = '#email-input';
                    break;
                case '#forgot-password':
                    closeForgotPasswordModalAndGoBack();
                    return; // Exit to prevent default history.back()
                case '#send-login-link-confirm':
                    targetHash = '#email-password'; // Go back to email-password from send-login-link-confirm
                    break;
                case '#send-login-link-done':
                    closeSendLoginLinkModalAndGoBack();
                    return; // Exit to prevent default history.back()
                case '#email-input':
                case '#privacy-policy': /* Added for policy pages */
                case '#terms-and-conditions': /* Added for policy pages */
                    targetHash = ''; // Empty hash means initial login state
                    break;
                default:
                    // Fallback for any other state, go to initial login or close all
                    targetHash = '';
                    break;
            }

            // Update history and render the target state
            if (targetHash === '') {
                history.pushState('', document.title, window.location.pathname + window.location.search);
                hideAllModals(); // This also shows the main login section
            } else {
                history.pushState(null, '', targetHash);
                renderModalState(targetHash);
            }
        }
        window.handleBack = handleBack; // Make it globally accessible

        // Specific close functions for bottom sheet modals (with transitions)
        function closeForgotPasswordModalAndGoBack() {
            forgotPasswordModal.classList.remove('modal-state-active'); // Remove active class to trigger transition
            forgotPasswordModal.addEventListener('transitionend', function handler() {
                forgotPasswordModalOverlay.style.display = 'none';
                forgotPasswordModal.classList.add('modal-state-hidden'); // Add back hidden class after transition
                forgotPasswordModal.removeEventListener('transitionend', handler);
                history.back(); // Go back in history after modal is fully hidden
            }, { once: true });
        }
        window.closeForgotPasswordModalAndGoBack = closeForgotPasswordModalAndGoBack;

        function closeSendLoginLinkModalAndGoBack() {
            sendLoginLinkConfirmModal.classList.remove('modal-state-active');
            sendLoginLinkDoneModal.classList.remove('modal-state-active');
            // Listen to one of them for transition end
            sendLoginLinkConfirmModal.addEventListener('transitionend', function handler() {
                sendLoginLinkModalOverlay.style.display = 'none';
                sendLoginLinkConfirmModal.classList.add('modal-state-hidden'); // Add back hidden class
                sendLoginLinkDoneModal.classList.add('modal-state-hidden'); // Add back hidden class
                sendLoginLinkConfirmModal.removeEventListener('transitionend', handler);
                history.back();
            }, { once: true });
        }
        window.closeSendLoginLinkModalAndGoBack = closeSendLoginLinkModalAndGoBack;

        // Password visibility toggle
        function togglePasswordVisibility(inputId, buttonElement) {
            const passwordInput = document.getElementById(inputId);
            const icon = buttonElement.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            }
        }
        window.togglePasswordVisibility = togglePasswordVisibility;

        // Handle skip button
        async function handleSkip() {
            try {
                console.log('⏭️ Skipping authentication...');

                const response = await fetch('login.php?path=/api/skip-authentication', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include'
                });

                const data = await response.json();

                if (data.success && data.skipped) {
                    console.log('✅ Authentication skipped successfully');

                    // Clean up any old localStorage data
                    localStorage.removeItem('logged-in');
                    localStorage.removeItem('not-logged-in-skipped');
                    sessionStorage.removeItem('miko_current_email');
                    sessionStorage.removeItem('logged_in_email');

                    // Redirect to home page
                    window.location.href = '/home.html';
                } else {
                    console.error('❌ Failed to skip authentication:', data.message);
                    showToast('Failed to skip authentication. Please try again.', 'error');
                }
            } catch (error) {
                console.error('🚨 Error during skip authentication:', error);
                showToast('An error occurred. Please try again.', 'error');
            }
        }
        window.handleSkip = handleSkip;

        // Handle successful login
        async function handleSuccessfulLogin(user) {
            // Session is now managed by secure cookies on the server side
            // No need to store session data in localStorage/sessionStorage
            console.log('✅ Login successful for user:', user.email);

            // Clean up any old localStorage data
            localStorage.removeItem('logged-in');
            localStorage.removeItem('not-logged-in-skipped');
            sessionStorage.removeItem('miko_current_email');
            sessionStorage.removeItem('logged_in_email');

            // Clear any skip authentication since user is now properly logged in
            try {
                await fetch('login.php?path=/api/clear-skip', { method: 'POST' });
                console.log('🧹 Cleared skip authentication');
            } catch (error) {
                console.log('⚠️ Could not clear skip authentication:', error);
            }

            // Redirect to home page - session will be validated there
            window.location.href = '/home.html';
        }

        // --- API Call Functions (now pointing to login.php) ---

        /**
         * Sends a login link email via the backend.
         * @param {string} email The recipient email.
         * @returns {Promise<object>} The JSON response from the backend.
         */
        async function sendLoginLinkEmail(email) {
            try {
                const response = await fetch('login.php?path=/api/send-login-link', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email: email })
                });
                const result = await response.json();

                // Handle server-side rate limiting
                if (!result.success && result.remaining_time) {
                    const sendBtn = document.getElementById('sendLoginLinkBtn');
                    const confirmBtn = document.getElementById('confirmAndSendLoginLinkBtn');

                    if (sendBtn) {
                        window.limit.startCooldownFromServer(sendBtn, window.limit.LAST_LOGIN_LINK_REQUEST_KEY, result.remaining_time, 'Send me a login link');
                    }
                    if (confirmBtn) {
                        window.limit.startCooldownFromServer(confirmBtn, window.limit.LAST_LOGIN_LINK_REQUEST_KEY, result.remaining_time, 'Send a login link');
                    }
                }

                return result;
            } catch (error) {
                console.error('Error sending login link email:', error);
                return { success: false, message: 'Network error or server issue.' };
            }
        }
        window.sendLoginLinkEmail = sendLoginLinkEmail;

        /**
         * Sends a password reset email via the backend.
         * @param {string} email The recipient email.
         * @returns {Promise<object>} The JSON response from the backend.
         */
        async function sendPasswordResetEmail(email) {
            try {
                const response = await fetch('login.php?path=/api/send-password-reset', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email: email })
                });
                const result = await response.json();

                // Handle server-side rate limiting
                if (!result.success && result.remaining_time) {
                    const resetBtn = document.getElementById('forgotPasswordSendBtn');

                    if (resetBtn) {
                        window.limit.startCooldownFromServer(resetBtn, window.limit.LAST_PASSWORD_RESET_REQUEST_KEY, result.remaining_time, 'RESET PASSWORD');
                    }
                }

                return result;
            } catch (error) {
                console.error('Error sending password reset email:', error);
                return { success: false, message: 'Network error or server issue.' };
            }
        }
        window.sendPasswordResetEmail = sendPasswordResetEmail;

        /**
         * Sends a verification OTP email via the backend.
         * @param {string} email The recipient email.
         * @returns {Promise<object>} The JSON response from the backend.
         */
        async function sendVerificationEmail(email) {
            try {
                const response = await fetch('login.php?path=/api/send-verification', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email: email })
                });
                const result = await response.json();

                // Handle server-side rate limiting
                if (!result.success && result.remaining_time) {
                    const sendBtn = document.getElementById('sendVerificationEmailBtn');
                    const resendLink = document.getElementById('resendOtpLink');

                    if (sendBtn) {
                        window.limit.startCooldownFromServer(sendBtn, window.limit.LAST_OTP_REQUEST_KEY, result.remaining_time, 'Send An OTP');
                    }
                    if (resendLink) {
                        window.limit.startCooldownFromServer(resendLink, window.limit.LAST_OTP_REQUEST_KEY, result.remaining_time, 'Request Again');
                    }
                }

                return result;
            } catch (error) {
                console.error('Error sending verification email:', error);
                return { success: false, message: 'Network error or server issue.' };
            }
        }
        window.sendVerificationEmail = sendVerificationEmail;

        /**
         * Verifies an OTP via the backend.
         * @param {string} email The user's email.
         * @param {string} otp The OTP to verify.
         * @returns {Promise<object>} The JSON response from the backend.
         */
        async function verifyOtp(email, otp) {
            try {
                const response = await fetch('login.php?path=/api/verify-otp', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email: email, otp: otp })
                });
                return await response.json();
            } catch (error) {
                console.error('Error verifying OTP:', error);
                return { success: false, message: 'Network error or server issue.' };
            }
        }
        window.verifyOtp = verifyOtp;

        /**
         * Handles magic link login by verifying the token with the backend.
         * @param {string} email The user's email.
         * @param {string} token The magic login token.
         */
        async function handleMagicLinkLogin(email, token) {
            document.body.style.backgroundColor = 'var(--white)';
            document.body.innerHTML = `
                <div class="loading-container">
                    <div class="illustration-icon-wrapper loading-icon-spin">
                        <i class="fas fa-utensils"></i>
                    </div>
                    <div class="miko-text">
                        <span class="mi">MI</span><span class="ko">KO</span>
                    </div>
                    <p class="loading-message">Logging you in...</p>
                    <div class="loading-dots"><span></span><span></span><span></span></div>
                </div>
            `;
            document.body.classList.add('loading-state');

            try {
                const response = await fetch('login.php?path=/api/verify-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email: email, token: token, type: 'login_link' })
                });
                const data = await response.json();

                if (data.success) {
                    showToast('Logged in successfully via magic link!', 'success');
                    const simulatedUser = {
                        uid: data.userId || 'magic-link-user-' + Math.random().toString(36).substring(2, 15),
                        email: email,
                        displayName: email.split('@')[0] || 'Magic Link User'
                    };
                    await handleSuccessfulLogin(simulatedUser);
                } else {
                    showToast(`Magic link login failed: ${data.message || 'Invalid or expired link.'}`, 'error');
                    // Redirect back to login page if magic link fails
                    setTimeout(() => {
                        window.location.href = '/login.html';
                    }, 2000);
                }
            } catch (error) {
                console.error('Error during magic link login:', error);
                showToast('An error occurred during magic link login. Please try again.', 'error');
                setTimeout(() => {
                    window.location.href = '/login.html';
                }, 2000);
            }
        }
        window.handleMagicLinkLogin = handleMagicLinkLogin;


        // Normalize email to lowercase and trim whitespace
        function normalizeEmail(email) {
            return email.toLowerCase().trim();
        }

        // Email Continue (Check existence and create user if not exists)
        async function handleEmailContinue() {
            const email = normalizeEmail(modalEmailInput.value);

            if (!window.validator.isValidEmail(email)) {
                showToast('Please enter a valid email address.', 'error');
                return;
            }

            window.currentEmailForPasswordReset = email;
            sessionStorage.setItem('miko_current_email', email);

            try {
                // First, check if user exists in our Supabase public.users table
                const checkUserResponse = await fetch('login.php?path=/api/check-user-existence', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email: email })
                });

                if (!checkUserResponse.ok) {
                    throw new Error(`HTTP error! status: ${checkUserResponse.status}`);
                }

                const checkUserData = await checkUserResponse.json();

                if (checkUserData.userExists) {
                    history.pushState(null, '', '#email-password');
                    renderModalState('#email-password');
                } else {
                    // If user does not exist, show verification needed modal
                    history.pushState(null, '', '#email-verification-needed');
                    renderModalState('#email-verification-needed');
                }
            } catch (error) {
                console.error('Error checking email existence or creating user:', error);
                showToast('An error occurred. Please try again later.', 'error');
                // Fallback to verification needed if any error occurs
                history.pushState(null, '', '#email-verification-needed');
                renderModalState('#email-verification-needed');
            }
        }

        // Email Signup (Create user in public.users)
        async function handleEmailSignup() {
            const email = window.currentEmailForPasswordReset;
            const password = modalSignupPasswordInput.value;

            if (!window.validator.isValidPassword(password)) {
                showToast('Password must be at least 6 characters long.', 'error');
                return;
            }
            if (!window.validator.isValidEmail(email)) {
                showToast('An invalid email was detected. Please restart the signup process.', 'error');
                return;
            }

            try {
                const response = await fetch('login.php?path=/api/signup', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email: email, password: password })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.success) {
                    showToast('Account created successfully! You are now logged in.', 'success');
                    const simulatedUser = {
                        id: data.userId, // Use the ID returned from Supabase
                        email: email,
                        displayName: email.split('@')[0] || 'Email User'
                    };
                    await handleSuccessfulLogin(simulatedUser);
                } else {
                    showToast(`Registration failed: ${data.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                console.error('Error during registration:', error);
                showToast('An error occurred during registration. Please try again later.', 'error');
            }
        }

        // Email Login with Password (Verify password against public.users)
        async function handleEmailLoginWithPassword() {
            const email = window.currentEmailForPasswordReset;
            const password = modalPasswordInput.value;

            if (!window.validator.isValidPassword(password)) {
                showToast('Please enter your password.', 'error');
                return;
            }
            if (!window.validator.isValidEmail(email)) {
                showToast('An invalid email was detected. Please restart the login process.', 'error');
                return;
            }

            try {
                const response = await fetch('login.php?path=/api/login-with-password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email: email, password: password })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.loggedIn) {
                    const simulatedUser = {
                        id: data.userId, // Use the ID returned from Supabase
                        email: email,
                        displayName: email.split('@')[0] || 'Email User'
                    };
                    await handleSuccessfulLogin(simulatedUser);
                } else {
                    showToast('Incorrect email or password. Please try again.', 'error');
                }
            } catch (error) {
                console.error('Error during login:', error);
                showToast('An error occurred during login. Please try again later.', 'error');
            }
        }

        // Handle opening send login link confirmation modal
        function handleOpenSendLoginLinkConfirmation() {
            window.location.hash = '#send-login-link-confirm';
        }

        // Handle confirming and sending login link
        async function handleConfirmAndSendLoginLink() {
            const email = window.currentEmailForPasswordReset;

            if (!window.validator.isValidEmail(email)) {
                showToast('An invalid email was detected. Please restart the login process.', 'error');
                return;
            }

            // Check client-side rate limiting first
            if (!window.limit.canRequest(window.limit.LAST_LOGIN_LINK_REQUEST_KEY, window.limit.LOGIN_LINK_COOLDOWN_SECONDS)) {
                const remainingTime = window.limit.getRemainingTime(window.limit.LAST_LOGIN_LINK_REQUEST_KEY, window.limit.LOGIN_LINK_COOLDOWN_SECONDS);
                showToast(`Please wait ${window.limit.formatTime(remainingTime)} before requesting another login link.`, 'warning');
                return;
            }

            // Start cooldown immediately for better UX
            window.limit.startCooldownForAllButtons(window.limit.LAST_LOGIN_LINK_REQUEST_KEY);

            // Make transition instant for better UX
            const confirmModal = document.getElementById('sendLoginLinkConfirmModal');
            const doneModal = document.getElementById('sendLoginLinkDoneModal');
            if (confirmModal) confirmModal.classList.add('instant-transition');
            if (doneModal) doneModal.classList.add('instant-transition');

            // Redirect immediately for better UX
            window.location.hash = '#send-login-link-done';

            // Remove instant transition class after a short delay to restore normal transitions
            setTimeout(() => {
                if (confirmModal) confirmModal.classList.remove('instant-transition');
                if (doneModal) doneModal.classList.remove('instant-transition');
            }, 100);

            // Send email in background
            sendLoginLinkEmail(email).then(result => {
                if (!result.success) {
                    // If request failed, reset the buttons and show error
                    window.limit.notifyCountdownComplete(window.limit.LAST_LOGIN_LINK_REQUEST_KEY);
                    showToast(result.message || 'Failed to send login link. Please try again.', 'error');
                }
            }).catch(error => {
                console.error('Error sending login link:', error);
                // If request failed, reset the buttons
                window.limit.notifyCountdownComplete(window.limit.LAST_LOGIN_LINK_REQUEST_KEY);
                showToast('An error occurred while sending the login link. Please try again later.', 'error');
            });
        }

        // Handle forgot password
        function handleForgotPassword(event) {
            event.preventDefault(); // Prevent the default link behavior
            window.location.hash = '#forgot-password';
        }

        // Handle sending verification email
        async function handleSendVerificationEmail() {
            const email = window.currentEmailForPasswordReset;

            if (!window.validator.isValidEmail(email)) {
                showToast('An invalid email was detected. Please restart the login process.', 'error');
                return;
            }

            // Check client-side rate limiting first
            if (!window.limit.canRequest(window.limit.LAST_OTP_REQUEST_KEY, window.limit.OTP_COOLDOWN_SECONDS)) {
                const remainingTime = window.limit.getRemainingTime(window.limit.LAST_OTP_REQUEST_KEY, window.limit.OTP_COOLDOWN_SECONDS);
                showToast(`Please wait ${window.limit.formatTime(remainingTime)} before requesting another OTP.`, 'warning');
                return;
            }

            // Start cooldown immediately for better UX
            window.limit.startCooldownForAllButtons(window.limit.LAST_OTP_REQUEST_KEY);

            // Redirect immediately for better UX
            window.location.hash = '#email-verification-sent';

            // Send email in background
            sendVerificationEmail(email).then(result => {
                if (!result.success) {
                    // If request failed, reset the buttons and show error
                    window.limit.notifyCountdownComplete(window.limit.LAST_OTP_REQUEST_KEY);
                    showToast(result.message || 'Failed to send verification email. Please try again.', 'error');
                    // Optionally redirect back to verification needed
                    // window.location.hash = '#email-verification-needed';
                }
            }).catch(error => {
                console.error('Error sending verification email:', error);
                // If request failed, reset the buttons
                window.limit.notifyCountdownComplete(window.limit.LAST_OTP_REQUEST_KEY);
                showToast('An error occurred while sending the verification email. Please try again later.', 'error');
                // Optionally redirect back to verification needed
                // window.location.hash = '#email-verification-needed';
            });
        }

        // Handle resending verification email
        async function handleResendVerificationEmail() {
            const email = window.currentEmailForPasswordReset;

            if (!window.validator.isValidEmail(email)) {
                showToast('An invalid email was detected. Please restart the login process.', 'error');
                return;
            }

            // Check client-side rate limiting first
            if (!window.limit.canRequest(window.limit.LAST_OTP_REQUEST_KEY, window.limit.OTP_COOLDOWN_SECONDS)) {
                const remainingTime = window.limit.getRemainingTime(window.limit.LAST_OTP_REQUEST_KEY, window.limit.OTP_COOLDOWN_SECONDS);
                showToast(`Please wait ${window.limit.formatTime(remainingTime)} before requesting another OTP.`, 'warning');
                return;
            }

            // Start cooldown immediately for better UX
            window.limit.startCooldownForAllButtons(window.limit.LAST_OTP_REQUEST_KEY);

            // Show success message immediately
            showToast('Verification email sent successfully!', 'success');

            // Send email in background
            sendVerificationEmail(email).then(result => {
                if (!result.success) {
                    // If request failed, reset the buttons and show error
                    window.limit.notifyCountdownComplete(window.limit.LAST_OTP_REQUEST_KEY);
                    showToast(result.message || 'Failed to resend verification email. Please try again.', 'error');
                }
            }).catch(error => {
                console.error('Error resending verification email:', error);
                // If request failed, reset the buttons
                window.limit.notifyCountdownComplete(window.limit.LAST_OTP_REQUEST_KEY);
                showToast('An error occurred while resending the verification email. Please try again later.', 'error');
            });
        }

        // Handle OTP verification
        async function handleVerifyOtp() {
            const email = window.currentEmailForPasswordReset;
            const otpInputs = document.querySelectorAll('.otp-input-box');
            const otp = Array.from(otpInputs).map(input => input.value).join('');

            if (!window.validator.isValidEmail(email)) {
                showToast('An invalid email was detected. Please restart the login process.', 'error');
                return;
            }

            if (otp.length !== 6) {
                showToast('Please enter the complete 6-digit OTP.', 'error');
                return;
            }

            try {
                const result = await verifyOtp(email, otp);

                if (result.success) {
                    // Navigate to signup modal after successful verification
                    window.location.hash = '#email-signup';
                } else {
                    showToast(result.message || 'Invalid OTP. Please try again.', 'error');
                }
            } catch (error) {
                console.error('Error verifying OTP:', error);
                showToast('An error occurred while verifying the OTP. Please try again later.', 'error');
            }
        }

        // Handle forgot password send
        async function handleForgotPasswordSend() {
            const email = window.currentEmailForPasswordReset;

            if (!window.validator.isValidEmail(email)) {
                showToast('An invalid email was detected. Please restart the login process.', 'error');
                return;
            }

            // Check client-side rate limiting first
            if (!window.limit.canRequest(window.limit.LAST_PASSWORD_RESET_REQUEST_KEY, window.limit.PASSWORD_RESET_COOLDOWN_SECONDS)) {
                const remainingTime = window.limit.getRemainingTime(window.limit.LAST_PASSWORD_RESET_REQUEST_KEY, window.limit.PASSWORD_RESET_COOLDOWN_SECONDS);
                showToast(`Please wait ${window.limit.formatTime(remainingTime)} before requesting another password reset.`, 'warning');
                return;
            }

            // Start cooldown immediately for better UX
            window.limit.startCooldownForAllButtons(window.limit.LAST_PASSWORD_RESET_REQUEST_KEY);

            // Show success message and navigate back immediately
            showToast('Password reset email sent successfully! Check your inbox.', 'success');
            history.back();

            // Send email in background
            sendPasswordResetEmail(email).then(result => {
                if (!result.success) {
                    // If request failed, reset the buttons and show error
                    window.limit.notifyCountdownComplete(window.limit.LAST_PASSWORD_RESET_REQUEST_KEY);
                    showToast(result.message || 'Failed to send password reset email. Please try again.', 'error');
                }
            }).catch(error => {
                console.error('Error sending password reset email:', error);
                // If request failed, reset the buttons
                window.limit.notifyCountdownComplete(window.limit.LAST_PASSWORD_RESET_REQUEST_KEY);
                showToast('An error occurred while sending the password reset email. Please try again later.', 'error');
            });
        }


        // Event Listeners
        window.addEventListener('popstate', (event) => {
            renderModalState(window.location.hash);
        });

        document.addEventListener('DOMContentLoaded', () => {
            // Prevent going back to index.html
            if (window.history.length > 1) {
                window.history.replaceState(null, null, window.location.href);
            }

            // Add popstate listener to prevent back navigation to index.html
            window.addEventListener('popstate', (event) => {
                // If user tries to go back, stay on login page
                window.history.pushState(null, null, window.location.href);
            });

            // Push initial state to prevent back navigation
            window.history.pushState(null, null, window.location.href);

            // Check if already logged in
            if (localStorage.getItem('logged-in') === 'true') {
                document.body.style.backgroundColor = 'var(--white)';
                document.body.innerHTML = `
                    <div class="loading-container">
                        <div class="illustration-icon-wrapper loading-icon-spin">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <div class="miko-text">
                            <span class="mi">MI</span><span class="ko">KO</span>
                        </div>
                        <p class="loading-message">Already logged in, redirecting to your profile...</p>
                        <div class="loading-dots"><span></span><span></span><span></span></div>
                    </div>
                `;
                document.body.classList.add('loading-state');

                setTimeout(() => {
                    window.location.href = '/home.html';
                }, 2500);

                return;
            }

            // Initial rendering based on URL hash or magic link
            const urlParams = new URLSearchParams(window.location.search);
            const loginToken = urlParams.get('token');
            const loginEmail = urlParams.get('email');

            if (loginToken && loginEmail) {
                history.replaceState(null, '', window.location.pathname);
                window.handleMagicLinkLogin(loginEmail, loginToken);
            } else if (window.location.hash) {
                renderModalState(window.location.hash);
            } else {
                // If no hash and not magic link, ensure main login section is visible
                hideAllModals(); // This ensures all modals are hidden
                mainLoginSection.classList.remove('modal-state-hidden'); // Explicitly show main login
            }

            // Attach event listeners to static elements
            document.getElementById('skipButton').addEventListener('click', handleSkip);
            document.getElementById('emailLoginToggle').addEventListener('click', () => {
                window.currentEmailForPasswordReset = ''; // Clear email on initial email login click
                sessionStorage.removeItem('miko_current_email');
                history.pushState(null, '', '#email-input');
                renderModalState('#email-input');
            });
            document.getElementById('googleLoginToggle').addEventListener('click', () => {
                window.location.href = 'google.php?action=login_google';
            });

            // Terms and Privacy links - now handled by router
            // No need for explicit event listeners here, as browser will navigate to full URL
            // and router.php will handle it.
            // The renderModalState on DOMContentLoaded or popstate will pick up the hash.

            // Modal Overlay click listeners to close modals
            modalOverlay.addEventListener('click', (event) => {
                // Only close if clicking on the overlay itself, not content within
                if (event.target === modalOverlay) {
                    handleBack(); // Use handleBack to manage history correctly
                }
            });

            forgotPasswordModalOverlay.addEventListener('click', (event) => {
                if (event.target === forgotPasswordModalOverlay) {
                    closeForgotPasswordModalAndGoBack();
                }
            });

            sendLoginLinkModalOverlay.addEventListener('click', (event) => {
                if (event.target === sendLoginLinkModalOverlay) {
                    closeSendLoginLinkModalAndGoBack();
                }
            });

            // Attach event listeners for elements within the modals (now static)
            // Email Input Modal
            document.getElementById('emailInputContinueBtn').addEventListener('click', handleEmailContinue);

            // Back buttons within modals and policy pages
            document.querySelectorAll('.modal-back-btn').forEach(button => {
                button.addEventListener('click', handleBack);
            });

            // Email Password Modal
            document.getElementById('emailLoginWithPasswordBtn').addEventListener('click', handleEmailLoginWithPassword);
            document.getElementById('sendLoginLinkBtn').addEventListener('click', handleOpenSendLoginLinkConfirmation);
            document.getElementById('forgotPasswordLink').addEventListener('click', handleForgotPassword);

            // Email Signup Modal
            document.getElementById('emailSignupBtn').addEventListener('click', handleEmailSignup);

            // Email Verification Needed Modal
            document.getElementById('sendVerificationEmailBtn').addEventListener('click', handleSendVerificationEmail);

            // Email Verification Sent Modal
            document.getElementById('changeEmailBtn').addEventListener('click', () => {
                history.pushState(null, '', '#email-input');
                renderModalState('#email-input');
            });
            document.getElementById('verifyOtpBtn').addEventListener('click', handleVerifyOtp);
            document.getElementById('resendOtpLink').addEventListener('click', (event) => {
                event.preventDefault();
                handleResendVerificationEmail();
            });

            // OTP Input Box Navigation
            const otpInputContainer = document.getElementById('otpInputContainer');
            if (otpInputContainer) {
                otpInputContainer.addEventListener('input', (event) => {
                    const target = event.target;
                    if (target.classList.contains('otp-input-box') && target.value.length === 1) {
                        const nextInput = target.nextElementSibling;
                        if (nextInput && nextInput.classList.contains('otp-input-box')) {
                            nextInput.focus();
                        }
                    }
                });

                otpInputContainer.addEventListener('keydown', (event) => {
                    const target = event.target;
                    if (target.classList.contains('otp-input-box') && event.key === 'Backspace' && target.value === '') {
                        const prevInput = target.previousElementSibling;
                        if (prevInput && prevInput.classList.contains('otp-input-box')) {
                            prevInput.focus();
                        }
                    }
                });
            }

            // Forgot Password Modal
            document.getElementById('forgotPasswordSendBtn').addEventListener('click', handleForgotPasswordSend);

            // Send Login Link Confirm Modal
            document.getElementById('confirmAndSendLoginLinkBtn').addEventListener('click', handleConfirmAndSendLoginLink);

            // Send Login Link Done Modal
            document.getElementById('closeSendLoginLinkDoneBtn').addEventListener('click', closeSendLoginLinkModalAndGoBack);


            // Attach global password toggle listeners using event delegation
            document.body.addEventListener('click', (event) => {
                const toggleButton = event.target.closest('.password-toggle-btn');
                if (toggleButton) {
                    const inputId = toggleButton.dataset.passwordInput;
                    togglePasswordVisibility(inputId, toggleButton);
                }
            });

            // Initialize cooldown UI for buttons that need it
            if (window.limit) {
                window.limit.updateCooldownUI(
                    document.getElementById('sendLoginLinkBtn'),
                    window.limit.LAST_LOGIN_LINK_REQUEST_KEY,
                    window.limit.LOGIN_LINK_COOLDOWN_SECONDS,
                    'Send me a login link'
                );
                window.limit.updateCooldownUI(
                    document.getElementById('sendVerificationEmailBtn'),
                    window.limit.LAST_OTP_REQUEST_KEY,
                    window.limit.OTP_COOLDOWN_SECONDS,
                    'Send An OTP'
                );
                window.limit.updateCooldownUI(
                    document.getElementById('resendOtpLink'),
                    window.limit.LAST_OTP_REQUEST_KEY,
                    window.limit.OTP_COOLDOWN_SECONDS,
                    'Request Again'
                );
                window.limit.updateCooldownUI(
                    document.getElementById('forgotPasswordSendBtn'),
                    window.limit.LAST_PASSWORD_RESET_REQUEST_KEY,
                    window.limit.PASSWORD_RESET_COOLDOWN_SECONDS,
                    'RESET PASSWORD'
                );
                window.limit.updateCooldownUI(
                    document.getElementById('confirmAndSendLoginLinkBtn'),
                    window.limit.LAST_LOGIN_LINK_REQUEST_KEY,
                    window.limit.LOGIN_LINK_COOLDOWN_SECONDS,
                    'Send a login link'
                );
            } else {
                console.error("Error: window.limit is not defined. Cooldown UI will not update.");
            }
        });

        // Prevent elastic scroll on iOS
        function preventElasticScroll() {
            let startY = 0;
            const mainContent = document.querySelector('.main-content');

            if (mainContent) {
                mainContent.addEventListener('touchstart', function(e) {
                    startY = e.touches[0].pageY;
                }, { passive: false });

                mainContent.addEventListener('touchmove', function(e) {
                    const y = e.touches[0].pageY;
                    const scrollTop = mainContent.scrollTop;
                    const scrollHeight = mainContent.scrollHeight;
                    const clientHeight = mainContent.clientHeight;

                    // Prevent scrolling past the top
                    if (scrollTop <= 0 && y > startY) {
                        e.preventDefault();
                    }

                    // Prevent scrolling past the bottom
                    if (scrollTop + clientHeight >= scrollHeight && y < startY) {
                        e.preventDefault();
                    }
                }, { passive: false });
            }
        }

        // Initialize elastic scroll prevention
        preventElasticScroll();

        // Loading animation utility functions
        window.showButtonLoading = function(button) {
            if (button) {
                button.classList.add('btn-loading');
                button.disabled = true;
                // Add a data attribute to track loading state
                button.setAttribute('data-loading', 'true');
            }
        };

        window.hideButtonLoading = function(button) {
            if (button) {
                button.classList.remove('btn-loading');
                button.disabled = false;
                // Remove loading state tracking
                button.removeAttribute('data-loading');
            }
        };

        // Prevent multiple clicks utility function
        window.preventMultipleClicks = function(button, callback, duration = 2000) {
            // Check if button is already processing
            if (button.getAttribute('data-loading') === 'true' || button.disabled) {
                console.log('Button click prevented - already processing');
                return;
            }

            // Show loading immediately
            showButtonLoading(button);

            // Execute the callback
            if (typeof callback === 'function') {
                try {
                    const result = callback();

                    // If callback returns a promise, handle it
                    if (result && typeof result.then === 'function') {
                        result
                            .finally(() => {
                                hideButtonLoading(button);
                            });
                    } else {
                        // For non-promise callbacks, hide loading after duration
                        setTimeout(() => {
                            hideButtonLoading(button);
                        }, duration);
                    }
                } catch (error) {
                    console.error('Button callback error:', error);
                    hideButtonLoading(button);
                }
            } else {
                // No callback provided, just show loading for duration
                setTimeout(() => {
                    hideButtonLoading(button);
                }, duration);
            }
        };

        // Example usage for testing - you can remove this
        // To show loading: showButtonLoading(document.getElementById('googleLoginToggle'));
        // To hide loading: hideButtonLoading(document.getElementById('googleLoginToggle'));

        // Prevent iOS Safari auto-scroll on input focus
        function preventAutoScroll() {
            const inputs = document.querySelectorAll('input, textarea');

            inputs.forEach(input => {
                input.addEventListener('focus', function(e) {
                    // Prevent default scroll behavior
                    e.preventDefault();

                    // Small delay to ensure the keyboard is shown, then scroll back
                    setTimeout(() => {
                        const mainContent = document.querySelector('.main-content');
                        if (mainContent) {
                            // Keep the current scroll position
                            const currentScrollTop = mainContent.scrollTop;
                            mainContent.scrollTop = currentScrollTop;
                        }

                        // Also prevent window scroll
                        window.scrollTo(0, 0);
                    }, 100);
                }, { passive: false });

                input.addEventListener('blur', function() {
                    // Ensure scroll position stays controlled when input loses focus
                    setTimeout(() => {
                        window.scrollTo(0, 0);
                    }, 100);
                });
            });
        }

        // Initialize auto-scroll prevention
        preventAutoScroll();

        // Re-initialize when new inputs are added (for dynamic content)
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    preventAutoScroll();
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    </script>
</body>
</html>
