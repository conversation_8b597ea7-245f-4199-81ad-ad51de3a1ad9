document.addEventListener('DOMContentLoaded', () => {
    const style = document.createElement('style');
    style.textContent = `
        /* Define color variables to match the main site theme */
        :root {
            --primary-accent: #ff5018;
            --secondary-accent: #FF7F50;
            --neutral-base: #F7F7F7;
            --text-dark-elements: #333333;
            --subtle-detail: #A7A7A7;
            --pure-white: #FFFFFF;
            --shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;

            /* Light Theme Colors (Default) */
            --body-bg: var(--neutral-base);
            --text-color: var(--text-dark-elements);
            --card-bg: var(--pure-white);
            --header-bg: var(--primary-accent);
            --header-text: var(--pure-white);
        }

        /* Dark Mode Styles */
        @media (prefers-color-scheme: dark) {
            :root {
                --body-bg: #1a1a1a; /* Darker background */
                --text-color: #e0e0e0; /* Lighter text */
                --card-bg: #2c2c2c; /* Darker card background */
                --header-bg: var(--primary-accent); /* Primary accent can remain or be a darker shade */
                --header-text: var(--pure-white);
            }
        }

        /* Styles for the full-screen QR modal */
        .qr-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh; /* Use 100vh for full viewport height */
            background-color: black; /* Changed back to solid black */
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000; /* Ensure it's on top */
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .qr-modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .qr-modal-content {
            background-color: transparent; /* No background */
            padding: 0;
            border-radius: 0;
            box-shadow: none;
            width: 100%;
            height: 100vh; /* Use 100vh for full viewport height */
            max-width: 100%;
            max-height: 100vh; /* Ensure it spans full height */
            overflow: hidden;
            position: relative; /* Needed for absolute positioning of video/canvas */
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* Style to prevent body scrolling when modal is open and make it transparent */
        body.no-scroll {
            overflow: hidden;
            /* Removed background-color: transparent !important; to revert body background */
        }

        /* Styles for the video and canvas elements within the modal */
        #qr-video, #qr-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover; /* Ensures video covers the entire area */
            display: block;
        }
        #qr-canvas {
            display: none; /* Canvas is hidden, used for processing frames */
        }
        .qr-error-message {
            color: var(--pure-white); /* Changed to use variable */
            font-size: 1.2rem;
            text-align: center;
            padding: 20px;
            z-index: 1001; /* Ensure error message is above black background */
        }

        /* Styles for the success feedback directly on the video */
        .qr-scan-success-feedback {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--primary-accent); /* Changed to use variable */
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1002; /* Above the video */
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .qr-scan-success-feedback.show {
            opacity: 1;
            visibility: visible;
        }

        .qr-success-icon-container {
            background-color: var(--pure-white); /* Changed to use variable */
            border-radius: 50%;
            width: 100px;
            height: 100px;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            transform: scale(0.8);
            opacity: 0;
            animation: popIn 0.3s forwards;
        }

        .qr-success-icon-container .fas.fa-check {
            color: var(--primary-accent); /* Changed to use variable */
            font-size: 50px;
        }

        @keyframes popIn {
            from { transform: scale(0.8); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }

        /* Styles for the corner brackets */
        .qr-corner {
            position: absolute;
            width: 50px; /* Length of each arm of the L-shape */
            height: 50px;
            border: 3px solid var(--pure-white); /* Changed to use variable */
            z-index: 1001; /* Above the video */
            opacity: 1; /* Always visible now, no longer hidden initially */
            transition: opacity 0.5s ease; /* Smooth transition for appearance */
            animation: breathe 1.2s infinite alternate ease-in-out; /* Breathing animation - faster */
        }

        /* Keyframes for the breathing animation with scale */
        @keyframes breathe {
            0% {
                opacity: 0.3; /* More noticeable pulse */
                transform: scale(0.95); /* Slightly smaller */
            }
            100% {
                opacity: 1;
                transform: scale(1); /* Original size */
            }
        }

        /* .qr-corner.show-corners is no longer needed as they are always visible */
        .qr-corner.top-left {
            top: calc(50% - 125px - 3px); /* Half of square size - half of border width */
            left: calc(50% - 125px - 3px);
            border-right: none;
            border-bottom: none;
            border-top-left-radius: 8px;
        }
        .qr-corner.top-right {
            top: calc(50% - 125px - 3px);
            right: calc(50% - 125px - 3px);
            border-left: none;
            border-bottom: none;
            border-top-right-radius: 8px;
        }
        .qr-corner.bottom-left {
            bottom: calc(50% - 125px - 3px);
            left: calc(50% - 125px - 3px);
            border-right: none;
            border-top: none;
            border-bottom-left-radius: 8px;
        }
        .qr-corner.bottom-right {
            bottom: calc(50% - 125px - 3px);
            right: calc(50% - 125px - 3px);
            border-left: none;
            border-top: none;
            border-bottom-right-radius: 8px;
        }

        @media (max-width: 400px) {
            .qr-corner {
                width: 40px;
                height: 40px;
            }
            .qr-corner.top-left {
                top: calc(50% - 100px - 3px); /* Adjusted for 200px square */
                left: calc(50% - 100px - 3px);
            }
            .qr-corner.top-right {
                top: calc(50% - 100px - 3px);
                right: calc(50% - 100px - 3px);
            }
            .qr-corner.bottom-left {
                bottom: calc(50% - 100px - 3px);
                left: calc(50% - 100px - 3px);
            }
            .qr-corner.bottom-right {
                bottom: calc(50% - 100px - 3px);
                right: calc(50% - 100px - 3px);
            }
        }
    `;
    document.head.appendChild(style);

    let video = null;
    let canvas = null;
    let canvasContext = null;
    let stream = null; // To store the MediaStream object
    let animationFrameId = null; // To store the requestAnimationFrame ID
    let audioContext = null; // For playing beep sound

    // HTML content for the full-screen QR modal
    const qrModalHtmlContent = `
        <div class="qr-modal-overlay" id="qrFullScreenModal">
            <div class="qr-modal-content">
                <video id="qr-video" autoplay playsinline></video>
                <canvas id="qr-canvas"></canvas>
                <div class="qr-scan-success-feedback" id="qrScanSuccessFeedback">
                    <div class="qr-success-icon-container">
                        <i class="fas fa-check"></i>
                    </div>
                </div>
                <!-- Corner brackets for the scanning guide -->
                <div class="qr-corner top-left"></div>
                <div class="qr-corner top-right"></div>
                <div class="qr-corner bottom-left"></div>
                <div class="qr-corner bottom-right"></div>
            </div>
        </div>
    `;

    // Function to play a beep sound (higher pitch)
    function playBeep() {
        if (!audioContext) {
            audioContext = new (window.AudioContext || window.webkitAudioContext)();
        }
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.type = 'sine'; // Sine wave for a clean beep
        oscillator.frequency.value = 1000; // Higher frequency for a higher pitch (e.g., 1000 Hz)
        gainNode.gain.setValueAtTime(0, audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(1, audioContext.currentTime + 0.01);
        gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.15);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.15);
    }

    // Function to show the success feedback directly on the video and then close modal
    function showSuccessFeedbackAndCloseModal(redirectUrl) {
        const successFeedback = document.getElementById('qrScanSuccessFeedback');
        const qrModal = document.getElementById('qrFullScreenModal');

        if (successFeedback) {
            successFeedback.classList.add('show');
            // Stop scanner immediately so video freezes behind the feedback
            stopQrScanner();

            // After feedback animation, close the modal and redirect
            setTimeout(() => {
                successFeedback.classList.remove('show');
                if (qrModal) {
                    qrModal.classList.remove('show');
                    document.body.classList.remove('no-scroll'); // Re-enable body scrolling
                    // Revert theme color to original when modal closes
                    const themeColorMeta = document.querySelector('meta[name="theme-color"]');
                    if (themeColorMeta) {
                        themeColorMeta.setAttribute('content', '#FF6B35'); // Revert to original orange
                    }
                }
                if (redirectUrl) {
                    window.location.href = redirectUrl;
                }
            }, 1000); // Display feedback for 1 second before closing and redirecting
        } else {
            // Fallback to just closing and redirecting if feedback element isn't found
            if (qrModal) {
                qrModal.classList.remove('show');
                document.body.classList.remove('no-scroll');
                stopQrScanner();
                // Revert theme color to original when modal closes
                const themeColorMeta = document.querySelector('meta[name="theme-color"]');
                if (themeColorMeta) {
                    themeColorMeta.setAttribute('content', '#FF6B35'); // Revert to original orange
                }
                if (redirectUrl) {
                    window.location.href = redirectUrl;
                }
            }
        }
    }

    // Function to decode Base64 JSON (from qr.html)
    function decodeBase64Json(base64Str) {
        try {
            const decoded = atob(base64Str);
            return JSON.parse(decoded);
        } catch (e) {
            return { error: "Invalid or corrupt base64-encoded JSON." };
        }
    }

    // Function to handle successful QR scan (from qr.html)
    async function onScanSuccess(decodedText) {
        console.log(`QR Code scanned: ${decodedText}`);

        // Play a beep sound on successful scan
        playBeep();

        try {
            const parsed = JSON.parse(decodedText);
            console.log("Parsed QR Data:", parsed);

            // --- Send data to server ---
            const serverEndpoint = '/qr.php'; // Your server endpoint
            const dataToSendToServer = {
                public: parsed.public,
                hidden: parsed.hidden, // Send the original Base64 encoded hidden data
                originalQrContent: decodedText // Send the full original QR string
            };

            const response = await fetch(serverEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(dataToSendToServer),
            });

            if (response.ok) {
                const result = await response.json();
                console.log('QR data sent to server successfully!', result);
                if (result.redirectUrl) {
                    // Show success feedback and then redirect
                    showSuccessFeedbackAndCloseModal(result.redirectUrl);
                } else {
                    console.warn("Server did not provide a redirectUrl.");
                    showSuccessFeedbackAndCloseModal(null); // Close modal without redirect
                }
            } else {
                console.error('Failed to send QR data to server:', response.status, response.statusText);
                showSuccessFeedbackAndCloseModal(null); // Close modal on server error
            }
            // --- End send data to server ---

        } catch (e) {
            console.error("Failed to parse QR code content or send to server:", e);
            showSuccessFeedbackAndCloseModal(null); // Close modal on client-side error
        }
    }

    // Function to stop the QR scanner and release camera resources
    function stopQrScanner() {
        if (animationFrameId) {
            cancelAnimationFrame(animationFrameId);
            animationFrameId = null;
        }
        if (stream) {
            stream.getVideoTracks().forEach(track => {
                track.stop(); // Stop the video track to fully release camera
            });
            stream = null; // Set stream to null so a new one is requested next time
        }
        if (video) {
            video.pause(); // Pause the video element
            video.srcObject = null; // Clear the video source
        }
    }

    // Function to continuously scan for QR codes
    function tick() {
        if (video.readyState === video.HAVE_ENOUGH_DATA) {
            canvas.height = video.videoHeight;
            canvas.width = video.videoWidth;
            canvasContext.drawImage(video, 0, 0, canvas.width, canvas.height);
            const imageData = canvasContext.getImageData(0, 0, canvas.width, canvas.height);
            const code = jsQR(imageData.data, imageData.width, imageData.height, {
                inversionAttempts: "dontInvert",
            });

            if (code) {
                onScanSuccess(code.data);
                return; // Stop the loop if a QR code is found
            }
        }
        animationFrameId = requestAnimationFrame(tick);
    }

    // Function to show the full-screen QR modal
    function showQrFullScreenModal() {
        let qrModal = document.getElementById('qrFullScreenModal');
        if (!qrModal) {
            document.body.insertAdjacentHTML('beforeend', qrModalHtmlContent);
            qrModal = document.getElementById('qrFullScreenModal');
            video = document.getElementById('qr-video');
            canvas = document.getElementById('qr-canvas');
            canvasContext = canvas.getContext('2d');

            // Add a click listener to the overlay itself to close the modal
            qrModal.addEventListener('click', (event) => {
                // Only close if clicking directly on the overlay, not its children
                if (event.target === qrModal || event.target === video) { // Allow clicking video to close
                    qrModal.classList.remove('show');
                    document.body.classList.remove('no-scroll'); // Re-enable body scrolling
                    stopQrScanner(); // Stop scanner when modal is closed
                    // Revert theme color to original when modal closes
                    const themeColorMeta = document.querySelector('meta[name="theme-color"]');
                    if (themeColorMeta) {
                        themeColorMeta.setAttribute('content', '#FF6B35'); // Revert to original orange
                    }
                }
            });
        }
        
        // The theme color will remain the default from home.html, no change here.
        // const themeColorMeta = document.querySelector('meta[name="theme-color"]');
        // if (themeColorMeta) {
        //     themeColorMeta.setAttribute('content', 'transparent'); // This line is now commented out
        // }

        // Load jsQR library if not already loaded
        if (typeof jsQR === 'undefined') {
            const script = document.createElement('script');
            script.src = "https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.min.js";
            script.type = "text/javascript";
            script.onload = () => {
                startCameraAndScanner();
            };
            document.head.appendChild(script);
        } else {
            startCameraAndScanner();
        }

        qrModal.classList.add('show'); // Show the modal
        document.body.classList.add('no-scroll'); // Prevent body scrolling
    }

    // Function to start the camera and begin scanning
    async function startCameraAndScanner() {
        const qrModalContent = document.querySelector('.qr-modal-content');

        try {
            // Always request a new stream if 'stream' is null or its tracks are stopped
            if (!stream || stream.getVideoTracks().length === 0 || stream.getVideoTracks()[0].readyState === 'ended') {
                stream = await navigator.mediaDevices.getUserMedia({ video: { facingMode: "environment" } });
                video.srcObject = stream;
                video.setAttribute("playsinline", true);
                await video.play(); // Ensure video starts playing before tick
                console.log("Started new camera stream.");
            } else {
                // If stream exists and is active, just ensure video is playing
                video.srcObject = stream; // Re-assign srcObject if it was cleared
                await video.play();
                console.log("Re-enabled existing camera stream.");
            }

            animationFrameId = requestAnimationFrame(tick); // Start scanning loop
            // Remove any existing error message if camera starts successfully
            const existingErrorMessage = qrModalContent.querySelector('.qr-error-message');
            if (existingErrorMessage) {
                existingErrorMessage.remove();
            }
        } catch (err) {
            console.error("Error accessing camera:", err);
            stopQrScanner(); // Ensure resources are released on error
            let errorMessageDiv = qrModalContent.querySelector('.qr-error-message');
            if (!errorMessageDiv) {
                errorMessageDiv = document.createElement('div');
                errorMessageDiv.classList.add('qr-error-message');
                qrModalContent.appendChild(errorMessageDiv);
            }
            errorMessageDiv.textContent = "Camera access denied or not available. Please ensure camera permissions are granted.";
        }
    }

    // Get the QR button from the navigation bar (from home.html)
    const qrButtonNavItem = document.getElementById('qrButtonNavItem');

    if (qrButtonNavItem) {
        qrButtonNavItem.addEventListener('click', (event) => {
            event.preventDefault(); // Prevent default link behavior
            showQrFullScreenModal(); // Call the function to show the full-screen modal
        });
    } else {
        console.error('Could not find #qrButtonNavItem to attach QR modal functionality.');
    }
});
