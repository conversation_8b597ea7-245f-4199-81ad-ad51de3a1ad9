<?php
// Set headers for CORS. Adjust 'Access-Control-Allow-Origin' to your Canvas URL
// if your Canvas is hosted on a different domain/port.
// For development, '*' can be used, but specify exact origins in production.
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header("Content-Type: application/json");

// Handle preflight OPTIONS request for CORS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Define the directory where you want to save the data
// Ensure this directory exists and is writable by the web server.
$saveDirectory = 'scanned_data';
if (!is_dir($saveDirectory)) {
    mkdir($saveDirectory, 0777, true); // Create directory if it doesn't exist, with write permissions
}

// Get the raw POST data
$jsonData = file_get_contents('php://input');

// Decode the JSON data
$data = json_decode($jsonData, true); // true to get an associative array

// Check if JSON decoding was successful and data is not empty
if (json_last_error() !== JSON_ERROR_NONE || empty($data)) {
    echo json_encode(["error" => "Invalid JSON data provided."]);
    http_response_code(400); // Bad Request
    exit();
}

// Extract public, hidden (base64 encoded), and original QR content data
$publicData = isset($data['public']) ? $data['public'] : 'N/A';
$hiddenDataRaw = isset($data['hidden']) ? $data['hidden'] : null; // This is the Base64 string from client
$originalQrContent = isset($data['originalQrContent']) ? $data['originalQrContent'] : '';

$hiddenJsonStringForFile = 'No hidden data or invalid format.';
$parsedHiddenData = null; // To store the decoded and parsed hidden JSON

if ($hiddenDataRaw !== null) {
    // Attempt to decode Base64 and then parse JSON
    if (is_string($hiddenDataRaw)) {
        $decodedBase64 = base64_decode($hiddenDataRaw, true); // true for strict decoding
        if ($decodedBase64 !== false) {
            $parsedHidden = json_decode($decodedBase64, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                // Successfully decoded Base64 and parsed JSON
                $hiddenJsonStringForFile = json_encode($parsedHidden, JSON_PRETTY_PRINT);
                $parsedHiddenData = $parsedHidden; // Store for redirection logic
            } else {
                // Base64 decoded, but not valid JSON. Save as plain text.
                $hiddenJsonStringForFile = "Base64 decoded, but not valid JSON:\n" . $decodedBase64;
            }
        } else {
            // Not a valid Base64 string. Treat as plain string.
            $hiddenJsonStringForFile = "Not Base64 encoded, treated as plain string:\n" . $hiddenDataRaw;
        }
    } else {
        // Fallback for other types (shouldn't happen if client sends base64 string)
        $hiddenJsonStringForFile = "Unexpected hidden data type:\n" . print_r($hiddenDataRaw, true);
    }
}

try {
    $timestamp = date("Ymd_His_u"); // Unique timestamp with microseconds
    $filename = "qr_hidden_data_{$timestamp}.txt";
    $filepath = $saveDirectory . DIRECTORY_SEPARATOR . $filename;

    $fileContent = "Public Data: " . $publicData . "\n";
    $fileContent .= "Hidden Data (Decoded & Pretty Printed):\n" . $hiddenJsonStringForFile . "\n";
    $fileContent .= "Original Full QR Content:\n" . $originalQrContent . "\n"; // Save original QR content too

    if (file_put_contents($filepath, $fileContent) === false) {
        error_log("Failed to write to file: " . $filepath);
        // Do not exit, continue to send response to client even if file save fails
    } else {
        error_log("Saved QR data to " . $filepath);
    }

} catch (Exception $e) {
    error_log("Error processing data for file save: " . $e->getMessage());
    // Do not exit, continue to send response to client even if file save fails
}

// --- Construct Redirection URL ---
$restaurantName = 'UnknownRestaurant';
$tableNumber = 'UnknownTable';
$secretCode = 'UnknownSecret';

if ($parsedHiddenData !== null) {
    $restaurantName = isset($parsedHiddenData['Restaurant Name']) ? $parsedHiddenData['Restaurant Name'] : $restaurantName;
    $tableNumber = isset($parsedHiddenData['Table Number']) ? $parsedHiddenData['Table Number'] : $tableNumber;
    $secretCode = isset($parsedHiddenData['Secret Code']) ? $parsedHiddenData['Secret Code'] : $secretCode;
}

// Base64 URL-safe encode the original QR content
// PHP's base64_encode is standard, then replace characters for URL-safety
$encodedFullQrDataUrlSafe = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($originalQrContent));

// Your domain
$domain = 'https://6409815b48f2.ngrok-free.app'; 
$redirectUrl = "https://{$domain}/dine-in.html#" . 
               urlencode($restaurantName) . 
               "#tablenumber:" . urlencode($tableNumber) . 
               "#" . urlencode($secretCode) . 
               "#" . $encodedFullQrDataUrlSafe;

// Send the redirect URL back to the client
echo json_encode(["message" => "QR data received and processed by server.", "redirectUrl" => $redirectUrl]);
http_response_code(200); // OK

?>
