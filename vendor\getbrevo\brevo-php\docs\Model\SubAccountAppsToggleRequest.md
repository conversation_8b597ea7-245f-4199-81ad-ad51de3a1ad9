# SubAccountAppsToggleRequest

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**inbox** | **bool** | Set this field to enable or disable Inbox on the sub-account / Not applicable on ENTv2 | [optional] 
**whatsapp** | **bool** | Set this field to enable or disable Whatsapp campaigns on the sub-account | [optional] 
**automation** | **bool** | Set this field to enable or disable Automation on the sub-account | [optional] 
**emailCampaigns** | **bool** | Set this field to enable or disable Email Campaigns on the sub-account | [optional] 
**smsCampaigns** | **bool** | Set this field to enable or disable SMS Marketing on the sub-account | [optional] 
**landingPages** | **bool** | Set this field to enable or disable Landing pages on the sub-account | [optional] 
**transactionalEmails** | **bool** | Set this field to enable or disable Transactional Email on the sub-account | [optional] 
**transactionalSms** | **bool** | Set this field to enable or disable Transactional SMS on the sub-account | [optional] 
**facebookAds** | **bool** | Set this field to enable or disable Facebook ads on the sub-account | [optional] 
**webPush** | **bool** | Set this field to enable or disable Web Push on the sub-account | [optional] 
**meetings** | **bool** | Set this field to enable or disable Meetings on the sub-account | [optional] 
**conversations** | **bool** | Set this field to enable or disable Conversations on the sub-account | [optional] 
**crm** | **bool** | Set this field to enable or disable Sales CRM on the sub-account | [optional] 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


