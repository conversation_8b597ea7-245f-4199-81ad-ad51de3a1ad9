<?php
/**
 * GetStatsByDevice
 *
 * PHP version 5
 *
 * @category Class
 * @package  Brevo\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * Brevo API
 *
 * Brevo provide a RESTFul API that can be used with any languages. With this API, you will be able to :   - Manage your campaigns and get the statistics   - Manage your contacts   - Send transactional Emails and SMS   - and much more...  You can download our wrappers at https://github.com/orgs/brevo  **Possible responses**   | Code | Message |   | :-------------: | ------------- |   | 200  | OK. Successful Request  |   | 201  | OK. Successful Creation |   | 202  | OK. Request accepted |   | 204  | OK. Successful Update/Deletion  |   | 400  | Error. Bad Request  |   | 401  | Error. Authentication Needed  |   | 402  | Error. Not enough credit, plan upgrade needed  |   | 403  | Error. Permission denied  |   | 404  | Error. Object does not exist |   | 405  | Error. Method not allowed  |   | 406  | Error. Not Acceptable  |
 *
 * OpenAPI spec version: 3.0.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.29
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Brevo\Client\Model;

use \ArrayAccess;
use \Brevo\Client\ObjectSerializer;

/**
 * GetStatsByDevice Class Doc Comment
 *
 * @category Class
 * @package  Brevo\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class GetStatsByDevice implements ModelInterface, ArrayAccess
{
    const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $swaggerModelName = 'getStatsByDevice';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerTypes = [
        'desktop' => 'map[string,\Brevo\Client\Model\GetDeviceBrowserStats]',
        'mobile' => 'map[string,\Brevo\Client\Model\GetDeviceBrowserStats]',
        'tablet' => 'map[string,\Brevo\Client\Model\GetDeviceBrowserStats]',
        'unknown' => 'map[string,\Brevo\Client\Model\GetDeviceBrowserStats]'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerFormats = [
        'desktop' => null,
        'mobile' => null,
        'tablet' => null,
        'unknown' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerTypes()
    {
        return self::$swaggerTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerFormats()
    {
        return self::$swaggerFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'desktop' => 'desktop',
        'mobile' => 'mobile',
        'tablet' => 'tablet',
        'unknown' => 'unknown'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'desktop' => 'setDesktop',
        'mobile' => 'setMobile',
        'tablet' => 'setTablet',
        'unknown' => 'setUnknown'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'desktop' => 'getDesktop',
        'mobile' => 'getMobile',
        'tablet' => 'getTablet',
        'unknown' => 'getUnknown'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$swaggerModelName;
    }

    

    

    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['desktop'] = isset($data['desktop']) ? $data['desktop'] : null;
        $this->container['mobile'] = isset($data['mobile']) ? $data['mobile'] : null;
        $this->container['tablet'] = isset($data['tablet']) ? $data['tablet'] : null;
        $this->container['unknown'] = isset($data['unknown']) ? $data['unknown'] : null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets desktop
     *
     * @return map[string,\Brevo\Client\Model\GetDeviceBrowserStats]
     */
    public function getDesktop()
    {
        return $this->container['desktop'];
    }

    /**
     * Sets desktop
     *
     * @param map[string,\Brevo\Client\Model\GetDeviceBrowserStats] $desktop Statistics of the campaign on the basis of desktop devices
     *
     * @return $this
     */
    public function setDesktop($desktop)
    {
        $this->container['desktop'] = $desktop;

        return $this;
    }

    /**
     * Gets mobile
     *
     * @return map[string,\Brevo\Client\Model\GetDeviceBrowserStats]
     */
    public function getMobile()
    {
        return $this->container['mobile'];
    }

    /**
     * Sets mobile
     *
     * @param map[string,\Brevo\Client\Model\GetDeviceBrowserStats] $mobile Statistics of the campaign on the basis of mobile devices
     *
     * @return $this
     */
    public function setMobile($mobile)
    {
        $this->container['mobile'] = $mobile;

        return $this;
    }

    /**
     * Gets tablet
     *
     * @return map[string,\Brevo\Client\Model\GetDeviceBrowserStats]
     */
    public function getTablet()
    {
        return $this->container['tablet'];
    }

    /**
     * Sets tablet
     *
     * @param map[string,\Brevo\Client\Model\GetDeviceBrowserStats] $tablet Statistics of the campaign on the basis of tablet devices
     *
     * @return $this
     */
    public function setTablet($tablet)
    {
        $this->container['tablet'] = $tablet;

        return $this;
    }

    /**
     * Gets unknown
     *
     * @return map[string,\Brevo\Client\Model\GetDeviceBrowserStats]
     */
    public function getUnknown()
    {
        return $this->container['unknown'];
    }

    /**
     * Sets unknown
     *
     * @param map[string,\Brevo\Client\Model\GetDeviceBrowserStats] $unknown Statistics of the campaign on the basis of unknown devices
     *
     * @return $this
     */
    public function setUnknown($unknown)
    {
        $this->container['unknown'] = $unknown;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    #[\ReturnTypeWillChange]
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }

    /**
     * Sets value based on offset.
     *
     * @param integer $offset Offset
     * @param mixed   $value  Value to be set
     *
     * @return void
     */
    #[\ReturnTypeWillChange]
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    #[\ReturnTypeWillChange]
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) { // use JSON pretty print
            return json_encode(
                ObjectSerializer::sanitizeForSerialization($this),
                JSON_PRETTY_PRINT
            );
        }

        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


