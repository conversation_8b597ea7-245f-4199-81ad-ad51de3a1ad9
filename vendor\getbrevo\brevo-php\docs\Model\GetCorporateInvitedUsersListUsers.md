# GetCorporateInvitedUsersListUsers

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**groups** | [**\Brevo\Client\Model\GetCorporateInvitedUsersListGroups**](GetCorporateInvitedUsersListGroups.md) |  | 
**email** | **string** | Email address of the user. | 
**isOwner** | **string** | Flag for indicating is user owner of the organization. | 
**status** | **string** | Status of the invited user. | 
**featureAccess** | [**\Brevo\Client\Model\GetCorporateInvitedUsersListFeatureAccess**](GetCorporateInvitedUsersListFeatureAccess.md) |  | 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


