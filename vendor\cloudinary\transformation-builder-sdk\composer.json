{"name": "cloudinary/transformation-builder-sdk", "version": "2.1.2", "description": "Cloudinary PHP Transformation Builder SDK", "keywords": ["cloudinary", "sdk", "cloud", "image management", "cdn"], "type": "library", "homepage": "https://github.com/cloudinary/php-transformation-builder-sdk", "minimum-stability": "stable", "license": "MIT", "authors": [{"name": "Cloudinary", "homepage": "https://github.com/cloudinary/php-transformation-builder-sdk/graphs/contributors"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/cloudinary/php-transformation-builder-sdk/issues"}, "require": {"php": ">=8.0.0", "ext-json": "*"}, "require-dev": {"symfony/phpunit-bridge": "^7.2", "phpmd/phpmd": "*", "squizlabs/php_codesniffer": "*", "friendsofphp/php-cs-fixer": "*", "ext-dom": "*", "ext-libxml": "*", "ext-zip": "*"}, "autoload": {"classmap": ["src"]}, "autoload-dev": {"classmap": ["tests"], "psr-4": {"Cloudinary\\Test\\": "tests/"}}}