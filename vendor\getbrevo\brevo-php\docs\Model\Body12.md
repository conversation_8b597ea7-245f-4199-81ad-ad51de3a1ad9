# Body12

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**visitorId** | **string** | visitor’s ID received &lt;a href&#x3D;\"https://developers.brevo.com/docs/conversations-webhooks\"&gt;from a webhook&lt;/a&gt; or generated by you to &lt;a href&#x3D;\"https://developers.brevo.com/docs/customize-the-widget#identifying-existing-users\"&gt;bind existing user account to Conversations&lt;/a&gt; | 
**text** | **string** | message text | 
**agentId** | **string** | agent ID. It can be found on agent’s page or received &lt;a href&#x3D;\"https://developers.brevo.com/docs/conversations-webhooks\"&gt;from a webhook&lt;/a&gt;. Optional if &#x60;groupId&#x60; is set. | [optional] 
**groupId** | **string** | group ID. It can be found on group’s page. Optional if &#x60;agentId&#x60; is set. | [optional] 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


