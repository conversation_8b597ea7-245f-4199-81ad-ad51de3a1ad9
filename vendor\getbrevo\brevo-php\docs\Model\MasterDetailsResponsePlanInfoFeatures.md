# MasterDetailsResponsePlanInfoFeatures

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **string** | Name of the feature | [optional] 
**unitValue** | **string** | Unit value of the feature | [optional] 
**quantity** | **int** | Quantity provided in the plan | [optional] 
**quantityWithOverages** | **int** | Quantity with overages provided in the plan (only applicable on ENTv2) | [optional] 
**used** | **int** | Quantity consumed by master | [optional] 
**usedOverages** | **int** | Quantity consumed by sub-organizations over the admin plan limit (only applicable on ENTv2) | [optional] 
**remaining** | **int** | Quantity remaining in the plan | [optional] 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


