<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0, viewport-fit=cover">
    <meta name="theme-color" content="#ff5018">

    <title>MIKO APP - INSTALL</title>
    <link rel="manifest" crossorigin="use-credentials" href="/manifest.json">

    <!-- Google Fonts - Poppins and DM Sans -->
    <link href="https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Apple-specific PWA meta tags for iOS icons and full-screen mode -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="MIKO">
    <!-- Apple Touch Icon (recommended size 180x180 for modern iOS devices) -->
    <link rel="apple-touch-icon" href="/icons/icon-180x180.webp">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            transition: background-color 0.3s, color 0.3s;
        }
        /* Light Mode */
        body.light-mode {
            background-color: var(--neutral-base); /* Neutral base for light background */
            color: var(--text-dark-elements); /* Dark text for light mode */
        }
        .light-mode .bg-primary {
            background-color: var(--pure-white); /* Pure white for primary elements in light mode */
        }
        .light-mode .text-secondary {
            color: var(--subtle-detail); /* Subtle detail for secondary text in light mode */
        }
        .light-mode .border-separator {
            border-color: #D1D1D1; /* Slightly darker subtle detail for separators */
        }
        .light-mode .shadow-card {
            box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1);
        }
        .light-mode .tab-bar {
            background-color: var(--pure-white);
            border-top: 1px solid #D1D1D1;
        }

        /* Dark Mode */
        body.dark-mode {
            background-color: #121212; /* A dark grey for dark mode background */
            color: var(--pure-white); /* White text for dark mode */
        }
        .dark-mode .bg-primary {
            background-color: #1E1E1E; /* Slightly lighter dark grey for primary elements in dark mode */
        }
        .dark-mode .text-secondary {
            color: #B0B0B0; /* Lighter grey for secondary text in dark mode */
        }
        .dark-mode .border-separator {
            border-color: #3A3A3C; /* Darker grey for separators in dark mode */
        }
        .dark-mode .shadow-card {
            box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.2);
        }
         .dark-mode .tab-bar {
            background-color: #1E1E1E;
            border-top: 1px solid #3A3A3C;
        }

        /* Common Styles - using CSS variables for the new palette */
        :root {
            --primary-accent: #ff5018;
            --secondary-accent: #FF7F50;
            --neutral-base: #F7F7F7;
            --text-dark-elements: #333333;
            --subtle-detail: #A7A7A7;
            --pure-white: #FFFFFF;
        }

        .primary-accent-bg {
            background-color: var(--primary-accent); /* Primary accent for backgrounds */
        }
        .text-primary-accent {
            color: var(--primary-accent); /* Primary accent for text */
        }
        .star-filled {
            color: #FFD700; /* Gold for stars - kept as is for visibility */
        }
        .tab-icon {
            font-size: 24px;
        }
        .tab-label {
            font-size: 10px;
        }
        .scroll-snap-x {
            scroll-snap-type: x mandatory;
            -webkit-overflow-scrolling: touch; /* For smoother scrolling on iOS */
        }
        .scroll-snap-start {
            scroll-snap-align: start;
        }

        /* Hide scrollbar for webkit browsers (Chrome, Safari) */
        .hide-scrollbar::-webkit-scrollbar {
            display: none;
        }
        /* Hide scrollbar for IE, Edge and Firefox */
        .hide-scrollbar {
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }
    </style>
</head>
<body class="light-mode">
    <script>
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.register('/sw.js').then((reg) => {
      console.log('SW registered:', reg);
    });
  }
</script>

    <div class="min-h-screen flex flex-col">
        <!-- Top Navigation Bar -->
        <header class="flex items-center justify-end p-4 bg-primary border-b border-separator">
            <!-- Removed back button -->
            <div class="flex flex-col items-center flex-grow">
                <!-- Updated logo placeholder to reflect new primary accent color -->
                <img src="icons/icon.webp" alt="MIKO App Logo" class="rounded-lg mb-1 w-8 h-8">
                <span class="font-semibold text-lg">MIKO - BY PAKISTAN</span>
            </div>
            <button class="p-2 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                </svg>
            </button>
        </header>

        <!-- Main Content Area -->
        <main class="flex-grow p-4 overflow-y-auto">
            <!-- App Details Section -->
            <section class="flex items-start mb-6">
                <div class="mr-4">
                    <!-- Updated app icon placeholder to reflect new primary accent color -->
                    <img src="icons/icon.webp" alt="MIKO App Icon" class="rounded-2xl w-20 h-20">
                </div>
                <div class="flex-1">
                    <h1 class="text-xl font-bold mb-1">MIKO - BY PAKISTAN</h1>
                    <p class="text-secondary text-sm mb-3">easy food and grocery delivery</p>
                    <!-- Updated button class for new accent color and text -->
                    <button id="installButton" class="primary-accent-bg text-white px-6 py-2 rounded-full font-semibold text-base mb-4">Install</button>

                    <div class="grid grid-cols-4 gap-4 text-center text-sm">
                        <div>
                            <p class="font-semibold">366K</p>
                            <p class="text-secondary">RATINGS</p>
                            <div class="flex justify-center mt-1">
                                <span class="star-filled">★</span>
                                <span class="star-filled">★</span>
                                <span class="star-filled">★</span>
                                <span class="star-filled">★</span>
                                <span class="star-filled">☆</span>
                            </div>
                            <p class="font-semibold">4.5</p>
                        </div>
                        <div>
                            <p class="font-semibold">AGES</p>
                            <p class="text-secondary">4+</p>
                            <p class="text-secondary mt-1">Years</p>
                        </div>
                        <div>
                            <p class="font-semibold">CHART</p>
                            <p class="text-secondary">No.1</p>
                            <p class="text-secondary mt-1">Food & Drink</p>
                        </div>
                        <div>
                            <p class="font-semibold">DEVELOPER</p>
                            <p class="text-secondary">MIKO PAKISTAN</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- What's New Section -->
            <section class="mb-6 bg-primary p-4 rounded-xl shadow-card">
                <div class="flex justify-between items-center mb-2">
                    <h2 class="text-lg font-semibold">What's New</h2>
                    <!-- Updated text color for new accent -->
                    <button class="text-primary-accent flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>
                <p class="text-sm text-secondary mb-2">Version 25.29.1 <span class="ml-2">2d ago</span></p>
                <p class="text-sm mb-2">
                    We're always working hard to optimize our app with the latest technologies and best new features. This version includes a number of UI/UX improvements as well as stability enhancements.
                    <!-- Updated link color for new accent -->
                    <a href="#" class="text-primary-accent">more</a>
                </p>
            </section>

            <!-- Preview Section -->
            <section class="mb-6">
                <h2 class="text-lg font-semibold mb-3">Preview</h2>
                <div class="flex overflow-x-auto space-x-4 pb-2 scroll-snap-x hide-scrollbar">
                    <div class="flex-shrink-0 w-64 h-auto rounded-xl overflow-hidden shadow-card scroll-snap-start">
                        <img src="screenshot/1.webp" alt="App Screenshot 1" class="w-full h-full object-cover">
                    </div>
                    <div class="flex-shrink-0 w-64 h-auto rounded-xl overflow-hidden shadow-card scroll-snap-start">
                        <img src="screenshot/2.webp" alt="App Screenshot 2" class="w-full h-full object-cover">
                    </div>
                    <div class="flex-shrink-0 w-64 h-auto rounded-xl overflow-hidden shadow-card scroll-snap-start">
                        <img src="screenshot/3.webp" alt="App Screenshot 3" class="w-full h-full object-cover">
                    </div>
                    <div class="flex-shrink-0 w-64 h-auto rounded-xl overflow-hidden shadow-card scroll-snap-start">
                        <img src="screenshot/4.webp" alt="App Screenshot 4" class="w-full h-full object-cover">
                    </div>
                    <div class="flex-shrink-0 w-64 h-auto rounded-xl overflow-hidden shadow-card scroll-snap-start">
                        <img src="screenshot/5.webp" alt="App Screenshot 5" class="w-full h-full object-cover">
                    </div>
                    <div class="flex-shrink-0 w-64 h-auto rounded-xl overflow-hidden shadow-card scroll-snap-start">
                        <img src="screenshot/6.webp" alt="App Screenshot 6" class="w-full h-full object-cover">
                    </div>
                </div>
            </section>

            <!-- Ratings & Reviews Section -->
            <section class="mb-6 bg-primary p-4 rounded-xl shadow-card">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold">Ratings & Reviews</h2>
                    <button class="text-primary-accent flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>
                <div class="flex items-center mb-4">
                    <p class="text-5xl font-bold mr-4">4.5</p>
                    <div class="flex flex-col items-center">
                        <div class="flex text-xl star-filled">
                            <span>★</span><span>★</span><span>★</span><span>★</span><span>☆</span>
                        </div>
                        <p class="text-secondary text-sm">366K Ratings</p>
                    </div>
                </div>
                <p class="font-semibold mb-4">Most Helpful Reviews</p>

                <!-- Review Item -->
                <div class="mb-4 pb-4 border-b border-separator last:border-b-0">
                    <div class="flex items-center mb-1">
                        <div class="flex text-sm star-filled mr-2">
                            <span>★</span><span>★</span><span>★</span><span>★</span><span>★</span>
                        </div>
                        <p class="text-sm text-secondary">3y ago · MonaRaja</p>
                    </div>
                    <p class="font-semibold mb-1">Worst experience</p>
                    <p class="text-sm mb-2">Extremely bad services of Foodpanda: Pandamart Pakistan Received two faulty items in one order Products Review: Eg...</p>
                    <p class="font-semibold text-sm">Developer Response <span class="text-secondary font-normal">1y ago</span></p>
                    <p class="text-sm text-secondary">Hi MonaRaja, we're sorry to hear about your experience with our service. It's not the experience we want for our custome...</p>
                </div>

                <p class="text-center text-secondary text-sm mt-4 mb-4">Tap to Rate</p>
                <div class="flex justify-center mb-6">
                    <span class="text-4xl text-secondary mx-2">☆</span>
                    <span class="text-4xl text-secondary mx-2">☆</span>
                    <span class="text-4xl text-secondary mx-2">☆</span>
                    <span class="text-4xl text-secondary mx-2">☆</span>
                    <span class="text-4xl text-secondary mx-2">☆</span>
                </div>

                <div class="flex justify-around space-x-2">
                    <button class="flex-1 flex items-center justify-center border border-subtle-detail rounded-full py-2 text-sm font-semibold">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        Write a Review
                    </button>
                    <button class="flex-1 flex items-center justify-center border border-subtle-detail rounded-full py-2 text-sm font-semibold">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M8.228 9.247a4.998 4.998 0 00-.577 1.042A7.999 7.999 0 0112 21c4.2 0 7.841-2.405 9.373-5.748.001-.003.001-.006.002-.009a1 1 0 00-1.002-1.002c-.003.001-.006.001-.009.002A5.998 5.998 0 0012 15c-1.657 0-3.15-.67-4.228-1.753zM12 9a3 3 0 100-6 3 3 0 000 6z" />
                        </svg>
                        App Support
                    </button>
                </div>
            </section>

            <!-- App Privacy Section -->
            <section class="mb-6 bg-primary p-4 rounded-xl shadow-card">
                <div class="flex justify-between items-center mb-2">
                    <h2 class="text-lg font-semibold">App Privacy</h2>
                    <button class="text-primary-accent flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>
                <p class="text-sm text-secondary mb-2">See Details</p>
            </section>

            <!-- Information Section -->
            <section class="mb-6 bg-primary p-4 rounded-xl shadow-card">
                <h2 class="text-lg font-semibold mb-3">Information</h2>
                <div class="space-y-3">
                    <div class="flex justify-between items-center border-b border-separator pb-2">
                        <span class="text-sm">Provider</span>
                        <span class="text-sm text-secondary">Internet Services Polen Sp. z o.o.</span>
                    </div>
                    <div class="flex justify-between items-center border-b border-separator pb-2">
                        <span class="text-sm">Size</span>
                        <span class="text-sm text-secondary">247.5 MB</span>
                    </div>
                    <div class="flex justify-between items-center border-b border-separator pb-2">
                        <span class="text-sm">Category</span>
                        <span class="text-sm text-secondary">Food & Drink</span>
                    </div>
                    <div class="flex justify-between items-center border-b border-separator pb-2">
                        <span class="text-sm">Compatibility</span>
                        <span class="text-sm text-secondary">Works on this iPhone <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" /></svg></span>
                    </div>
                    <div class="flex justify-between items-center border-b border-separator pb-2">
                        <span class="text-sm">Location</span>
                        <span class="text-sm text-secondary">This app may use your location even when it is not... <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" /></svg></span>
                    </div>
                    <div class="flex justify-between items-center border-b border-separator pb-2">
                        <span class="text-sm">Languages</span>
                        <span class="text-sm text-secondary">English and 16 more <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" /></svg></span>
                    </div>
                    <div class="flex justify-between items-center pb-2">
                        <span class="text-sm">Age Rating</span>
                        <span class="text-sm text-secondary">4+</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm">Copyright</span>
                        <span class="text-sm text-secondary">© MIKO - BY PAKISTAN 2025</span>
                    </div>
                </div>
                <a href="#" class="text-primary-accent text-sm mt-4 block">Privacy Policy</a>
                <a href="#" class="text-primary-accent text-sm block">Report a Problem</a>
            </section>

            <!-- More by MIKO - BY PAKISTAN Section -->
            <section class="mb-20 bg-primary p-4 rounded-xl shadow-card">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold">More by MIKO - BY PAKISTAN</h2>
                    <button class="text-primary-accent flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>

                <div class="flex items-center mb-4">
                    <img src="icons/icon.webp" alt="MIKO Rider App Icon" class="rounded-lg w-12 h-12 mr-3">
                    <div class="flex-1">
                        <p class="font-semibold">MIKO Rider</p>
                        <p class="text-secondary text-sm">Utilities</p>
                    </div>
                    <button class="bg-gray-200 dark:bg-gray-700 text-text-dark-elements dark:text-pure-white px-4 py-1 rounded-full font-semibold text-sm">Get</button>
                </div>

                <div class="flex items-center">
                    <img src="icons/icon.webp" alt="MIKO Partner App Icon" class="rounded-lg w-12 h-12 mr-3">
                    <div class="flex-1">
                        <p class="font-semibold">MIKO Partner</p>
                        <p class="text-secondary text-sm">Manage your business on the go</p>
                    </div>
                    <button class="bg-gray-200 dark:bg-gray-700 text-text-dark-elements dark:text-pure-white px-4 py-1 rounded-full font-semibold text-sm">Get</button>
                </div>
            </section>
        </main>

        <!-- Bottom Tab Bar -->
        <nav class="tab-bar fixed bottom-0 left-0 right-0 py-2 border-t border-separator">
            <ul class="flex justify-around items-center h-full">
                <li class="flex flex-col items-center flex-1">
                    <button class="flex flex-col items-center p-2 rounded-lg">
                        <!-- Updated icon color for new accent -->
                        <svg xmlns="http://www.w3.org/2000/svg" class="tab-icon h-6 w-6 text-primary-accent" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 15H9v-6h2v6zm4 0h-2V7h2v10z"/>
                        </svg>
                        <!-- Updated label color for new accent -->
                        <span class="tab-label text-primary-accent">Today</span>
                    </button>
                </li>
                <li class="flex flex-col items-center flex-1">
                    <button class="flex flex-col items-center p-2 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="tab-icon h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M11.049 2.152A2.999 2.999 0 0113.999 2h.001c.642 0 1.258.24 1.72.67l2.88 2.88c.43.462.67 1.078.67 1.72v.001c0 2.95-.99 5.64-2.618 7.742a13.999 13.999 0 01-7.742 2.618H9c-2.95 0-5.64-.99-7.742-2.618A13.999 13.999 0 012 9c0-2.95.99-5.64 2.618-7.742L7.293 2.293a1 1 0 011.414 1.414l-2.618 2.618A11.999 11.999 0 005 9c0 2.492.83 4.764 2.21 6.643a11.999 11.999 0 006.643 2.21c2.492 0 4.764-.83 6.643-2.21a11.999 11.999 0 002.21-6.643c0-2.492-.83-4.764-2.21-6.643L14.707 2.293a1 1 0 01-1.414-1.414L11.049 2.152z" />
                        </svg>
                        <span class="tab-label">Games</span>
                    </button>
                </li>
                <li class="flex flex-col items-center flex-1">
                    <button class="flex flex-col items-center p-2 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="tab-icon h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                        </svg>
                        <span class="tab-label">Apps</span>
                    </button>
                </li>
                <li class="flex flex-col items-center flex-1">
                    <button class="flex flex-col items-center p-2 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="tab-icon h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.75 17L9 20l-1 1h8l-1-1-1.25-3M3 5h18M5 9h14L12 22 5 9z" />
                        </svg>
                        <span class="tab-label">Arcade</span>
                    </button>
                </li>
                <li class="flex flex-col items-center flex-1">
                    <button class="flex flex-col items-center p-2 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="tab-icon h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        <span class="tab-label">Search</span>
                    </button>
                </li>
            </ul>
        </nav>
    </div>

    <script>
        let deferredPrompt; // Variable to store the event
        let installButton; // Declare globally

        // Function to set the theme based on user's preference
        function setTheme() {
            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                document.body.classList.remove('light-mode');
                document.body.classList.add('dark-mode');
            } else {
                document.body.classList.remove('dark-mode');
                document.body.classList.add('light-mode');
            }
        }

        // Handle install button click
        async function handleInstallClick() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                console.log(`User response to the install prompt: ${outcome}`);
                deferredPrompt = null; // Prompt consumed
                // The button will remain visible as per user's request
            }
        }

        // Update button text and visibility based on installability
        function updateInstallButtonState() {
            if (!installButton) return; // Ensure button exists

            installButton.textContent = 'Install'; // Always show "Install"
            installButton.style.display = 'block'; // Always visible

            // Only make it clickable if a prompt is available
            if (deferredPrompt) {
                installButton.removeEventListener('click', handleInstallClick); // Remove to prevent duplicates
                installButton.addEventListener('click', handleInstallClick);
                installButton.disabled = false; // Enable button
                console.log('Button set to Install (clickable).');
            } else {
                // If no prompt, it's either already installed or not installable
                // We keep it visible but disabled or non-functional for installation
                installButton.removeEventListener('click', handleInstallClick); // Remove any install listener
                installButton.disabled = true; // Disable button if not installable
                console.log('Button set to Install (not clickable - no prompt available).');
            }
        }

        // DOMContentLoaded ensures the button element is available and service worker registers fast
        document.addEventListener('DOMContentLoaded', () => {
            installButton = document.getElementById('installButton'); // Assign the button element

            // Initial setup for theme
            setTheme();

            // Register the service worker as early as possible after DOM is loaded
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/service-worker.js')
                    .then(registration => {
                        console.log('Service Worker registered with scope:', registration.scope);
                    })
                    .catch(error => {
                        console.error('Service Worker registration failed:', error);
                    });
            }

            // Listen for changes in the color scheme preference
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
                setTheme();
            });

            // Listen for the beforeinstallprompt event
            window.addEventListener('beforeinstallprompt', (e) => {
                e.preventDefault();
                deferredPrompt = e;
                console.log('beforeinstallprompt fired, app is installable.');
                updateInstallButtonState(); // Update button when installability changes
            });

            // Listen for appinstalled event (PWA installed)
            window.addEventListener('appinstalled', () => {
                console.log('PWA was installed');
                deferredPrompt = null; // Clear deferredPrompt after installation
                updateInstallButtonState(); // Update button state (will become disabled if no new prompt)
            });

            // Initial call to set button state
            updateInstallButtonState();

            // For URL handling, you can check the current URL on load or use the `protocol_handlers` in manifest.json
            const currentUrl = window.location.href;
            const expectedBaseUrl = 'https://f120ef96076b.ngrok-free.app/'; // Your PWA's base URL

            if (currentUrl.startsWith(expectedBaseUrl)) {
                console.log('App opened with base URL:', currentUrl);
                const path = currentUrl.substring(expectedBaseUrl.length);
                if (path) {
                    console.log('Deep link path:', path);
                }
            }
        });
    </script>
</body>
</html>
