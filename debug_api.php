<?php
/**
 * Debug API to test individual endpoints
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

// Supabase configuration
$supabaseUrl = "https://xswrokjllrkdyepluztn.supabase.co";
$supabaseServiceRoleKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inhzd3Jva2psbHJrZHllcGx1enRuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjczMDU4MywiZXhwIjoyMDY4MzA2NTgzfQ.5Ek5Rlj3Iozc476u9ebVTlREp6RtTRYqmK8Sq_1uzrA";

function supabaseRequest($endpoint) {
    global $supabaseUrl, $supabaseServiceRoleKey;
    
    $url = $supabaseUrl . "/rest/v1/" . $endpoint;
    
    $headers = [
        "apikey: $supabaseServiceRoleKey",
        "Authorization: Bearer $supabaseServiceRoleKey",
        "Content-Type: application/json",
        "Accept: application/json"
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        return ['error' => "cURL Error: " . $error];
    }
    
    if ($httpCode !== 200) {
        return ['error' => "HTTP Error: " . $httpCode . " - " . $response];
    }
    
    return json_decode($response, true);
}

$test = $_GET['test'] ?? 'all';

$results = [];

switch ($test) {
    case 'currency':
        $results['currency'] = supabaseRequest('currency?is_active=eq.1&select=symbol&limit=1');
        break;
        
    case 'categories':
        $results['categories'] = supabaseRequest('categories?select=name,emoji,ranking&order=ranking.asc');
        break;
        
    case 'food_items':
        $results['food_items'] = supabaseRequest('food_items?category=eq.Popular&select=id,item_name,price,original_price,image,description,category&order=id.desc&limit=5');
        break;
        
    case 'offers':
        $results['offers'] = supabaseRequest('offers?order=expiry_date.asc&select=id,banner,link,discount,flat,restaurant_name,code,expiry_date,created_at,description&limit=5');
        break;
        
    case 'restaurants':
        $results['restaurants'] = supabaseRequest('restaurants?select=id,name,image,description,restaurant_profile(*)&order=id&limit=5');
        break;
        
    default:
        // Test all
        $results['currency'] = supabaseRequest('currency?is_active=eq.1&select=symbol&limit=1');
        $results['categories'] = supabaseRequest('categories?select=name,emoji,ranking&order=ranking.asc&limit=5');
        $results['food_items'] = supabaseRequest('food_items?category=eq.Popular&select=id,item_name,price,original_price,image,description,category&order=id.desc&limit=3');
        $results['offers'] = supabaseRequest('offers?order=expiry_date.asc&select=id,banner,link,discount,flat,restaurant_name,code,expiry_date,created_at,description&limit=3');
        $results['restaurants'] = supabaseRequest('restaurants?select=id,name,image,description,restaurant_profile(*)&order=id&limit=3');
        break;
}

// Add metadata
$results['metadata'] = [
    'test' => $test,
    'timestamp' => date('Y-m-d H:i:s'),
    'server_time' => microtime(true)
];

echo json_encode($results, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
?>
