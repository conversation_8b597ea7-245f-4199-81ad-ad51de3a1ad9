<?php
/**
 * GetReportsReports
 *
 * PHP version 5
 *
 * @category Class
 * @package  Brevo\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * Brevo API
 *
 * Brevo provide a RESTFul API that can be used with any languages. With this API, you will be able to :   - Manage your campaigns and get the statistics   - Manage your contacts   - Send transactional Emails and SMS   - and much more...  You can download our wrappers at https://github.com/orgs/brevo  **Possible responses**   | Code | Message |   | :-------------: | ------------- |   | 200  | OK. Successful Request  |   | 201  | OK. Successful Creation |   | 202  | OK. Request accepted |   | 204  | OK. Successful Update/Deletion  |   | 400  | Error. Bad Request  |   | 401  | Error. Authentication Needed  |   | 402  | Error. Not enough credit, plan upgrade needed  |   | 403  | Error. Permission denied  |   | 404  | Error. Object does not exist |   | 405  | Error. Method not allowed  |   | 406  | Error. Not Acceptable  |
 *
 * OpenAPI spec version: 3.0.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.29
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Brevo\Client\Model;

use \ArrayAccess;
use \Brevo\Client\ObjectSerializer;

/**
 * GetReportsReports Class Doc Comment
 *
 * @category Class
 * @package  Brevo\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class GetReportsReports implements ModelInterface, ArrayAccess
{
    const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $swaggerModelName = 'getReports_reports';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerTypes = [
        'date' => '\DateTime',
        'requests' => 'int',
        'delivered' => 'int',
        'hardBounces' => 'int',
        'softBounces' => 'int',
        'clicks' => 'int',
        'uniqueClicks' => 'int',
        'opens' => 'int',
        'uniqueOpens' => 'int',
        'spamReports' => 'int',
        'blocked' => 'int',
        'invalid' => 'int',
        'unsubscribed' => 'int'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerFormats = [
        'date' => 'date',
        'requests' => 'int64',
        'delivered' => 'int64',
        'hardBounces' => 'int64',
        'softBounces' => 'int64',
        'clicks' => 'int64',
        'uniqueClicks' => 'int64',
        'opens' => 'int64',
        'uniqueOpens' => 'int64',
        'spamReports' => 'int64',
        'blocked' => 'int64',
        'invalid' => 'int64',
        'unsubscribed' => 'int64'
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerTypes()
    {
        return self::$swaggerTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerFormats()
    {
        return self::$swaggerFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'date' => 'date',
        'requests' => 'requests',
        'delivered' => 'delivered',
        'hardBounces' => 'hardBounces',
        'softBounces' => 'softBounces',
        'clicks' => 'clicks',
        'uniqueClicks' => 'uniqueClicks',
        'opens' => 'opens',
        'uniqueOpens' => 'uniqueOpens',
        'spamReports' => 'spamReports',
        'blocked' => 'blocked',
        'invalid' => 'invalid',
        'unsubscribed' => 'unsubscribed'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'date' => 'setDate',
        'requests' => 'setRequests',
        'delivered' => 'setDelivered',
        'hardBounces' => 'setHardBounces',
        'softBounces' => 'setSoftBounces',
        'clicks' => 'setClicks',
        'uniqueClicks' => 'setUniqueClicks',
        'opens' => 'setOpens',
        'uniqueOpens' => 'setUniqueOpens',
        'spamReports' => 'setSpamReports',
        'blocked' => 'setBlocked',
        'invalid' => 'setInvalid',
        'unsubscribed' => 'setUnsubscribed'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'date' => 'getDate',
        'requests' => 'getRequests',
        'delivered' => 'getDelivered',
        'hardBounces' => 'getHardBounces',
        'softBounces' => 'getSoftBounces',
        'clicks' => 'getClicks',
        'uniqueClicks' => 'getUniqueClicks',
        'opens' => 'getOpens',
        'uniqueOpens' => 'getUniqueOpens',
        'spamReports' => 'getSpamReports',
        'blocked' => 'getBlocked',
        'invalid' => 'getInvalid',
        'unsubscribed' => 'getUnsubscribed'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$swaggerModelName;
    }

    

    

    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['date'] = isset($data['date']) ? $data['date'] : null;
        $this->container['requests'] = isset($data['requests']) ? $data['requests'] : null;
        $this->container['delivered'] = isset($data['delivered']) ? $data['delivered'] : null;
        $this->container['hardBounces'] = isset($data['hardBounces']) ? $data['hardBounces'] : null;
        $this->container['softBounces'] = isset($data['softBounces']) ? $data['softBounces'] : null;
        $this->container['clicks'] = isset($data['clicks']) ? $data['clicks'] : null;
        $this->container['uniqueClicks'] = isset($data['uniqueClicks']) ? $data['uniqueClicks'] : null;
        $this->container['opens'] = isset($data['opens']) ? $data['opens'] : null;
        $this->container['uniqueOpens'] = isset($data['uniqueOpens']) ? $data['uniqueOpens'] : null;
        $this->container['spamReports'] = isset($data['spamReports']) ? $data['spamReports'] : null;
        $this->container['blocked'] = isset($data['blocked']) ? $data['blocked'] : null;
        $this->container['invalid'] = isset($data['invalid']) ? $data['invalid'] : null;
        $this->container['unsubscribed'] = isset($data['unsubscribed']) ? $data['unsubscribed'] : null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if ($this->container['date'] === null) {
            $invalidProperties[] = "'date' can't be null";
        }
        if ($this->container['requests'] === null) {
            $invalidProperties[] = "'requests' can't be null";
        }
        if ($this->container['delivered'] === null) {
            $invalidProperties[] = "'delivered' can't be null";
        }
        if ($this->container['hardBounces'] === null) {
            $invalidProperties[] = "'hardBounces' can't be null";
        }
        if ($this->container['softBounces'] === null) {
            $invalidProperties[] = "'softBounces' can't be null";
        }
        if ($this->container['clicks'] === null) {
            $invalidProperties[] = "'clicks' can't be null";
        }
        if ($this->container['uniqueClicks'] === null) {
            $invalidProperties[] = "'uniqueClicks' can't be null";
        }
        if ($this->container['opens'] === null) {
            $invalidProperties[] = "'opens' can't be null";
        }
        if ($this->container['uniqueOpens'] === null) {
            $invalidProperties[] = "'uniqueOpens' can't be null";
        }
        if ($this->container['spamReports'] === null) {
            $invalidProperties[] = "'spamReports' can't be null";
        }
        if ($this->container['blocked'] === null) {
            $invalidProperties[] = "'blocked' can't be null";
        }
        if ($this->container['invalid'] === null) {
            $invalidProperties[] = "'invalid' can't be null";
        }
        if ($this->container['unsubscribed'] === null) {
            $invalidProperties[] = "'unsubscribed' can't be null";
        }
        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets date
     *
     * @return \DateTime
     */
    public function getDate()
    {
        return $this->container['date'];
    }

    /**
     * Sets date
     *
     * @param \DateTime $date Date of the statistics
     *
     * @return $this
     */
    public function setDate($date)
    {
        $this->container['date'] = $date;

        return $this;
    }

    /**
     * Gets requests
     *
     * @return int
     */
    public function getRequests()
    {
        return $this->container['requests'];
    }

    /**
     * Sets requests
     *
     * @param int $requests Number of requests for the date
     *
     * @return $this
     */
    public function setRequests($requests)
    {
        $this->container['requests'] = $requests;

        return $this;
    }

    /**
     * Gets delivered
     *
     * @return int
     */
    public function getDelivered()
    {
        return $this->container['delivered'];
    }

    /**
     * Sets delivered
     *
     * @param int $delivered Number of delivered emails for the date
     *
     * @return $this
     */
    public function setDelivered($delivered)
    {
        $this->container['delivered'] = $delivered;

        return $this;
    }

    /**
     * Gets hardBounces
     *
     * @return int
     */
    public function getHardBounces()
    {
        return $this->container['hardBounces'];
    }

    /**
     * Sets hardBounces
     *
     * @param int $hardBounces Number of hardbounces for the date
     *
     * @return $this
     */
    public function setHardBounces($hardBounces)
    {
        $this->container['hardBounces'] = $hardBounces;

        return $this;
    }

    /**
     * Gets softBounces
     *
     * @return int
     */
    public function getSoftBounces()
    {
        return $this->container['softBounces'];
    }

    /**
     * Sets softBounces
     *
     * @param int $softBounces Number of softbounces for the date
     *
     * @return $this
     */
    public function setSoftBounces($softBounces)
    {
        $this->container['softBounces'] = $softBounces;

        return $this;
    }

    /**
     * Gets clicks
     *
     * @return int
     */
    public function getClicks()
    {
        return $this->container['clicks'];
    }

    /**
     * Sets clicks
     *
     * @param int $clicks Number of clicks for the date
     *
     * @return $this
     */
    public function setClicks($clicks)
    {
        $this->container['clicks'] = $clicks;

        return $this;
    }

    /**
     * Gets uniqueClicks
     *
     * @return int
     */
    public function getUniqueClicks()
    {
        return $this->container['uniqueClicks'];
    }

    /**
     * Sets uniqueClicks
     *
     * @param int $uniqueClicks Number of unique clicks for the date
     *
     * @return $this
     */
    public function setUniqueClicks($uniqueClicks)
    {
        $this->container['uniqueClicks'] = $uniqueClicks;

        return $this;
    }

    /**
     * Gets opens
     *
     * @return int
     */
    public function getOpens()
    {
        return $this->container['opens'];
    }

    /**
     * Sets opens
     *
     * @param int $opens Number of openings for the date
     *
     * @return $this
     */
    public function setOpens($opens)
    {
        $this->container['opens'] = $opens;

        return $this;
    }

    /**
     * Gets uniqueOpens
     *
     * @return int
     */
    public function getUniqueOpens()
    {
        return $this->container['uniqueOpens'];
    }

    /**
     * Sets uniqueOpens
     *
     * @param int $uniqueOpens Number of unique openings for the date
     *
     * @return $this
     */
    public function setUniqueOpens($uniqueOpens)
    {
        $this->container['uniqueOpens'] = $uniqueOpens;

        return $this;
    }

    /**
     * Gets spamReports
     *
     * @return int
     */
    public function getSpamReports()
    {
        return $this->container['spamReports'];
    }

    /**
     * Sets spamReports
     *
     * @param int $spamReports Number of complaints (spam reports) for the date
     *
     * @return $this
     */
    public function setSpamReports($spamReports)
    {
        $this->container['spamReports'] = $spamReports;

        return $this;
    }

    /**
     * Gets blocked
     *
     * @return int
     */
    public function getBlocked()
    {
        return $this->container['blocked'];
    }

    /**
     * Sets blocked
     *
     * @param int $blocked Number of blocked emails for the date
     *
     * @return $this
     */
    public function setBlocked($blocked)
    {
        $this->container['blocked'] = $blocked;

        return $this;
    }

    /**
     * Gets invalid
     *
     * @return int
     */
    public function getInvalid()
    {
        return $this->container['invalid'];
    }

    /**
     * Sets invalid
     *
     * @param int $invalid Number of invalid emails for the date
     *
     * @return $this
     */
    public function setInvalid($invalid)
    {
        $this->container['invalid'] = $invalid;

        return $this;
    }

    /**
     * Gets unsubscribed
     *
     * @return int
     */
    public function getUnsubscribed()
    {
        return $this->container['unsubscribed'];
    }

    /**
     * Sets unsubscribed
     *
     * @param int $unsubscribed Number of unsubscribed emails for the date
     *
     * @return $this
     */
    public function setUnsubscribed($unsubscribed)
    {
        $this->container['unsubscribed'] = $unsubscribed;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    #[\ReturnTypeWillChange]
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }

    /**
     * Sets value based on offset.
     *
     * @param integer $offset Offset
     * @param mixed   $value  Value to be set
     *
     * @return void
     */
    #[\ReturnTypeWillChange]
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    #[\ReturnTypeWillChange]
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) { // use JSON pretty print
            return json_encode(
                ObjectSerializer::sanitizeForSerialization($this),
                JSON_PRETTY_PRINT
            );
        }

        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


