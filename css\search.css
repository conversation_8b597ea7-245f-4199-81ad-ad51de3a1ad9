/* search.css - Contains styles specifically for the search functionality */

/* Header Search Bar Container */
.header-search-bar-container {
    width: 100%;
    display: none;
    align-items: center;
    justify-content: space-between;
    opacity: 0;
    transition: opacity 0.3s ease-out;
}

.header-search-bar-container.active {
    display: flex;
    opacity: 1;
}

.header-search-bar-container .search-input {
    flex-grow: 1;
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 100px;
    padding: 1rem;
    margin: 3.5px;
    transition: box-shadow 0.3s ease, transform 0.3s ease-out, opacity 0.3s ease-out;
    transform: translateY(-100%);
    opacity: 0;
}

.header-search-bar-container.active .search-input {
    transform: translateY(0);
    opacity: 1;
}

.header-search-bar-container .search-input:focus-within {
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.5);
}

.header-search-bar-container .search-icon-in-bar {
    color: var(--white);
    font-size: 1.1rem;
    margin-right: 0.75rem;
}

.header-search-bar-container input {
    flex-grow: 1;
    border: none;
    background-color: transparent;
    color: var(--white);
    outline: none;
    font-size: 1rem;
    line-height: 1.5;
}

.header-search-bar-container input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.header-search-bar-container input:focus {
    outline: none;
}

.header-search-bar-container .close-search-btn {
    background: transparent;
    border: none;
    color: white;
    font-size: 1.8rem;
    margin-left: 1rem;
    cursor: pointer;
    line-height: 1;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* No Results Message */
.no-results-message {
    display: none;
    margin-top: 2rem;
    padding: 2rem;
    background-color: white;
    border-radius: 12px;
    flex-direction: column;
    align-items: center;
    color: black;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    width: 90%;
    max-width: 400px;
}

.no-results-message.visible {
    display: flex;
}

.no-results-message .icon {
    font-size: 3rem;
    color: #ccc;
    margin-bottom: 1rem;
}

.no-results-message .message-text {
    font-size: 1.1rem;
    font-weight: 500;
}

/* Search Content Wrapper */
.search-content-wrapper {
    width: 100%;
    display: none;
    flex-direction: column;
    align-items: center;
    text-align: left;
    padding-top: 1rem;
}

.search-content-wrapper.active {
    display: flex;
}

/* Section Container (for Popular Foodies, Hot Recommendations, Restaurants) */
.section-container {
    width: 100%;
    max-width: 800px;
    margin-top: 1rem;
    text-align: left;
    display: none;
}

.section-container.visible {
    display: block;
}

.section-container h3 {
    font-size: 1.1rem;
    color: var(--text);
    margin-bottom: 1rem;
    padding: 0 1.5rem;
}

/* Tags Container (for Popular Foodies and scrollable lists) */
.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    padding: 0 1.5rem;
}

.tags-container.scrollable {
    flex-wrap: nowrap;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    padding-bottom: 10px;
    scrollbar-width: thin;
    scrollbar-color: var(--primary) var(--neutral);
}

.tags-container.scrollable::-webkit-scrollbar {
    height: 8px;
}

.tags-container.scrollable::-webkit-scrollbar-track {
    background: var(--neutral);
    border-radius: 10px;
}

.tags-container.scrollable::-webkit-scrollbar-thumb {
    background-color: var(--primary);
    border-radius: 10px;
    border: 2px solid var(--neutral);
}

.tag-item {
    background-color: #e0e0e0;
    color: var(--text);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-shrink: 0;
}

/* Reusable Card Base Styles */
.card-base {
    background-color: var(--white);
    border-radius: 15px;
    padding: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    width: 100%;
    flex-shrink: 0;
    cursor: pointer;
    position: relative;
    box-shadow: var(--shadow);
}

.card-image-section {
    width: 100%;
    height: 100px;
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
    overflow: hidden;
    background-color: #333333;
    display: flex;
    justify-content: center;
    align-items: center;
}

.card-image-section img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.card-info-section {
    width: 100%;
    padding: 0.75rem 0.5rem;
    font-size: 1rem;
    color: var(--text);
    font-weight: 600;
    text-align: center;
    min-height: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.card-info-section h5 {
    font-size: 1rem;
    margin: 0;
    color: var(--text);
    font-weight: 600;
}

.card-info-section .sub-text {
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.2rem;
}

.card-bottom-section {
    width: 100%;
    padding: 0.75rem 0.5rem;
    font-size: 0.9rem;
    font-weight: bold;
    color: var(--primary);
    text-align: center;
    border-bottom-left-radius: 15px;
    border-bottom-right-radius: 15px;
    background-color: var(--neutral);
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
}

.card-bottom-section .price-info {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 0.25rem;
}

.card-bottom-section small {
    font-size: 0.7rem;
    color: #999;
    text-decoration: line-through;
    margin-left: 0;
}

/* Specific styles for hot recommendation item cards */
.hot-recommendation-item-card {
    width: 150px; /* Fixed width for horizontal scroll */
}

/* Search Results List */
.search-results-list {
    width: 100%;
    max-width: 800px;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    justify-content: center;
    flex-grow: 1;
    display: none;
}
.search-results-list.visible {
    display: flex;
}

.search-category-section {
    background-color: var(--white);
    border-radius: 15px;
    box-shadow: var(--shadow);
    text-align: left;
    width: 100%;
    display: none;
}

.search-category-section.visible {
    display: block;
}

.search-category-section h3 {
    font-size: 1.2rem;
    color: var(--primary);
    margin-bottom: 1rem;
    padding: 1.5rem 1.5rem 0.5rem 1.5rem;
}

/* Grid for food items (3 per row) */
#foodResultsContainer {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
    justify-items: center;
    padding: 1.5rem;
}

/* Grid for restaurant search results (single column, full width) */
#restaurantSearchResultsContainer {
    display: grid;
    grid-template-columns: 1fr; /* Single column, full width */
    gap: 1rem;
    padding: 1.5rem;
}

/* Styles for restaurant display cards (horizontal scroll) */
.restaurant-display-card {
    background-color: var(--white);
    border-radius: 12px;
    padding: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: left;
    width: 280px; /* Fixed width for horizontal scroll */
    flex-shrink: 0;
    cursor: pointer;
    box-shadow: var(--shadow);
    position: relative;
    border: 1px solid rgba(255, 107, 53, 0.2);
}

.restaurant-display-card .restaurant-image-section {
    width: 100%;
    height: 150px;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    overflow: hidden;
    background-color: #eee;
    display: flex;
    justify-content: center;
    align-items: center;
}

.restaurant-display-card .restaurant-image-section img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.restaurant-display-card .restaurant-info-section {
    width: 100%;
    padding: 0.75rem 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.restaurant-display-card .restaurant-name-rating {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
}

.restaurant-display-card .restaurant-name-rating h5 {
    font-size: 1.1rem;
    color: var(--text);
    font-weight: 600;
    margin: 0;
}

.restaurant-display-card .rating-info {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.9rem;
    color: #666;
}

.restaurant-display-card .rating-info i {
    color: #FFD700;
    font-size: 0.8rem;
}

.restaurant-display-card .delivery-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.restaurant-display-card .delivery-info .delivery-time,
.restaurant-display-card .delivery-info .delivery-fee {
    display: flex;
    align-items: center;
    gap: 0.2rem;
}

.restaurant-display-card .delivery-info i {
    font-size: 0.75rem;
    color: #999;
}

.restaurant-display-card .delivery-info .delivery-fee.free {
    color: #4CAF50;
    font-weight: 600;
}
.restaurant-display-card .delivery-info .delivery-fee.paid {
    color: #333;
}

/* New styles for restaurant search result items (full width, home page card style) */
.restaurant-search-result-item {
    background-color: var(--white);
    border-radius: 12px;
    padding: 0; /* Internal sections will have padding */
    display: flex;
    flex-direction: column; /* Image on top, info below */
    align-items: center;
    text-align: left;
    width: 100%; /* Fills the container */
    flex-shrink: 0;
    cursor: pointer;
    box-shadow: var(--shadow);
    position: relative;
    border: 1px solid rgba(255, 107, 53, 0.2);
}

.restaurant-search-result-item .restaurant-image-section {
    width: 100%;
    height: 150px;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    overflow: hidden;
    background-color: #eee;
    display: flex;
    justify-content: center;
    align-items: center;
}

.restaurant-search-result-item .restaurant-image-section img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.restaurant-search-result-item .restaurant-info-section {
    width: 100%;
    padding: 0.75rem 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.restaurant-search-result-item .restaurant-name-rating {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
}

.restaurant-search-result-item .restaurant-name-rating h5 {
    font-size: 1.1rem;
    color: var(--text);
    font-weight: 600;
    margin: 0;
}

.restaurant-search-result-item .rating-info {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.9rem;
    color: #666;
}

.restaurant-search-result-item .rating-info i {
    color: #FFD700;
    font-size: 0.8rem;
}

.restaurant-search-result-item .delivery-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.restaurant-search-result-item .delivery-info .delivery-time,
.restaurant-search-result-item .delivery-info .delivery-fee {
    display: flex;
    align-items: center;
    gap: 0.2rem;
}

.restaurant-search-result-item .delivery-info i {
    font-size: 0.75rem;
    color: #999;
}

.restaurant-search-result-item .delivery-info .delivery-fee.free {
    color: #4CAF50;
    font-weight: 600;
}
.restaurant-search-result-item .delivery-info .delivery-fee.paid {
    color: #333;
}
