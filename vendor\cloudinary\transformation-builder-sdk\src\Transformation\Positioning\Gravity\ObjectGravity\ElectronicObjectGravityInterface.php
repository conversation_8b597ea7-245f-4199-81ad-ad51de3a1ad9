<?php
/**
 * This file is part of the Cloudinary PHP package.
 *
 * (c) Cloudinary
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Cloudinary\Transformation;

/**
 * Interface ElectronicObjectGravityInterface
 *
 * @api
 */
interface ElectronicObjectGravityInterface
{
    //Electronic Category
    public const ELECTRONIC = 'electronic';
    public const TV_MONITOR = 'tvmonitor';
    public const LAPTOP     = 'laptop';
    public const MOUSE  = 'mouse';
    public const REMOTE = 'remote';
    public const KEYBOARD = 'keyboard';
    public const CELLPHONE = 'cellphone';
}
