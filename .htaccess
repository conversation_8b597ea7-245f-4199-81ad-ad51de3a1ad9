# PWA and Icon Optimization
<IfModule mod_headers.c>
    # Cache icons for 1 week
    <FilesMatch "\.(png|jpg|jpeg|gif|webp|ico)$">
        Header set Cache-Control "public, max-age=604800"
        Header set Access-Control-Allow-Origin "*"
    </FilesMatch>
    
    # Cache manifest for 1 hour (shorter to allow updates)
    <FilesMatch "manifest\.json$">
        Header set Cache-Control "public, max-age=3600"
        Header set Content-Type "application/manifest+json"
        Header set Access-Control-Allow-Origin "*"
    </FilesMatch>
    
    # Cache service worker for 1 hour
    <FilesMatch "service-worker\.js$">
        Header set Cache-Control "public, max-age=3600"
        Header set Access-Control-Allow-Origin "*"
    </FilesMatch>
</IfModule>

# MIME Types for PWA
<IfModule mod_mime.c>
    AddType application/manifest+json .webmanifest .json
    AddType image/webp .webp
    AddType text/cache-manifest .appcache
</IfModule>

# Compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE application/manifest+json
</IfModule>

# Security headers
<IfModule mod_headers.c>
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options nosniff
    
    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Prevent clickjacking
    Header always set X-Frame-Options SAMEORIGIN
</IfModule>
