# GetCampaignStats

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**listId** | **int** | List Id of email campaign (only in case of get email campaign(s)(not for global stats)) | [optional] 
**uniqueClicks** | **int** | Number of unique clicks for the campaign | 
**clickers** | **int** | Number of total clicks for the campaign | 
**complaints** | **int** | Number of complaints (Spam reports) for the campaign | 
**delivered** | **int** | Number of delivered emails for the campaign | 
**sent** | **int** | Number of sent emails for the campaign | 
**softBounces** | **int** | Number of softbounce for the campaign | 
**hardBounces** | **int** | Number of harbounce for the campaign | 
**uniqueViews** | **int** | Number of unique openings for the campaign | 
**trackableViews** | **int** | Recipients without any privacy protection option enabled in their email client | 
**trackableViewsRate** | **float** | Rate of recipients without any privacy protection option enabled in their email client | [optional] 
**estimatedViews** | **int** | Rate of recipients without any privacy protection option enabled in their email client, applied to all delivered emails | [optional] 
**unsubscriptions** | **int** | Number of unsubscription for the campaign | 
**viewed** | **int** | Number of openings for the campaign | 
**deferred** | **int** | Number of deferred emails for the campaign | [optional] 
**returnBounce** | **int** | Total number of non-delivered campaigns for a particular campaign id. | [optional] 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


