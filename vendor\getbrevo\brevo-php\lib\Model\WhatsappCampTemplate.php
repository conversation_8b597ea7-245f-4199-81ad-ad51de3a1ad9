<?php
/**
 * WhatsappCampTemplate
 *
 * PHP version 5
 *
 * @category Class
 * @package  Brevo\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * Brevo API
 *
 * Brevo provide a RESTFul API that can be used with any languages. With this API, you will be able to :   - Manage your campaigns and get the statistics   - Manage your contacts   - Send transactional Emails and SMS   - and much more...  You can download our wrappers at https://github.com/orgs/brevo  **Possible responses**   | Code | Message |   | :-------------: | ------------- |   | 200  | OK. Successful Request  |   | 201  | OK. Successful Creation |   | 202  | OK. Request accepted |   | 204  | OK. Successful Update/Deletion  |   | 400  | Error. Bad Request  |   | 401  | Error. Authentication Needed  |   | 402  | Error. Not enough credit, plan upgrade needed  |   | 403  | Error. Permission denied  |   | 404  | Error. Object does not exist |   | 405  | Error. Method not allowed  |   | 406  | Error. Not Acceptable  |
 *
 * OpenAPI spec version: 3.0.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.29
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Brevo\Client\Model;

use \ArrayAccess;
use \Brevo\Client\ObjectSerializer;

/**
 * WhatsappCampTemplate Class Doc Comment
 *
 * @category Class
 * @package  Brevo\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class WhatsappCampTemplate implements ModelInterface, ArrayAccess
{
    const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $swaggerModelName = 'WhatsappCampTemplate';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerTypes = [
        'name' => 'string',
        'category' => 'string',
        'language' => 'string',
        'containsButton' => 'bool',
        'displayHeader' => 'bool',
        'headerType' => 'string',
        'components' => '\Brevo\Client\Model\ComponentItems[]',
        'headerVariables' => '\Brevo\Client\Model\VariablesItems[]',
        'bodyVariables' => '\Brevo\Client\Model\VariablesItems[]',
        'buttonType' => 'string',
        'hideFooter' => 'bool'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerFormats = [
        'name' => null,
        'category' => null,
        'language' => null,
        'containsButton' => null,
        'displayHeader' => null,
        'headerType' => null,
        'components' => null,
        'headerVariables' => null,
        'bodyVariables' => null,
        'buttonType' => null,
        'hideFooter' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerTypes()
    {
        return self::$swaggerTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerFormats()
    {
        return self::$swaggerFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'name' => 'name',
        'category' => 'category',
        'language' => 'language',
        'containsButton' => 'contains_button',
        'displayHeader' => 'display_header',
        'headerType' => 'header_type',
        'components' => 'components',
        'headerVariables' => 'header_variables',
        'bodyVariables' => 'body_variables',
        'buttonType' => 'button_type',
        'hideFooter' => 'hide_footer'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'name' => 'setName',
        'category' => 'setCategory',
        'language' => 'setLanguage',
        'containsButton' => 'setContainsButton',
        'displayHeader' => 'setDisplayHeader',
        'headerType' => 'setHeaderType',
        'components' => 'setComponents',
        'headerVariables' => 'setHeaderVariables',
        'bodyVariables' => 'setBodyVariables',
        'buttonType' => 'setButtonType',
        'hideFooter' => 'setHideFooter'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'name' => 'getName',
        'category' => 'getCategory',
        'language' => 'getLanguage',
        'containsButton' => 'getContainsButton',
        'displayHeader' => 'getDisplayHeader',
        'headerType' => 'getHeaderType',
        'components' => 'getComponents',
        'headerVariables' => 'getHeaderVariables',
        'bodyVariables' => 'getBodyVariables',
        'buttonType' => 'getButtonType',
        'hideFooter' => 'getHideFooter'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$swaggerModelName;
    }

    

    

    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['name'] = isset($data['name']) ? $data['name'] : null;
        $this->container['category'] = isset($data['category']) ? $data['category'] : null;
        $this->container['language'] = isset($data['language']) ? $data['language'] : null;
        $this->container['containsButton'] = isset($data['containsButton']) ? $data['containsButton'] : null;
        $this->container['displayHeader'] = isset($data['displayHeader']) ? $data['displayHeader'] : null;
        $this->container['headerType'] = isset($data['headerType']) ? $data['headerType'] : null;
        $this->container['components'] = isset($data['components']) ? $data['components'] : null;
        $this->container['headerVariables'] = isset($data['headerVariables']) ? $data['headerVariables'] : null;
        $this->container['bodyVariables'] = isset($data['bodyVariables']) ? $data['bodyVariables'] : null;
        $this->container['buttonType'] = isset($data['buttonType']) ? $data['buttonType'] : null;
        $this->container['hideFooter'] = isset($data['hideFooter']) ? $data['hideFooter'] : null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets name
     *
     * @return string
     */
    public function getName()
    {
        return $this->container['name'];
    }

    /**
     * Sets name
     *
     * @param string $name name of the template
     *
     * @return $this
     */
    public function setName($name)
    {
        $this->container['name'] = $name;

        return $this;
    }

    /**
     * Gets category
     *
     * @return string
     */
    public function getCategory()
    {
        return $this->container['category'];
    }

    /**
     * Sets category
     *
     * @param string $category description of the template
     *
     * @return $this
     */
    public function setCategory($category)
    {
        $this->container['category'] = $category;

        return $this;
    }

    /**
     * Gets language
     *
     * @return string
     */
    public function getLanguage()
    {
        return $this->container['language'];
    }

    /**
     * Sets language
     *
     * @param string $language language of the template
     *
     * @return $this
     */
    public function setLanguage($language)
    {
        $this->container['language'] = $language;

        return $this;
    }

    /**
     * Gets containsButton
     *
     * @return bool
     */
    public function getContainsButton()
    {
        return $this->container['containsButton'];
    }

    /**
     * Sets containsButton
     *
     * @param bool $containsButton containsButton
     *
     * @return $this
     */
    public function setContainsButton($containsButton)
    {
        $this->container['containsButton'] = $containsButton;

        return $this;
    }

    /**
     * Gets displayHeader
     *
     * @return bool
     */
    public function getDisplayHeader()
    {
        return $this->container['displayHeader'];
    }

    /**
     * Sets displayHeader
     *
     * @param bool $displayHeader displayHeader
     *
     * @return $this
     */
    public function setDisplayHeader($displayHeader)
    {
        $this->container['displayHeader'] = $displayHeader;

        return $this;
    }

    /**
     * Gets headerType
     *
     * @return string
     */
    public function getHeaderType()
    {
        return $this->container['headerType'];
    }

    /**
     * Sets headerType
     *
     * @param string $headerType type of header
     *
     * @return $this
     */
    public function setHeaderType($headerType)
    {
        $this->container['headerType'] = $headerType;

        return $this;
    }

    /**
     * Gets components
     *
     * @return \Brevo\Client\Model\ComponentItems[]
     */
    public function getComponents()
    {
        return $this->container['components'];
    }

    /**
     * Sets components
     *
     * @param \Brevo\Client\Model\ComponentItems[] $components array of component item objects
     *
     * @return $this
     */
    public function setComponents($components)
    {
        $this->container['components'] = $components;

        return $this;
    }

    /**
     * Gets headerVariables
     *
     * @return \Brevo\Client\Model\VariablesItems[]
     */
    public function getHeaderVariables()
    {
        return $this->container['headerVariables'];
    }

    /**
     * Sets headerVariables
     *
     * @param \Brevo\Client\Model\VariablesItems[] $headerVariables array of variables item object
     *
     * @return $this
     */
    public function setHeaderVariables($headerVariables)
    {
        $this->container['headerVariables'] = $headerVariables;

        return $this;
    }

    /**
     * Gets bodyVariables
     *
     * @return \Brevo\Client\Model\VariablesItems[]
     */
    public function getBodyVariables()
    {
        return $this->container['bodyVariables'];
    }

    /**
     * Sets bodyVariables
     *
     * @param \Brevo\Client\Model\VariablesItems[] $bodyVariables array of variables item variables
     *
     * @return $this
     */
    public function setBodyVariables($bodyVariables)
    {
        $this->container['bodyVariables'] = $bodyVariables;

        return $this;
    }

    /**
     * Gets buttonType
     *
     * @return string
     */
    public function getButtonType()
    {
        return $this->container['buttonType'];
    }

    /**
     * Sets buttonType
     *
     * @param string $buttonType buttonType
     *
     * @return $this
     */
    public function setButtonType($buttonType)
    {
        $this->container['buttonType'] = $buttonType;

        return $this;
    }

    /**
     * Gets hideFooter
     *
     * @return bool
     */
    public function getHideFooter()
    {
        return $this->container['hideFooter'];
    }

    /**
     * Sets hideFooter
     *
     * @param bool $hideFooter hideFooter
     *
     * @return $this
     */
    public function setHideFooter($hideFooter)
    {
        $this->container['hideFooter'] = $hideFooter;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    #[\ReturnTypeWillChange]
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }

    /**
     * Sets value based on offset.
     *
     * @param integer $offset Offset
     * @param mixed   $value  Value to be set
     *
     * @return void
     */
    #[\ReturnTypeWillChange]
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    #[\ReturnTypeWillChange]
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) { // use JSON pretty print
            return json_encode(
                ObjectSerializer::sanitizeForSerialization($this),
                JSON_PRETTY_PRINT
            );
        }

        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


