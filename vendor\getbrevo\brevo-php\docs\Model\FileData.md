# FileData

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**url** | **string** | Url of uploaded file | [optional] 
**id** | **string** | Id of uploaded file | [optional] 
**name** | **string** | Name of uploaded file | [optional] 
**authorId** | **string** | Account id of user which created the file | [optional] 
**author** | **object** | Account details of user which created the file | [optional] 
**contactId** | **int** | Contact id of contact on which file is uploaded | [optional] 
**dealId** | **string** | Deal id linked to a file | [optional] 
**companyId** | **string** | Company id linked to a file | [optional] 
**size** | **int** | Size of file in bytes | [optional] 
**createdAt** | [**\DateTime**] | File created date/time | [optional] 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


