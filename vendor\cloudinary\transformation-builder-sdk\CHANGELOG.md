2.1.2 / 2025-02-03
==================

* Fix doc links

2.1.1 / 2025-01-14
==================

  * Fix type declarations

2.1.0 / 2025-01-14
==================

New functionality and features
------------------------------

  * Add PHP type declarations
    * ⚠ While unlikely, this change could potentially alter behavior in certain cases and may require minor adjustments 
    to your code. Please report any unexpected issues encountered.

2.0.0 / 2024-12-16
==================

⚠ BREAKING CHANGES
------------------------------

  * Drop support for PHP `5.6` and `7.x`

New functionality and features
------------------------------

  * Add support for `autoPad` resize
  * Add support for `enhance` effect

1.5.0 / 2024-05-27
==================

New functionality and features
------------------------------

  * Add support for `blurFaces` and `blurRegion` effects
  * Add support for `auto` resize
  * Add support for `upscale` effect

1.4.0 / 2024-01-07
==================

New functionality and features
------------------------------

  * Add support for `generativeFill` background
  * Add support for AI generative effects

1.3.0 / 2023-10-04
==================

New functionality and features
------------------------------

  * Add support for `FetchVideoSource` in video overlays
  * Add support for audio layers

Other Changes
-------------

  * Fix handling of effect value
  * Fix phpdoc @param tags with the NULL type
  * Add documentation references

1.2.0 / 2023-03-23
==================

* Add support for `BackgroundRemoval` effect

1.1.1 / 2023-01-28
==================

* Fix PHP 8.2 deprecation warnings

1.1.0 / 2023-01-19
==================

  * Add support for `DropShadow` effect

1.0.0 / 2022-05-23
===================

  * The first version of the Cloudinary PHP Transformation Builder SDK
