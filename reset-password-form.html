<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#ff5018">
    <title>MIKO - Reset Password</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Updated font import to Montserrat -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@700;800&display=swap" rel="stylesheet">


    <!-- Embedded CSS -->
    <style>
        /* Basic Reset & Variables */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Montserrat', sans-serif; /* Changed font to Montserrat */
        }

        :root {
            /* Site's Base Color Palette (Updated) */
            --primary-accent: #ff5018;
            --secondary-accent: #FF7F50;
            --neutral-base: #F7F7F7;
            --text-dark-elements: #333333;
            --subtle-detail: #A7A7A7;
            --pure-white: #FFFFFF; /* Changed from --white to --pure-white */
            --shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            --transition: all 0.5s ease; /* Increased transition duration */

            /* Light Theme Colors (Default) */
            --body-bg: var(--neutral-base); /* This is still defined but not directly used by body background */
            --text-color: var(--text-dark-elements);
            --card-bg: var(--pure-white);
            --header-bg: var(--primary-accent);
            --header-text: var(--pure-white);
            --border-gray: #E0E0E0; /* Kept for input borders */
        }

        body {
            background-color: var(--primary-accent); /* Changed to primary-accent */
            color: var(--text-color); /* Using new variable */
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1.5rem;
            overflow-x: hidden;
            position: relative; /* Needed for z-index context for overlay */
        }

        /* New styles for the background overlay */
        .background-overlay {
            position: fixed; /* Fixed position to cover the entire viewport */
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            /* Moved background image here */
            background-image: url("data:image/svg+xml,%3Csvg width='200' height='200' viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='foodPattern' x='0' y='0' width='200' height='200' patternUnits='userSpaceOnUse'%3E%3Cg fill='none' stroke='white' stroke-width='1.5' opacity='0.2'%3E%3C!-- Tomato/Round Item --%3E%3Ccircle cx='40' cy='40' r='18'/%3E%3Cpath d='M40 22 L40 58 M22 40 L58 40'/%3E%3C!-- Chili Pepper 1 --%3E%3Cpath d='M160 30 Q175 15, 185 30 T190 50 T180 70 T160 60 Z'/%3E%3C!-- Cucumber Slice --%3E%3Ccircle cx='140' cy='150' r='15'/%3E%3Cpath d='M140 135 L140 165 M125 150 L155 150'/%3E%3C!-- Leaf --%3E%3Cpath d='M60 110 Q70 100, 80 110 T90 120 T80 130 T70 120 T60 110 Z'/%3E%3C!-- Chili Pepper 2 (rotated) --%3E%3Cg transform='translate(20, 100) rotate(90)'%3E%3Cpath d='M160 30 Q175 15, 185 30 T190 50 T180 70 T160 60 Z'/%3E%3C/g%3E%3C/g%3E%3C/pattern%3E%3C/defs%3E%3Crect x='0' y='0' width='100%25' height='100%25' fill='url(%23foodPattern)'/%3E%3C/svg%3E");
            background-repeat: repeat;
            z-index: -1; /* Place it behind other content */
        }

        .reset-container {
            background-color: var(--card-bg); /* Using new variable */
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--shadow);
            width: 100%;
            max-width: 450px;
            text-align: center;
            position: relative; /* Ensure it's above the overlay */
            z-index: 1;
        }

        .reset-container h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--primary-accent);
        }

        .reset-container p {
            font-size: 0.95rem;
            color: var(--subtle-detail);
            margin-bottom: 2rem;
        }

        .input-group {
            margin-bottom: 1.5rem;
            text-align: left;
        }

        .input-group label {
            display: block;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-dark-elements);
        }

        .input-group input {
            width: 100%;
            padding: 1rem;
            border: 1px solid var(--border-gray);
            border-radius: 12px;
            font-size: 1rem;
            color: var(--text-dark-elements);
            background-color: var(--neutral-base);
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.08);
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        .input-group input:focus {
            outline: none;
            border-color: var(--primary-accent);
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.08), 0 0 0 2px rgba(255, 107, 53, 0.2);
        }

        .password-input-container {
            position: relative;
        }

        /* Styles for the eye icon button */
        .password-toggle-btn {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--subtle-detail);
            cursor: pointer;
            font-size: 1.2rem;
            padding: 0.2rem;
            z-index: 10; /* Ensure it's above the input */
        }

        .password-toggle-btn:hover {
            color: var(--text-dark-elements);
        }

        /* Styles for the checkbox container (Remember Me) */
        .checkbox-container {
            display: flex;
            align-items: center;
            margin-top: 0.5rem;
            font-size: 0.9rem;
            color: var(--text-dark-elements);
            margin-bottom: 1rem;
            justify-content: flex-start; /* Align checkbox to the left */
        }

        .checkbox-container input[type="checkbox"] {
            width: auto;
            margin-right: 8px;
            padding: 0;
            height: auto;
            box-shadow: none;
            -webkit-appearance: checkbox;
            -moz-appearance: checkbox;
            appearance: checkbox;
        }

        .submit-btn {
            background-color: var(--primary-accent);
            color: var(--pure-white);
            border: none;
            padding: 1rem;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            width: 100%;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: 0 4px 10px rgba(255, 107, 53, 0.3);
        }

        .submit-btn:hover {
            background-color: var(--secondary-accent);
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(255, 107, 53, 0.4);
        }

        .message-box {
            margin-top: 1.5rem;
            padding: 1rem;
            border-radius: 12px;
            font-size: 0.9rem;
            display: none;
            text-align: left;
        }

        .message-box.success {
            background-color: #e6ffe6;
            color: #28a745;
            border: 1px solid #28a745;
        }

        .message-box.error {
            background-color: #ffe6e6;
            color: #dc3545;
            border: 1px solid #dc3545;
        }

        .loading-dots {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 1em;
        }

        .loading-dots span {
            display: inline-block;
            width: 8px;
            height: 8px;
            background-color: var(--pure-white);
            border-radius: 50%;
            margin: 0 4px;
            animation: bounce 0.6s infinite alternate;
        }

        .loading-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .loading-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes bounce {
            0% {
                transform: translateY(0);
            }
            100% {
                transform: translateY(-8px);
            }
        }

        @media (max-width: 480px) {
            .reset-container {
                padding: 1.5rem;
            }
            .reset-container h1 {
                font-size: 1.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- New div for the background overlay -->
    <div class="background-overlay"></div>

    <div class="reset-container">
        <h1>Reset Your Password</h1>
        <p id="emailDisplay">Loading...</p>
        <div class="input-group">
            <label for="newPassword">New Password</label>
            <div class="password-input-container">
                <input type="password" id="newPassword" placeholder="Enter new password">
                <button type="button" class="password-toggle-btn" id="togglePasswordVisibilityBtn">
                    <i class="fas fa-eye-slash"></i>
                </button>
            </div>
        </div>
        <div class="input-group">
            <label for="confirmPassword">Confirm New Password</label>
            <div class="password-input-container">
                <input type="password" id="confirmPassword" placeholder="Confirm new password">
                <!-- The toggle button is now outside this container, controlling both -->
            </div>
        </div>
        <div class="checkbox-container">
            <input type="checkbox" id="rememberMe">
            <label for="rememberMe">Remember Me</label>
        </div>
        <button class="submit-btn" id="resetPasswordBtn">RESET PASSWORD</button>
        <div id="messageBox" class="message-box"></div>
    </div>

    <!-- Embedded JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const emailDisplay = document.getElementById('emailDisplay');
            const newPasswordInput = document.getElementById('newPassword');
            const confirmPasswordInput = document.getElementById('confirmPassword');
            const resetPasswordBtn = document.getElementById('resetPasswordBtn');
            const messageBox = document.getElementById('messageBox');

            const togglePasswordVisibilityBtn = document.getElementById('togglePasswordVisibilityBtn'); // The single eye button
            const rememberMeCheckbox = document.getElementById('rememberMe'); // Remember Me checkbox

            let token = null;
            let email = null;

            // Function to display messages
            function showMessage(message, type = 'info') {
                messageBox.textContent = message;
                messageBox.className = 'message-box ' + type; // Add type for styling (e.g., 'success', 'error', 'info')
                messageBox.style.display = 'block';
            }

            // Function to hide messages
            function hideMessage() {
                messageBox.style.display = 'none';
                messageBox.textContent = '';
                messageBox.className = 'message-box';
            }

            // Function to disable/enable form elements
            function setFormEnabled(enabled) {
                newPasswordInput.disabled = !enabled;
                confirmPasswordInput.disabled = !enabled;
                resetPasswordBtn.disabled = !enabled;
                togglePasswordVisibilityBtn.disabled = !enabled; // Disable/enable the eye button
                rememberMeCheckbox.disabled = !enabled; // Disable/enable remember me checkbox
            }

            // Event listener for the single password toggle button
            togglePasswordVisibilityBtn.addEventListener('click', () => {
                const isPassword = newPasswordInput.type === 'password';
                newPasswordInput.type = isPassword ? 'text' : 'password';
                confirmPasswordInput.type = isPassword ? 'text' : 'password';

                const icon = togglePasswordVisibilityBtn.querySelector('i');
                if (isPassword) {
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                } else {
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                }
            });

            // Function to handle password reset submission
            async function handleResetPassword() {
                hideMessage(); // Clear previous messages

                const newPassword = newPasswordInput.value;
                const confirmPassword = confirmPasswordInput.value;

                if (!newPassword || !confirmPassword) {
                    showMessage('Please fill in both password fields.', 'error');
                    return;
                }

                if (newPassword !== confirmPassword) {
                    showMessage('Passwords do not match.', 'error');
                    return;
                }

                if (newPassword.length < 6) { // Supabase default minimum password length
                    showMessage('Password must be at least 6 characters long.', 'error');
                    return;
                }

                // Show loading indicator
                resetPasswordBtn.innerHTML = '<div class="loading-dots"><span></span><span></span><span></span></div>';
                setFormEnabled(false); // Disable form during submission

                try {
                    // Call the backend API to reset the password
                    const response = await fetch('/login.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            action: 'verify_password_reset_token', // Correct action for custom token verification
                            email: email,
                            token: token,
                            new_password: newPassword,
                            remember_me: rememberMeCheckbox.checked // Send remember me state
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        showMessage(result.message, 'success');
                        // Redirect to login page after a short delay
                        setTimeout(() => {
                            window.location.href = '/login?message=' + encodeURIComponent('Password reset successfully. Please log in.');
                        }, 3000);
                    } else {
                        showMessage(result.message, 'error');
                        setFormEnabled(true); // Re-enable form on failure (unless token expired)
                    }
                } catch (error) {
                    console.error('Error during password reset:', error);
                    showMessage(`Network error during password reset: ${error.message}. Please try again.`, 'error');
                    setFormEnabled(true); // Re-enable form on network error
                } finally {
                    // Reset button text
                    resetPasswordBtn.innerHTML = 'RESET PASSWORD';
                }
            }

            // Event listener for the reset password button
            resetPasswordBtn.addEventListener('click', handleResetPassword);

            // On page load, extract token and email from URL and verify
            const urlParams = new URLSearchParams(window.location.search);
            token = urlParams.get('token');
            email = urlParams.get('email');

            // Initial state: disable form until token is verified
            setFormEnabled(false);
            emailDisplay.textContent = 'Verifying reset link...';

            if (!token || !email) {
                showMessage('Invalid or missing password reset link. Please request a new one.', 'error');
                emailDisplay.textContent = 'Invalid link.';
                // Optionally redirect to login page after a delay
                setTimeout(() => {
                    window.location.href = '/login';
                }, 3000);
                return; // Stop execution if link is bad
            }

            // The backend will verify the token when the user submits the new password.
            // For initial load, we just display the email and enable the form.
            emailDisplay.innerHTML = `Enter a new password for <span style="font-weight: bold; color: var(--text-dark-elements);">${email}</span>.`;
            setFormEnabled(true); // Enable form if token and email are present in URL
            hideMessage(); // Clear "Verifying..." message
        });
    </script>
</body>
</html>
