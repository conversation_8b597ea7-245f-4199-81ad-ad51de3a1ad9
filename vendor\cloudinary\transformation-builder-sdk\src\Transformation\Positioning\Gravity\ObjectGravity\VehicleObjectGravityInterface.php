<?php
/**
 * This file is part of the Cloudinary PHP package.
 *
 * (c) Cloudinary
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Cloudinary\Transformation;

/**
 * Interface VehicleObjectGravityInterface
 *
 * @api
 */
interface VehicleObjectGravityInterface
{
    //Vehicle Category
    public const VEHICLE = 'vehicle';
    public const BICYCLE = 'bicycle';
    public const CAR     = 'car';
    public const MOTORBIKE = 'motorbike';
    public const AEROPLANE = 'aeroplane';
    public const BUS       = 'bus';
    public const TRAIN = 'train';
    public const TRUCK = 'truck';
    public const BOAT  = 'boat';
}
