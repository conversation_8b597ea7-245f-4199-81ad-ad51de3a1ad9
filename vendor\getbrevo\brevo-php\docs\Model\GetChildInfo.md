# GetChildInfo

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**credits** | [**\Brevo\Client\Model\GetChildInfoCredits**](GetChildInfoCredits.md) |  | [optional] 
**statistics** | [**\Brevo\Client\Model\GetChildInfoStatistics**](GetChildInfoStatistics.md) |  | [optional] 
**password** | **string** | The encrypted password of child account | 
**ips** | **string[]** | IP(s) associated to a child account user | [optional] 
**apiKeys** | [**\Brevo\Client\Model\GetChildInfoApiKeys**](GetChildInfoApiKeys.md) |  | [optional] 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


