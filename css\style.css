
        /* General Reset and Variables */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Montserrat', sans-serif; /* Changed font to Montserrat */
            /* Disable default blueish highlight on click/tap */
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none; /* Disable callout on long press */
            -webkit-user-select: none;   /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* Remove outline on focus for all elements */
        *:focus {
            outline: none;
        }

        /* --- START: iOS PWA optimized styles --- */
        html {
            /* Prevent elastic scroll without breaking layout */
            touch-action: manipulation;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            /* Hide scrollbar for Firefox */
            scrollbar-width: none;
            /* Hide scrollbar for Edge/IE */
            -ms-overflow-style: none;
            /* Prevent elastic scroll */
            overscroll-behavior: none;
        }

        /* Hide scrollbar for Webkit browsers (Chrome, Safari) */
        html::-webkit-scrollbar {
            display: none;
        }

        body {
            background-color: var(--body-bg);
            color: var(--text-color);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-bottom: 80px;

            /* Prevent touch gestures and zoom */
            touch-action: manipulation;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-tap-highlight-color: transparent;

            /* Prevent elastic scroll on iOS */
            overscroll-behavior: none;
            -webkit-overflow-scrolling: touch;
        }
        /* --- END: iOS PWA optimized styles --- */

        :root {
            /* Site's Base Color Palette (Updated) */
            --primary-accent: #ff5018;
            --secondary-accent: #FF7F50;
            --neutral-base: #F7F7F7;
            --text-dark-elements: #333333;
            --subtle-detail: #A7A7A7;
            --pure-white: #FFFFFF;
            --shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            --transition: all 0.5s ease; /* Increased transition duration */

            /* Light Theme Colors (Default) */
            --body-bg: var(--neutral-base);
            --text-color: var(--text-dark-elements);
            --card-bg: var(--pure-white);
            --header-bg: var(--primary-accent);
            --header-text: var(--pure-white);

            /* Navigation specific colors for LIGHT theme */
            --nav-bg: var(--pure-white); /* Light background for nav */
            --nav-item-inactive-color: var(--text-dark-elements); /* Darker text for inactive on light bg */
            --nav-item-active-bg: var(--primary-accent); /* Primary color for active background */
            --nav-item-active-color: var(--pure-white); /* White icon for active on primary bg */
            --nav-add-button-bg: var(--primary-accent); /* Primary color for the add button */
            --nav-add-button-color: var(--pure-white); /* White icon for the add button */
            --nav-shadow-color: rgba(0, 0, 0, 0.15); /* Slightly darker shadow for light nav */

            /* Dark Theme Colors */
            --dark-body-bg: #1a1a1a; /* Dark background */
            --dark-text-color: #f0f0f0; /* Light text */
            --dark-card-bg: #2a2a2a; /* Slightly lighter dark for cards */
            --dark-header-bg: #ff5018; /* Keep primary accent for header */
            --dark-header-text: #ffffff; /* White text for header */

            /* Navigation specific colors for DARK theme */
            --dark-nav-bg: #2a2a2a; /* Dark background for nav */
            --dark-nav-item-inactive-color: #cccccc; /* Lighter text for inactive on dark bg */
            --dark-nav-item-active-bg: var(--primary-accent); /* Primary color for active background */
            --dark-nav-item-active-color: var(--pure-white); /* White icon for active on primary bg */
            --dark-nav-add-button-bg: var(--primary-accent); /* Primary color for the add button */
            --dark-nav-add-button-color: var(--pure-white); /* White icon for the add button */
            --dark-nav-shadow-color: rgba(255, 255, 255, 0.1); /* Lighter shadow for dark nav */

            /* --- Font Sizes (using rem for scalability) --- */
            --font-size-base: 1rem; /* 16px - Base for p and general text */
            --font-size-small: 0.75rem; /* 12px - For subtle details, review counts */
            --font-size-medium: 0.9rem; /* 14.4px - For category names, coupon tags */
            --font-size-large: 1.0rem; /* 16px - For food item names, prices */
            --font-size-x-large: 1.1rem; /* 17.6px - For restaurant names, h4 */
            --font-size-xx-large: 1.3rem; /* 20.8px - For h2 */
            --font-size-greetings: 1.8rem; /* 28.8px - For header-greeting */
            --font-size-heading: 2.2rem; /* 35.2px - For a main page title if needed */
        }

        /* Media query for dark mode preference */
        @media (prefers-color-scheme: dark) {
            :root {
                --body-bg: var(--dark-body-bg);
                --text-color: var(--dark-text-color);
                --card-bg: var(--dark-card-bg);
                --header-bg: var(--dark-header-bg);
                --header-text: var(--dark-header-text);

                --nav-bg: var(--dark-nav-bg);
                --nav-item-inactive-color: var(--dark-nav-item-inactive-color);
                --nav-item-active-color: var(--dark-nav-item-active-color);
                --nav-item-active-bg: var(--dark-nav-item-active-bg);
                --nav-add-button-bg: var(--dark-nav-add-button-bg);
                --nav-add-button-color: var(--dark-nav-add-button-color);
                --nav-shadow-color: var(--dark-nav-shadow-color);
            }
        }

        /* App Header */
        .app-header {
            width: 100%;
            height: 305px; /* Set initial height to 280px */
            max-height: 305px;
            background-color: var(--primary-accent);
            background-image: url("data:image/svg+xml,%3Csvg width='200' height='200' viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='foodPattern' x='0' y='0' width='200' height='200' patternUnits='userSpaceOnUse'%3E%3Cg fill='none' stroke='white' stroke-width='1.5' opacity='0.2'%3E%3C!-- Tomato/Round Item --%3E%3Ccircle cx='40' cy='40' r='18'/%3E%3Cpath d='M40 22 L40 58 M22 40 L58 40'/%3E%3C!-- Chili Pepper 1 --%3E%3Cpath d='M160 30 Q175 15, 185 30 T190 50 T180 70 T160 60 Z'/%3E%3C!-- Cucumber Slice --%3E%3Ccircle cx='140' cy='150' r='15'/%3E%3Cpath d='M140 135 L140 165 M125 150 L155 150'/%3E%3C!-- Leaf --%3E%3Cpath d='M60 110 Q70 100, 80 110 T90 120 T80 130 T70 120 T60 110 Z'/%3E%3C!-- Chili Pepper 2 (rotated) --%3E%3Cg transform='translate(20, 100) rotate(90)'%3E%3Cpath d='M160 30 Q175 15, 185 30 T190 50 T180 70 T160 60 Z'/%3E%3C/g%3E%3C/g%3E%3C/pattern%3E%3C/defs%3E%3Crect x='0' y='0' width='100%25' height='100%25' fill='url(%23foodPattern)'/%3E%3C/svg%3E");
            color: var(--header-text);
            display: flex;
            flex-direction: column; /* Changed to column for top and bottom sections */
            justify-content: center;
            align-items: center;
            box-shadow: var(--shadow);
            border-bottom-left-radius: 50px; /* Changed from 40px to 50px */
            border-bottom-right-radius: 50px; /* Changed from 40px to 50px */
            position: sticky;
            top: 0;
            z-index: 100;
            /* Added slide-down animation */
            animation: slide-down 0.7s ease-out forwards;
            /* Unified transition for all transformable properties, REMOVED HEIGHT */
            transition: padding 0.3s ease, border-radius 0.3s ease, background-color 0.3s ease, height 0.3s ease, transform 0.3s ease, opacity 0.3s ease; /* Added transform, opacity */
            overflow: hidden; /* Ensure content is clipped during height transition */
        }

        /* Keyframes for slide-down animation */
        @keyframes slide-down {
            0% {
                transform: translateY(-100%);
                opacity: 0;
            }
            100% {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* Styles for the smaller sticky header - STAGE 3 (Final) */
        .app-header.header-scrolled {
            border-bottom-left-radius: 25px; /* Changed from 15px to 25px */
            border-bottom-right-radius: 25px; /* Changed from 15px to 25px */
        }

        /* New style for the MIKO logo */
        .app-logo {
            background-color: var(--pure-white);
            color: var(--primary-accent);
            padding: 0.3rem 0.8rem;
            border-radius: 20px; /* Pill shape */
            font-size: var(--font-size-medium); /* Applied variable */
            font-weight: 600;
            margin-bottom: 0.5rem; /* Space between logo and top row */
            box-shadow: var(--shadow);
            z-index: 101; /* Ensure it's above other elements if needed */
        }

        .header-top-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            padding: 0 1rem; /* Simplified padding to 0 1rem */
            transition: padding 0.3s ease; /* Smooth transition for padding */
        }

        .header-profile-pic {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: #ccc; /* Placeholder background */
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .header-profile-pic img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .header-location {
            display: flex;
            flex-direction: column; /* Main container for location info, stacked vertically */
            align-items: center; /* Center items horizontally within this column */
            color: var(--pure-white);
            cursor: pointer;
        }

        .header-location-top-row {
            display: flex; /* New row for "Delivery location" and arrow */
            align-items: center; /* Vertically center items in this row */
            gap: 4px; /* Small gap between text and arrow */
        }

        .header-location-label {
            font-size: var(--font-size-medium); /* Applied variable */
            opacity: 0.8;
            font-weight: 400; /* Lighter weight */
        }

        .down-arrow-icon-small-wrapper {
            display: flex; /* Ensure SVG is centered within its small div */
            align-items: center;
            justify-content: center;
        }

        .header-location-name {
            font-weight: 600; /* Bolder for the actual location */
            font-size: var(--font-size-medium); /* Applied variable */
            margin-top: 2px; /* Small space between label row and location name */
        }

        /* Container for the right-side icons */
        .header-right-icons {
            position: relative; /* Establish positioning context for children */
            width: 40px; /* Fixed width and height for the container */
            height: 40px;
            display: flex; /* Use flex to center the absolute children */
            justify-content: center;
            align-items: center;
        }

        /* Notification Icon, Login Icon, and Logout Icon Styling */
        .header-login-icon,
        .header-logout-icon,
        .header-notification-icon,
        .header-search-icon-small {
            position: absolute; /* Position both icons absolutely within their parent */
            top: 0;
            left: 0;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--pure-white);
            box-shadow: var(--shadow);
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--text-dark-elements); /* Ensures the icon color is dark */
            font-size: var(--font-size-large); /* Applied variable */
            cursor: pointer;
            transition: opacity 0.3s ease, transform 0.3s ease; /* Explicitly added transition */
            /* Initial state for notification icon */
            opacity: 1;
            transform: scale(1);
            pointer-events: auto;
            z-index: 10; /* Ensure notification is on top initially */
        }

        .header-login-icon,
        .header-logout-icon {
            position: relative; /* Override absolute positioning for login/logout */
            margin-right: 10px; /* Add space between login/logout and notification */
        }

        .header-login-icon:hover,
        .header-logout-icon:hover {
            transform: scale(1.1);
        }

        .header-notification-icon svg,
        .header-login-icon svg,
        .header-logout-icon svg {
            width: 24px;
            height: 24px;
        }

        /* Small Search Icon Styling */
        .header-search-icon-small {
            /* Initial state for search icon */
            opacity: 0;
            transform: scale(0.8); /* Start smaller */
            pointer-events: none; /* Disable clicks when hidden */
            z-index: 5; /* Ensure search is behind notification initially */
        }

        .header-search-icon-small svg {
            width: 24px;
            height: 24px;
            fill: var(--subtle-detail); /* Matches the search bar icon color */
        }

        .header-bottom-row {
            width: 100%;
            text-align: left;
            padding: 0 1rem 0.5rem 1rem;
            transition: opacity 0.3s ease, max-height 0.3s ease, padding 0.3s ease, margin 0.3s ease; /* Faster transition */
            max-height: 200px; /* Initial max-height to allow transition */
        }

        .header-greeting {
            font-size: var(--font-size-greetings); /* Applied variable */
            font-weight: 700;
            color: var(--pure-white);
            margin-bottom: 1rem; /* Re-added margin */
            margin-top: 1.5rem; /* Added margin-top */
            transition: opacity 0.3s ease, transform 0.3s ease, margin 0.3s ease, font-size 0.3s ease; /* Added transform */
        }

        .header-search-bar {
            background-color: var(--pure-white);
            border-radius: 100px; /* Changed to 100px for full roundness */
            display: flex;
            align-items: center;
            padding: 0.8rem 1rem; /* This applies 0.8rem padding to top and bottom, and 1rem to left and right */
            box-shadow: var(--shadow);
            width: 100%;
            transition: opacity 0.3s ease, transform 0.3s ease, padding 0.3s ease; /* Added transform */
        }

        .header-search-bar svg { /* Style for the new SVG icon */
            width: 24px;
            height: 24px;
            margin-right: 0.8rem;
            fill: var(--subtle-detail); /* Use subtle-detail for fill color */
        }

        .header-search-bar input {
            border: none;
            flex-grow: 1;
            font-size: var(--font-size-large); /* Applied variable */
            color: var(--text-dark-elements);
            background: transparent;

            /* Prevent iOS Safari auto-scroll on focus */
            transform: translateZ(0);
            -webkit-transform: translateZ(0);
            -webkit-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
            touch-action: manipulation;
        }

        .header-search-bar input::placeholder {
            color: var(--subtle-detail);
        }

        .header-filter-icon {
            color: var(--subtle-detail);
            font-size: var(--font-size-large); /* Applied variable */
            margin-left: 0.8rem;
            cursor: pointer;
        }

        .header-title {
            font-size: var(--font-size-x-large); /* Applied variable (could be h4 or similar) */
            font-weight: 600;
        }

        /* Main Content */
        .main-content {
            flex-grow: 1;
            width: 100%;
            max-width: 800px;
            padding: 0;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start; /* Align content to the top */
            /* Removed initial opacity and transform, as skeleton will be visible */
        }

        /* Section Titles */
        .categories-section h2,
        .food-items-section h2,
        .offers-section h2,
        .restaurants-section h2 { /* Added restaurants section title */
            font-size: var(--font-size-xx-large); /* Applied variable */
            font-weight: 600;
            color: var(--text-color);
            /* Removed margin-bottom and padding-left, now handled by .section-header-container */
            margin-top: -20px;
            text-align: left; /* Default alignment for most titles */
        }

        /* Specific style for the #SpecialForYou heading */
        .special-for-you-section h2 {
            font-size: var(--font-size-xx-large); /* Applied variable */
            font-weight: 600;
            color: var(--text-color);
            /* Removed margin-bottom and padding-left, now handled by .section-header-container */
            padding-left: 0; /* Remove left padding */
            text-align: center; /* Center the text */
        }

        /* Categories Section */
        .categories-section {
            width: 100%;
            text-align: left;
            margin-top: 1.5rem; /* Space from header */
        }

        /* Skeleton Loading Styles */
        .skeleton-container {
            display: flex;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            white-space: nowrap;
            padding: 0 .5rem .5rem .5rem;
            gap: 0.75rem; /* Adjust as needed for specific grids */
            align-items: center;
            /* Hide scrollbar for Firefox */
            scrollbar-width: none;
            /* Hide scrollbar for Edge/IE */
            -ms-overflow-style: none;
        }

        /* Hide scrollbar for Webkit browsers */
        .skeleton-container::-webkit-scrollbar {
            display: none;
        }

        .skeleton-placeholder {
            background-color: #e0e0e0; /* Light grey for placeholders */
            border-radius: 4px;
            animation: shimmer 1.5s infinite linear;
            background: linear-gradient(to right, #e0e0e0 8%, #f0f0f0 18%, #e0e0e0 33%);
            background-size: 800px 104px; /* Larger background to animate across */
            position: relative;
            overflow: hidden; /* Ensure shimmer doesn't go outside */
        }

        @keyframes shimmer {
            0% {
                background-position: -468px 0;
            }
            100% {
                background-position: 468px 0;
            }
        }

        /* New styles for heading skeletons */
        .section-header-container {
            width: 100%;
            padding: 0 12px; /* Match section padding */
            text-align: left; /* Align skeleton to the left like the heading */
            margin-bottom: 11px; /* Space below heading/skeleton */
        }

        .heading-skeleton {
            height: var(--font-size-xx-large); /* Approximate height of h2 */
            width: 180px; /* Approximate width of a typical heading */
            margin-bottom: 0; /* Handled by parent container */
            border-radius: 4px;
            display: block; /* Ensure it takes up space */
        }

        /* Specific skeleton styles for categories */
        .skeleton-pill {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background-color: var(--card-bg); /* Use card background for the pill itself */
            border-radius: 25px;
            padding: 0.6rem 1.2rem;
            box-shadow: var(--shadow);
            flex-shrink: 0;
            gap: 0.4rem; /* Space between circle and text */
            width: 120px; /* Example width for a pill */
            height: 40px; /* Example height for a pill */
        }

        .skeleton-circle {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #e0e0e0; /* Grey for the circle */
            animation: shimmer 1.5s infinite linear;
            background: linear-gradient(to right, #e0e0e0 8%, #f0f0f0 18%, #e0e0e0 33%);
            background-size: 800px 104px;
        }

        .skeleton-text {
            height: 12px;
            background-color: #e0e0e0; /* Grey for text lines */
            border-radius: 2px;
            animation: shimmer 1.5s infinite linear;
            background: linear-gradient(to right, #e0e0e0 8%, #f0f0f0 18%, #e0e0e0 33%);
            background-size: 800px 104px;
        }

        .skeleton-text.short {
            width: 40px;
        }

        .skeleton-text.medium {
            width: 70px;
        }

        .skeleton-text.long {
            width: 90px;
        }

        /* Specific skeleton styles for food item cards and restaurant cards (similar) */
        .skeleton-food-card {
            background-color: var(--card-bg);
            border-radius: 15px;
            box-shadow: var(--shadow);
            overflow: hidden;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-top: 1rem;
            flex-shrink: 0;
            width: 200px; /* Adjusted width for a larger appearance */
            min-height: 280px; /* Adjusted minimum height to ensure it's taller */
            gap: 0.5rem; /* Space between skeleton elements inside card */
        }

        /* New skeleton for rectangular restaurant cards */
        .skeleton-restaurant-card {
            background-color: var(--card-bg);
            border-radius: 15px;
            box-shadow: var(--shadow);
            overflow: hidden;
            display: flex;
            flex-direction: column; /* Changed to column for skeleton as well */
            align-items: center; /* Center items horizontally */
            padding: 0.8rem; /* Padding for the entire card */
            width: 100%; /* Full width */
            /* Removed fixed height for skeleton, let it adjust */
            margin-bottom: 1rem; /* Space between cards */
            flex-shrink: 0;
            gap: 0.8rem; /* Space between image and text */
        }

        .skeleton-restaurant-card .skeleton-card-image {
            width: 100%; /* Full width for image */
            height: 200px; /* Fixed height for image (increased) */
            border-radius: 10px;
            flex-shrink: 0; /* Prevent image from shrinking */
            margin-bottom: 0.8rem; /* Add margin below image */
        }

        .skeleton-restaurant-card .skeleton-card-text-container {
            display: flex;
            flex-direction: column;
            flex-grow: 1; /* Allow text container to grow */
            gap: 0.4rem; /* Space between text lines */
            width: 100%; /* Ensure text container takes full width */
        }

        .skeleton-restaurant-card .skeleton-card-text {
            width: 80%; /* Adjust width for text lines */
            align-self: flex-start; /* Align text skeletons to the left */
        }

        .skeleton-restaurant-card .skeleton-text.short {
            width: 50px; /* Adjust for delivery time/price */
        }


        .skeleton-card-image {
            width: 90%;
            height: 150px; /* Increased height for images */
            background-color: #e0e0e0;
            border-radius: 10px;
            margin-bottom: 0.5rem;
            animation: shimmer 1.5s infinite linear;
            background: linear-gradient(to right, #e0e0e0 8%, #f0f0f0 18%, #e0e0e0 33%);
            background-size: 800px 104px;
        }

        .skeleton-food-card .skeleton-card-text {
            margin: 0 0.8rem; /* Horizontal margin to match padding */
            width: calc(100% - 1.6rem); /* Adjust width based on parent padding */
        }

        /* Specific skeleton styles for offer banners */
        .skeleton-offer-banner {
            flex-shrink: 0;
            width: 300px;
            height: 150px;
            border-radius: 15px;
            background-color: #e0e0e0;
            box-shadow: var(--shadow);
            animation: shimmer 1.5s infinite linear;
            background: linear-gradient(to right, #e0e0e0 8%, #f0f0f0 18%, #e0e0e0 33%);
            background-size: 800px 104px;
        }

        /* Hide actual content initially using opacity and pointer-events */
        .actual-content-hidden {
            opacity: 0;
            pointer-events: none; /* Disable clicks when hidden */
        }

        /* Show actual content when loaded using opacity and pointer-events */
        .actual-content-visible {
            opacity: 1;
            pointer-events: auto;
        }

        /* Add transition to the grids for smooth fading */
        .categories-grid,
        .food-items-grid,
        .offers-grid,
        .restaurants-grid { /* Added restaurants-grid */
            transition: opacity 0.3s ease-in-out;
        }

        /* Ensure skeletons are hidden with display: none when not needed */
        .skeleton-container.hidden {
            display: none;
        }

        /* Categories Grid */
        .categories-grid {
            display: flex; /* Changed to flex for horizontal layout */
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            white-space: nowrap;
            padding: 0 .5rem .5rem .5rem;
            gap: 0.75rem; /* Adjust as needed for specific grids */
            align-items: center;
            /* Hide scrollbar for Firefox */
            scrollbar-width: none;
            /* Hide scrollbar for Edge/IE */
            -ms-overflow-style: none;
        }

        /* Custom Scrollbar Styles for Webkit (Chrome, Safari, Edge) */
        .categories-grid::-webkit-scrollbar {
            display: none; /* Hide scrollbar for Webkit browsers */
        }

        .food-items-grid::-webkit-scrollbar,
        .offers-grid::-webkit-scrollbar,
        .restaurants-grid::-webkit-scrollbar { /* Added for restaurants-grid */
            /* Hide scrollbar for Firefox */
            scrollbar-width: none;
            /* Hide scrollbar for Edge/IE */
            -ms-overflow-style: none;
        }

        .food-items-grid::-webkit-scrollbar,
        .offers-grid::-webkit-scrollbar,
        .restaurants-grid::-webkit-scrollbar { /* Added for restaurants-grid */
            display: none; /* Hide scrollbar for Webkit browsers */
        }

        .categories-grid::-webkit-scrollbar-track,
        .food-items-grid::-webkit-scrollbar-track,
        .offers-grid::-webkit-scrollbar-track,
        .restaurants-grid::-webkit-scrollbar-track { /* Added for restaurants-grid */
            background: transparent; /* Transparent track */
            border-radius: 10px;
        }

        .categories-grid::-webkit-scrollbar-thumb,
        .food-items-grid::-webkit-scrollbar-thumb,
        .offers-grid::-webkit-scrollbar-thumb,
        .restaurants-grid::-webkit-scrollbar-thumb { /* Added for restaurants-grid */
            background-color: var(--primary-accent); /* Orange thumb */
            border-radius: 10px;
            border: 2px solid transparent; /* Transparent border to make thumb thinner if needed */
        }

        .category-item {
            display: inline-flex; /* Use inline-flex for horizontal alignment of emoji and name */
            align-items: center;
            justify-content: center;
            background-color: var(--card-bg);
            border: 1px solid transparent; /* Added transparent border for smooth transition */
            border-radius: 25px; /* Reverted to slightly larger pill shape for better appearance */
            padding: 0.6rem 1.2rem; /* Adjusted padding for wider pill shape */
            text-decoration: none;
            color: var(--text-color);
            transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, transform 0.3s ease-out; /* Added transform to transition */
            flex-shrink: 0; /* Prevent shrinking */
            position: relative; /* Needed for transform */
            height: 40px;
        }

        .category-item:hover {
            background-color: var(--card-bg); /* Keep background as it is */
            color: var(--text-color); /* Keep text color as it is */
            border: 1px solid var(--primary-accent); /* Show orange outline on hover */
        }

        .category-item.active-category {
            background-color: var(--primary-accent); /* Fills with primary accent color */
            color: var(--pure-white); /* Ensures active category text is always white */
            font-weight: 600;
            border-color: var(--primary-accent); /* Ensure border is also primary accent when active */
        }

        .category-item .emoji {
            font-size: var(--font-size-medium); /* Applied variable */
            margin-right: 0.4rem; /* Slightly more space between emoji and name */
            margin-bottom: 0; /* Remove bottom margin */
            line-height: 1;
        }

        .category-item .name {
            font-size: var(--font-size-medium); /* Applied variable */
            font-weight: 500;
            white-space: nowrap; /* Prevent text wrapping */
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Food Items Section */
        .food-items-section {
            width: 100%;
            text-align: left;
            margin-top: 1.5rem; /* Space from categories section */
        }

        .food-items-grid {
            display: flex;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            white-space: nowrap;
            padding: 0 1rem 0.5rem 1rem;
            gap: 1rem;
            align-items: flex-start;
        }

        .food-item-card {
            background-color:#8b8b8b0f; /* Changed to transparent */
            border-radius: 15px;

            overflow: hidden;
            text-decoration: none;
            color: var(--text-color);
            display: flex;
            flex-direction: column;
            align-items: center;
            transition: transform 0.2s ease-in-out;
            position: relative; /* For the discount tag positioning */
            flex-shrink: 0; /* Prevent shrinking in horizontal layout */
            width: 200px; /* Increased width for a larger appearance */
            min-height: 280px; /* Increased minimum height to ensure it's taller */
        }

        /* New discount tag styles */
        .discount-tag {
            position: absolute;
            top: 15px; /* Adjusted to match the image */
            left: 0;
            background: var(--primary-accent); /* Use primary accent color */
            color: var(--pure-white);
            font-size: var(--font-size-small); /* Applied variable */
            font-weight: 700;
            padding: 0.2rem 0.6rem; /* Adjusted padding */
            box-shadow: -1px 2px 3px rgba(0, 0, 0, 0.3); /* Match ribbon shadow */
            display: flex; /* Added flex to align content */
            align-items: center; /* Center items vertically */
            gap: 0.3rem; /* Space between icon and text */
            z-index: 10; /* Ensure it's above the image */
        }

        .discount-tag:before,
        .discount-tag:after {
            content: "";
            position: absolute;
        }

        .discount-tag:before {
            width: 7px;
            height: 100%;
            top: 0;
            left: -6.5px;
            padding: 0 0 7px;
            background: inherit; /* Inherit background from parent (.discount-tag) */
            border-radius: 5px 0 0 5px;
        }

        .discount-tag:after {
            width: 5px;
            height: 5px;
            bottom: -5px;
            left: -4.5px;
            background: var(--secondary-accent); /* Use secondary accent for the small corner */
            border-radius: 55px 0 0 5px;
        }

        .discount-tag svg {
            width: 16px; /* Adjust SVG size within the tag */
            height: 16px;
            fill: currentColor; /* Ensure SVG color matches text color */
        }

        /* New container for image and details to ensure full card coverage and better layout */
        .food-item-content {
            display: flex;
            flex-direction: column;
            width: 100%;
            height: 100%; /* Ensure it covers the whole card */
            padding-bottom: 0.8rem; /* Padding for the bottom of the content area */
        }

        .food-item-image {
            width: 90%; /* Adjusted width */
            height: 170px; /* Fixed height for images */
            object-fit: contain; /* Changed to contain to show full image */
            border-radius: 10px; /* Added border-radius to the image */
            margin-bottom: 4px; /* Space below image */
            align-self: center; /* Center the image within the card */
            transition: opacity 0.3s ease; /* Smooth transition for lazy loading */
        }

        /* Lazy loading styles */
        .lazy-load {
            opacity: 0.7; /* Slightly transparent while loading */
            background-color: #f0f0f0; /* Light background while loading */
        }

        .lazy-load:not([src*="data:image"]) {
            opacity: 1; /* Full opacity when loaded */
        }

        /* Favorite icon wrapper - NO CIRCLE */
        .food-item-favorite-wrapper {
            width: 24px; /* Adjust to match SVG size */
            height: 24px; /* Adjust to match SVG size */
            display: flex;
            justify-content: center;
            align-items: center;
            /* Removed border-radius, background-color, box-shadow */
            flex-shrink: 0; /* Prevent shrinking when placed in flex container */
            margin-left: auto; /* Push to the right within the price row */
            cursor: pointer; /* Indicate it's clickable */
        }

        .food-item-favorite-wrapper svg { /* Style for the new SVG icon */
            width: 100%; /* Make SVG fill the wrapper */
            height: 100%;
            /* Fill color is now handled by the SVG itself or the .liked class */
            transition: fill 0.2s ease-in-out; /* Transition for fill color */
        }

        .food-item-favorite-wrapper.liked svg path {
            /* fill: #ff0a0a; Removed as SVG now has internal fill */
            stroke: none; /* Remove stroke when filled */
        }
        /* Ensure default icon stroke is visible */
        .food-item-favorite-wrapper:not(.liked) svg path {
            stroke: #808080; /* Grey stroke for unliked state */
            fill: none; /* No fill for unliked state */
        }


        .food-item-details {
            padding: 18.8px 6px 5px 6px;/* Adjusted padding for better horizontal spacing */
            width: 100%;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            flex-grow: 1; /* Allow details to grow and fill available space */
            justify-content: space-between; /* Distribute content vertically */
        }

        .food-item-name-wrapper {
            display: flex;
            justify-content: space-between;
            align-items: flex-start; /* Align items to the top */
            width: 100%;
            margin-bottom: 0.4rem; /* Space below name wrapper */
        }

        .food-item-name {
            font-size: var(--font-size-large); /* Applied variable */
            font-weight: 700; /* Bolder font weight */
            /* Removed white-space, overflow, text-overflow to allow wrapping */
            color: var(--text-color); /* Use theme text color */
            flex-grow: 1; /* Allow name to take available space */
            padding-right: 0.5rem; /* Space between name and heart */
        }

        .food-item-reviews-wrapper {
            display: flex;
            align-items: center;
            font-size: var(--font-size-small); /* Applied variable */
            color: var(--subtle-detail);
            margin-bottom: 0.4rem; /* Space below reviews */
            width: 100%; /* Ensure it takes full width */
        }

        /* Style for the new SVG star icon */
        .food-item-reviews-wrapper .star-icon-svg {
            width: 16px; /* Adjust size as needed */
            height: 16px;
            margin-right: 0.3rem;
            flex-shrink: 0; /* Prevent shrinking */
        }

        .food-item-reviews-wrapper .star-icon-svg path {
            fill: #ffc038; /* Ensure the fill color is yellow/gold */
            stroke: #e2ac18; /* Ensure the stroke color is also yellow/gold */
        }

        .food-item-price-row {
            display: flex;
            justify-content: space-between; /* Distribute space between price and icon */
            align-items: center; /* Align vertically in the middle */
            width: 100%;
            margin-top: auto; /* Push price row to the bottom of the details section */
        }

        .food-item-price-section {
            display: flex; /* Use flex to align original and current price */
            align-items: baseline; /* Align prices at their baseline */
            gap: 0.5rem; /* Space between original and current price */
        }

        .food-item-price {
            font-size: var(--font-size-large); /* Applied variable */
            font-weight: 700; /* Bolder price */
            color: var(--primary-accent); /* Changed to primary accent (orange) */
            white-space: nowrap; /* Prevent wrapping */
            overflow: hidden; /* Hide overflow */
            text-overflow: ellipsis; /* Add ellipsis if text overflows */
            /* Removed margin-right to fix spacing, now handled by gap in .food-item-price-section */
        }

        .food-item-original-price {
            font-size: var(--font-size-small); /* Applied variable */
            color: var(--subtle-detail);
            text-decoration: line-through;
            white-space: nowrap; /* Prevent wrapping */
            overflow: hidden; /* Hide overflow */
            text-overflow: ellipsis; /* Add ellipsis if text overflows */
            /* Removed margin-right to fix spacing, now handled by gap in .food-item-price-section */
        }

        /* Offers Section */
        .offers-section {
            width: 100%;
            text-align: left;
            margin-top: 1.5rem; /* Space from previous section */
        }

        .offers-grid {
            display: flex;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            white-space: nowrap;
            padding: 0 1rem 1rem 1rem;
            gap: 1rem;
            align-items: flex-start;
            /* Hide scrollbar for Firefox */
            scrollbar-width: none;
            /* Hide scrollbar for Edge/IE */
            -ms-overflow-style: none;
        }

        .offers-grid::-webkit-scrollbar {
            display: none; /* Hide scrollbar for Webkit browsers */
        }

        /* Styles for the actual offer banners */
        .offer-banner {
            position: relative; /* Needed for absolute positioning of overlay */
            flex-shrink: 0;
            width: 385px;
            height: 170px;
            border-radius: 15px;
            box-shadow: var(--shadow);
            overflow: hidden; /* Ensures image and overlay stay within bounds */
            text-decoration: none; /* Remove underline from link */
            color: var(--pure-white); /* Default text color for overlay content */
        }

        .offer-banner img {
            width: 100%;
            height: 100%;
            object-fit: cover; /* Cover the entire banner area */
            border-radius: 15px; /* Match banner border-radius */
            transition: transform 0.3s ease; /* Smooth hover effect */
        }

        .offer-banner:hover img {
            transform: scale(1.05); /* Slight zoom on hover */
        }

        .offer-banner .overlay {
            position: absolute;
            top: 10px;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: flex-end; /* Align content to the bottom */
            align-items: flex-start; /* Align content to the left */
            padding: 1rem;
            border-radius: 15px; /* Match banner border-radius */
        }

        .offer-banner .discount-text {
            font-size: var(--font-size-xx-large); /* Applied variable */
            font-weight: 700;
            color: var(--pure-white);
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
            margin-bottom: 0.2rem;
        }

        .offer-banner .code-text {
            font-size: var(--font-size-medium); /* Applied variable */
            font-weight: 500;
            color: var(--pure-white);
            opacity: 0.8;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
        }

        /* New styles for Restaurants Section */
        .restaurants-section {
            width: 100%;
            text-align: left;
            margin-top: 1.5rem; /* Space from previous section */
            margin-bottom: 1.5rem; /* Space before nav bar */
        }

        /* Modified restaurants-grid for single column layout */
        .restaurants-grid {
            display: block; /* Stack items vertically */
            padding: 0 1rem; /* Consistent padding with other sections */
            /* Removed overflow-x, white-space, gap as they are not needed for block layout */
        }

        /* Modified restaurant-card for column layout */
        .restaurant-card {
            background-color: var(--card-bg); /* Use card background */
            border-radius: 15px;
            box-shadow: var(--shadow);
            overflow: hidden;
            text-decoration: none;
            color: var(--text-color);
            display: flex; /* Use flex for column layout of image and details */
            flex-direction: column; /* Image on top, details below */
            align-items: flex-start; /* Align items to the left within the card */
            transition: transform 0.2s ease-in-out;
            width: 100%; /* Full width of the container */
            /* Removed fixed height for rectangular card, let it adjust based on content */
            margin-bottom: 1rem; /* Space between cards */
            padding: 0; /* Remove padding from here, individual sections will have it */
            position: relative; /* For heart icon and coupon tag positioning */
        }

        .restaurant-card:last-child {
            margin-bottom: 0; /* No margin for the last card */
        }

        /* New wrapper for the image to allow absolute positioning of the heart icon */
        .restaurant-image-wrapper {
            position: relative;
            width: 100%;
            height: 200px; /* Increased height for the image */
            overflow: hidden;
            border-radius: 15px 15px 0 0; /* Rounded top corners, sharp bottom corners */
        }

        /* Modified restaurant-image for column layout */
        .restaurant-image {
            width: 100%; /* Full width for the image */
            height: 100%; /* Fill the wrapper height */
            object-fit: cover; /* Cover the area, cropping if necessary */
            border-radius: 15px 15px 0 0; /* Match wrapper border-radius */
            flex-shrink: 0; /* Prevent shrinking */
            transition: opacity 0.3s ease; /* Smooth transition for lazy loading */
        }

        /* New style for the heart icon on restaurant cards */
        .restaurant-favorite-wrapper {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 40px; /* Larger tap target */
            height: 40px;
            background-color: var(--pure-white);
            border-radius: 50%;
            box-shadow: var(--shadow);
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            z-index: 10; /* Ensure it's above the image */
        }

        .restaurant-favorite-wrapper svg {
            width: 24px; /* Icon size */
            height: 24px;
            transition: fill 0.2s ease-in-out, stroke 0.2s ease-in-out;
        }

        .restaurant-favorite-wrapper.liked svg path {
            fill: #ff0000; /* Red fill when liked */
            stroke: none; /* Remove stroke when filled */
        }

        .restaurant-favorite-wrapper:not(.liked) svg path {
            fill: none; /* No fill when not liked */
            stroke: #808080; /* Grey stroke when not liked */
        }

        /* Modified restaurant-details for column layout */
        .restaurant-details {
            display: flex;
            flex-direction: column;
            justify-content: space-between; /* Distribute content vertically */
            flex-grow: 1; /* Allow details to take up remaining space */
            align-items: flex-start; /* Align text to the left */
            padding: 0.8rem 1rem 0.6rem 1rem; /* Padding inside the details section */
            width: 100%; /* Ensure details take full width */
        }

        /* New container for name, rating, and delivery info */
        .restaurant-details-main {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            margin-bottom: 0.5rem; /* Space before coupon tag */
        }

        /* New row for name and rating */
        .restaurant-name-rating-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            margin-bottom: 0.2rem; /* Small space between name/rating and delivery info */
        }

        .restaurant-name {
            font-size: var(--font-size-x-large); /* Applied variable */
            font-weight: 700;
            color: var(--text-color);
            flex-grow: 1;
            white-space: normal; /* Allow text to wrap */
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2; /* Limit to 2 lines */
            line-clamp: 2; /* Standard property for compatibility */
            -webkit-box-orient: vertical;
            padding-right: 0.5rem; /* Space between name and rating */
        }

        .restaurant-rating {
            display: flex;
            align-items: center;
            font-size: var(--font-size-medium); /* Applied variable */
            color: var(--subtle-details); /* Darker text for rating */
            font-weight: 600; /* Bolder rating */
            flex-shrink: 0; /* Prevent shrinking */
            gap: 0.2rem; /* Smaller gap between star and number */
        }

        /* Style for the new SVG star icon in restaurant cards */
        .restaurant-rating .star-icon-svg {
            width: 16px; /* Adjust size as needed */
            height: 16px;
            margin-right: 0.3rem;
            flex-shrink: 0; /* Prevent shrinking */
        }

        .restaurant-rating .star-icon-svg path {
            fill: #ffc038; /* Ensure the fill color is yellow/gold */
            stroke: #e2ac18; /* Ensure the stroke color is also yellow/gold */
        }

        .restaurant-rating .review-count {
            font-size: var(--font-size-small); /* Applied variable */
            font-weight: 400; /* Lighter weight */
            color: var(--subtle-detail);
            margin-left: 0.2rem; /* Space between rating and count */
        }

        /* New row for delivery time and cuisine type */
        .restaurant-time-cuisine-row {
            display: flex;
            align-items: center;
            width: 100%;
            font-size: var(--font-size-medium); /* Applied variable */
            color: var(--subtle-detail);
            font-weight: 500;
            gap: 0.4rem; /* Small gap between elements */
            margin-bottom: 0.2rem; /* Small space below this row */
        }

        /* New row for distance and delivery charges */
        .restaurant-distance-delivery-row {
            display: flex;
            align-items: center;
            width: 100%;
            font-size: var(--font-size-medium); /* Applied variable */
            color: var(--subtle-detail);
            font-weight: 500;
            gap: 0.4rem; /* Small gap between elements */
            margin-bottom: 0.5rem; /* Space below this row and before separator */
        }

        .restaurant-delivery-time,
        .restaurant-distance-info,
        .restaurant-delivery-charges,
        .restaurant-cuisine-type {
            display: flex;
            align-items: center;
            white-space: nowrap;
            flex-shrink: 0;
            gap: 0.3rem; /* Space between icon and text */
        }

        .restaurant-delivery-time .time-icon-svg,
        .restaurant-distance-info .distance-icon-svg,
        .restaurant-delivery-charges .delivery-icon-svg {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
            fill: var(--subtle-detail);
        }

        /* Style for dynamic text elements */
        .distance-text,
        .time-text,
        .delivery-text {
            font-size: var(--font-size-medium);
            color: var(--subtle-detail);
        }

        /* Separator styles */
        .dot-separator {
            color: var(--subtle-detail);
            font-size: var(--font-size-medium); /* Applied variable */
            margin: 0 0.2rem;
            flex-shrink: 0;
        }
        .dash-separator {
            color: var(--subtle-detail);
            font-size: var(--font-size-medium); /* Applied variable */
            margin: 0 0.2rem;
            flex-shrink: 0;
        }

        /* Updated restaurant-info-separator for dashed line */
        .restaurant-info-separator {
            width: 100%;
            border-top: 1px dashed #e0e0e0; /* Changed to dashed border */
            margin: 0.5rem 0; /* Space above and below separator */
        }

        /* New container for coupon tags */
        .restaurant-offers-container {
            display: flex;
            flex-wrap: wrap; /* Allow items to wrap to the next line */
            gap: 0.5rem; /* Space between individual coupon tags */
            width: 100%;
        }

        /* New coupon tag for restaurant cards */
        .restaurant-coupon-tag {
            background-color: #ffe5e5; /* Light pink background */
            color: #ff0000; /* Red text */
            font-size: var(--font-size-medium); /* Applied variable */
            font-weight: 600;
            padding: 0.4rem 0.8rem;
            border-radius: 50px; /* Changed to 50px for pill shape */
            display: flex;
            align-items: center;
            gap: 0.4rem;
            /* Removed margin-top and margin-bottom as gap on parent handles it */
            align-self: flex-start; /* Align to the left */
            flex-shrink: 0; /* Prevent shrinking */
        }
        /* No need for last-child margin adjustment on individual tags due to gap */


        .restaurant-coupon-tag svg {
            width: 18px;
            height: 18px;
            fill: #ff0000; /* Red fill for icon */
        }


        /* New Floating Bottom Navigation Bar */
        .nav {
            position: fixed;
            bottom: 20px; /* Distance from the bottom */
            left: 50%;
            transform: translateX(-50%);
            width: 90%;
            background-color: var(--nav-bg); /* Uses theme variable */
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 5px 0; /* Reduced vertical padding */
            border-radius: 50px; /* 100% radius for rounded ends */
            /* Adjusted glowing border: reduced blur and spread */
            box-shadow: 0 4px 15px var(--nav-shadow-color), 0 0 3px 0.5px var(--primary-accent);
            z-index: 1000;
            overflow: hidden; /* Hide items outside the initial narrow width */
            height: 80px; /* Fixed height for consistent centering of items */
            /* Added slide-up animation */
            animation: slide-up 0.7s ease-out forwards;
            animation-delay: 0.2s; /* Delay the animation slightly after header */
            opacity: 0; /* Start hidden for animation */
        }

        /* Keyframes for slide-up animation */
        @keyframes slide-up {
            0% {
                transform: translateX(-50%) translateY(100%);
                opacity: 0;
            }
            100% {
                transform: translateX(-50%) translateY(0);
                opacity: 1;
            }
        }

        .nav__list {
            list-style: none;
            display: flex;
            justify-content: space-around; /* Distribute items */
            align-items: center;
            width: 100%;
            height: 100%; /* Fill parent nav height */
            padding: 0;
            margin: 0;
        }

        .nav__link {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: var(--nav-item-inactive-color); /* Uses theme variable */
            padding: 10px;
            border-radius: 50%; /* Make individual items circular */
            cursor: pointer;
            width: 50px; /* Fixed width for each item when expanded */
            height: 50px;
            flex-shrink: 0; /* Prevent shrinking */
            flex-grow: 0; /* Prevent growing initially */
            transition: color 0.3s ease, background-color 0.3s ease, width 0.3s ease, flex-grow 0.3s ease; /* Add width/flex-grow to transition */
        }

        .nav__link i {
            font-size: 1.6rem; /* Larger icon size */
            margin-bottom: 0;
        }

        .nav__link.active-link {
            background-color: var(--nav-item-active-bg); /* Uses theme variable */
            color: var(--nav-item-active-color); /* Uses theme variable */
        }

        /* Hover effect for inactive links: make them look like active links */
        .nav__link:not(.active-link):hover {
            background-color: var(--nav-item-active-bg);
            color: var(--nav-item-active-color);
        }

        .nav__link.active-link:hover {
            /* Keep active state on hover for active links, or add subtle effect */
            background-color: var(--nav-item-active-bg);
            color: var(--nav-item-active-color);
            transform: scale(1.05); /* Subtle scale for active item hover */
        }

        /* Special style for the central QR button */
        .nav__qr-button {
            background-color: var(--nav-add-button-bg); /* Uses theme variable */
            color: var(--nav-add-button-color); /* Uses theme variable */
            border-radius: 50%;
            width: 75px; /* Increased size */
            height: 75px; /* Increased size */
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2); /* Initial shadow */
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease; /* Add box-shadow to transition */
            margin-bottom: 0; /* Removed bottom margin */
            flex-shrink: 0; /* Prevent shrinking */
            flex-grow: 0; /* Prevent growing */
            animation: glowing-border 2s infinite ease-in-out; /* Apply the new animation */
        }

        .nav__qr-button svg { /* Style for the SVG inside the button */
            width: 60%; /* Adjust SVG size relative to button */
            height: 60%;
            object-fit: contain;
        }

        /* Keyframes for continuous glowing border animation */
        @keyframes glowing-border {
            0% {
                transform: scale(1);
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2),
                            0 0 0 0px var(--primary-accent); /* No initial glow */
            }
            50% {
                transform: scale(1.02); /* Slightly reduced scale up */
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25),
                            0 0 8px 3px var(--primary-accent); /* Reduced blur and spread */
            }
            100% {
                transform: scale(1);
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2),
                            0 0 0 0px var(--primary-accent); /* Glow shrinks back */
            }
        }

        /* Remove the animation from active-link as it's now applied directly to nav__qr-button */
        .nav__qr-button.active-link {
            /* No scale transformation, the glow is the main active indicator */
            transform: scale(1); /* Ensure no unintended scaling from other rules */
            /* Base shadow for the button, plus the initial state of the glow */
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2); /* Keep a base shadow, animation handles glow */
            /* Animation is now on .nav__qr-button */
        }

        /* Remove hover effect for the QR button */
        .nav__qr-button:hover {
            /* Keep its original colors on hover */
            background-color: var(--nav-add-button-bg);
            color: var(--nav-add-button-color);
            /* Do not reset transform or animation on hover if already active */
        }

        /* Styling for SVG icons in nav links */
        .nav__link svg {
            width: 1.6rem; /* Match font icon size */
            height: 1.6rem;
            fill: currentColor; /* Ensure SVG fill color changes with text color */
            margin-bottom: 0;
        }

        /* Styling for the new card image icon */
        .nav__link .card-icon-img {
            width: 1.6rem; /* Match other icon sizes */
            height: 1.6rem;
            object-fit: contain;
        }