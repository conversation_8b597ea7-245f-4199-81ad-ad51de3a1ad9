# GetAggregatedReport

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**range** | **string** | Time frame of the report | [optional] 
**requests** | **int** | Number of requests for the timeframe | [optional] 
**delivered** | **int** | Number of delivered emails for the timeframe | [optional] 
**hardBounces** | **int** | Number of hardbounces for the timeframe | [optional] 
**softBounces** | **int** | Number of softbounces for the timeframe | [optional] 
**clicks** | **int** | Number of clicks for the timeframe | [optional] 
**uniqueClicks** | **int** | Number of unique clicks for the timeframe | [optional] 
**opens** | **int** | Number of openings for the timeframe | [optional] 
**uniqueOpens** | **int** | Number of unique openings for the timeframe | [optional] 
**spamReports** | **int** | Number of complaint (spam report) for the timeframe | [optional] 
**blocked** | **int** | Number of blocked contact emails for the timeframe | [optional] 
**invalid** | **int** | Number of invalid emails for the timeframe | [optional] 
**unsubscribed** | **int** | Number of unsubscribed emails for the timeframe | [optional] 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


