# GetUserPermission

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**email** | **string** | Email address of the user. | 
**status** | **string** | Status of the invited user. | 
**privileges** | [**\Brevo\Client\Model\GetUserPermissionPrivileges[]**](GetUserPermissionPrivileges.md) | Granular feature permissions given to the user. | 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


