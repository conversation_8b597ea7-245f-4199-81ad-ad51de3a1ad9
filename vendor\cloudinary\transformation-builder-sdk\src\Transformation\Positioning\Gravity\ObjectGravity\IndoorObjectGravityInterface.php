<?php
/**
 * This file is part of the Cloudinary PHP package.
 *
 * (c) Cloudinary
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Cloudinary\Transformation;

/**
 * Interface IndoorObjectGravityInterface
 *
 * @api
 */
interface IndoorObjectGravityInterface
{
    //Indoor Category
    public const INDOOR = 'indoor';
    public const BOOK   = 'book';
    public const CLOCK = 'clock';
    public const VASE  = 'vase';
    public const SCISSORS = 'scissors';
    public const TEDDY_BEAR = 'teddy_bear';
    public const HAIR_DRIER = 'hair_drier';
    public const TOOTHBRUSH = 'toothbrush';
}
