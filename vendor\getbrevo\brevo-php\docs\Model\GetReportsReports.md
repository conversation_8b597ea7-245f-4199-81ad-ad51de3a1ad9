# GetReportsReports

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**date** | [**\DateTime**] | Date of the statistics | 
**requests** | **int** | Number of requests for the date | 
**delivered** | **int** | Number of delivered emails for the date | 
**hardBounces** | **int** | Number of hardbounces for the date | 
**softBounces** | **int** | Number of softbounces for the date | 
**clicks** | **int** | Number of clicks for the date | 
**uniqueClicks** | **int** | Number of unique clicks for the date | 
**opens** | **int** | Number of openings for the date | 
**uniqueOpens** | **int** | Number of unique openings for the date | 
**spamReports** | **int** | Number of complaints (spam reports) for the date | 
**blocked** | **int** | Number of blocked emails for the date | 
**invalid** | **int** | Number of invalid emails for the date | 
**unsubscribed** | **int** | Number of unsubscribed emails for the date | 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


