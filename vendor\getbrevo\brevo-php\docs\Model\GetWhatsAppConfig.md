# GetWhatsAppConfig

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**whatsappBusinessAccountId** | **string** | Id of the WhatsApp business account | [optional] 
**sendingLimit** | **string** | Sending limit Information of the WhatsApp API account | [optional] 
**phoneNumberQuality** | **string** | Quality status of phone number associated with WhatsApp account. There are three quality ratings. example - **High (GREEN) , Medium (YELLOW) and Low(RED)** | [optional] 
**whatsappBusinessAccountStatus** | **string** | Status information related to WhatsApp Api account | [optional] 
**businessStatus** | **string** | Verification status information of the Business account | [optional] 
**phoneNumberNameStatus** | **string** | Status of the name associated with WhatsApp Phone number | [optional] 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


