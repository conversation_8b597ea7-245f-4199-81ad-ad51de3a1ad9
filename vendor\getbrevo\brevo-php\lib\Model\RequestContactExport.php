<?php
/**
 * RequestContactExport
 *
 * PHP version 5
 *
 * @category Class
 * @package  Brevo\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * Brevo API
 *
 * Brevo provide a RESTFul API that can be used with any languages. With this API, you will be able to :   - Manage your campaigns and get the statistics   - Manage your contacts   - Send transactional Emails and SMS   - and much more...  You can download our wrappers at https://github.com/orgs/brevo  **Possible responses**   | Code | Message |   | :-------------: | ------------- |   | 200  | OK. Successful Request  |   | 201  | OK. Successful Creation |   | 202  | OK. Request accepted |   | 204  | OK. Successful Update/Deletion  |   | 400  | Error. Bad Request  |   | 401  | Error. Authentication Needed  |   | 402  | Error. Not enough credit, plan upgrade needed  |   | 403  | Error. Permission denied  |   | 404  | Error. Object does not exist |   | 405  | Error. Method not allowed  |   | 406  | Error. Not Acceptable  |   | 422  | Error. Unprocessable Entity |
 *
 * OpenAPI spec version: 3.0.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 3.0.68
 */
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Brevo\Client\Model;

use \ArrayAccess;
use \Brevo\Client\ObjectSerializer;

/**
 * RequestContactExport Class Doc Comment
 *
 * @category Class
 * @package  Brevo\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class RequestContactExport implements ModelInterface, ArrayAccess
{
    const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $swaggerModelName = 'requestContactExport';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerTypes = [
        'exportAttributes' => 'string[]',
        'customContactFilter' => '\Brevo\Client\Model\RequestContactExportCustomContactFilter',
        'notifyUrl' => 'string',
        'disableNotification' => 'bool',
        'exportMandatoryAttributes' => 'bool',
        'exportSubscriptionStatus' => 'string[]',
        'exportMetadata' => 'string[]'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerFormats = [
        'exportAttributes' => null,
        'customContactFilter' => null,
        'notifyUrl' => 'url',
        'disableNotification' => null,
        'exportMandatoryAttributes' => null,
        'exportSubscriptionStatus' => null,
        'exportMetadata' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerTypes()
    {
        return self::$swaggerTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerFormats()
    {
        return self::$swaggerFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'exportAttributes' => 'exportAttributes',
        'customContactFilter' => 'customContactFilter',
        'notifyUrl' => 'notifyUrl',
        'disableNotification' => 'disableNotification',
        'exportMandatoryAttributes' => 'exportMandatoryAttributes',
        'exportSubscriptionStatus' => 'exportSubscriptionStatus',
        'exportMetadata' => 'exportMetadata'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'exportAttributes' => 'setExportAttributes',
        'customContactFilter' => 'setCustomContactFilter',
        'notifyUrl' => 'setNotifyUrl',
        'disableNotification' => 'setDisableNotification',
        'exportMandatoryAttributes' => 'setExportMandatoryAttributes',
        'exportSubscriptionStatus' => 'setExportSubscriptionStatus',
        'exportMetadata' => 'setExportMetadata'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'exportAttributes' => 'getExportAttributes',
        'customContactFilter' => 'getCustomContactFilter',
        'notifyUrl' => 'getNotifyUrl',
        'disableNotification' => 'getDisableNotification',
        'exportMandatoryAttributes' => 'getExportMandatoryAttributes',
        'exportSubscriptionStatus' => 'getExportSubscriptionStatus',
        'exportMetadata' => 'getExportMetadata'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$swaggerModelName;
    }



    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['exportAttributes'] = isset($data['exportAttributes']) ? $data['exportAttributes'] : null;
        $this->container['customContactFilter'] = isset($data['customContactFilter']) ? $data['customContactFilter'] : null;
        $this->container['notifyUrl'] = isset($data['notifyUrl']) ? $data['notifyUrl'] : null;
        $this->container['disableNotification'] = isset($data['disableNotification']) ? $data['disableNotification'] : false;
        $this->container['exportMandatoryAttributes'] = isset($data['exportMandatoryAttributes']) ? $data['exportMandatoryAttributes'] : true;
        $this->container['exportSubscriptionStatus'] = isset($data['exportSubscriptionStatus']) ? $data['exportSubscriptionStatus'] : null;
        $this->container['exportMetadata'] = isset($data['exportMetadata']) ? $data['exportMetadata'] : null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if ($this->container['customContactFilter'] === null) {
            $invalidProperties[] = "'customContactFilter' can't be null";
        }
        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets exportAttributes
     *
     * @return string[]
     */
    public function getExportAttributes()
    {
        return $this->container['exportAttributes'];
    }

    /**
     * Sets exportAttributes
     *
     * @param string[] $exportAttributes List of all the attributes that you want to export. These attributes must be present in your contact database. It is required if exportMandatoryAttributes is set false. For example, ['fname', 'lname', 'email'].
     *
     * @return $this
     */
    public function setExportAttributes($exportAttributes)
    {
        $this->container['exportAttributes'] = $exportAttributes;

        return $this;
    }

    /**
     * Gets customContactFilter
     *
     * @return \Brevo\Client\Model\RequestContactExportCustomContactFilter
     */
    public function getCustomContactFilter()
    {
        return $this->container['customContactFilter'];
    }

    /**
     * Sets customContactFilter
     *
     * @param \Brevo\Client\Model\RequestContactExportCustomContactFilter $customContactFilter customContactFilter
     *
     * @return $this
     */
    public function setCustomContactFilter($customContactFilter)
    {
        $this->container['customContactFilter'] = $customContactFilter;

        return $this;
    }

    /**
     * Gets notifyUrl
     *
     * @return string
     */
    public function getNotifyUrl()
    {
        return $this->container['notifyUrl'];
    }

    /**
     * Sets notifyUrl
     *
     * @param string $notifyUrl Webhook that will be called once the export process is finished. For reference, https://help.brevo.com/hc/en-us/articles/360007666479
     *
     * @return $this
     */
    public function setNotifyUrl($notifyUrl)
    {
        $this->container['notifyUrl'] = $notifyUrl;

        return $this;
    }

    /**
     * Gets disableNotification
     *
     * @return bool
     */
    public function getDisableNotification()
    {
        return $this->container['disableNotification'];
    }

    /**
     * Sets disableNotification
     *
     * @param bool $disableNotification To avoid generating the email notification upon contact export, pass **true**
     *
     * @return $this
     */
    public function setDisableNotification($disableNotification)
    {
        $this->container['disableNotification'] = $disableNotification;

        return $this;
    }

    /**
     * Gets exportMandatoryAttributes
     *
     * @return bool
     */
    public function getExportMandatoryAttributes()
    {
        return $this->container['exportMandatoryAttributes'];
    }

    /**
     * Sets exportMandatoryAttributes
     *
     * @param bool $exportMandatoryAttributes To export mandatory attributes like EMAIL, ADDED_TIME, MODIFIED_TIME
     *
     * @return $this
     */
    public function setExportMandatoryAttributes($exportMandatoryAttributes)
    {
        $this->container['exportMandatoryAttributes'] = $exportMandatoryAttributes;

        return $this;
    }

    /**
     * Gets exportSubscriptionStatus
     *
     * @return string[]
     */
    public function getExportSubscriptionStatus()
    {
        return $this->container['exportSubscriptionStatus'];
    }

    /**
     * Sets exportSubscriptionStatus
     *
     * @param string[] $exportSubscriptionStatus Export subscription status of contacts for email & sms marketting. Pass email_marketing to obtain the marketing email subscription status & sms_marketing to retrieve the marketing SMS status of the contact.
     *
     * @return $this
     */
    public function setExportSubscriptionStatus($exportSubscriptionStatus)
    {
        $this->container['exportSubscriptionStatus'] = $exportSubscriptionStatus;

        return $this;
    }

    /**
     * Gets exportMetadata
     *
     * @return string[]
     */
    public function getExportMetadata()
    {
        return $this->container['exportMetadata'];
    }

    /**
     * Sets exportMetadata
     *
     * @param string[] $exportMetadata Export metadata of contacts such as _listIds, ADDED_TIME, MODIFIED_TIME.
     *
     * @return $this
     */
    public function setExportMetadata($exportMetadata)
    {
        $this->container['exportMetadata'] = $exportMetadata;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    #[\ReturnTypeWillChange]
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }

    /**
     * Sets value based on offset.
     *
     * @param integer $offset Offset
     * @param mixed   $value  Value to be set
     *
     * @return void
     */
    #[\ReturnTypeWillChange]
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    #[\ReturnTypeWillChange]
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) { // use JSON pretty print
            return json_encode(
                ObjectSerializer::sanitizeForSerialization($this),
                JSON_PRETTY_PRINT
            );
        }

        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}
