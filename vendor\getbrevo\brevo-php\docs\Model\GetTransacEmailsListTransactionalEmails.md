# GetTransacEmailsListTransactionalEmails

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**email** | **string** | Email address to which transactional email has been sent | 
**subject** | **string** | Subject of the sent email | 
**templateId** | **int** | Id of the template | [optional] 
**messageId** | **string** | Message Id of the sent email | 
**uuid** | **string** | Unique id of the email sent to a particular contact | 
**date** | **string** | Date on which transactional email was sent | 
**from** | **string** | Email address of the sender from which the email was sent | [optional] 
**tags** | **string[]** | Tags used for your email | [optional] 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


