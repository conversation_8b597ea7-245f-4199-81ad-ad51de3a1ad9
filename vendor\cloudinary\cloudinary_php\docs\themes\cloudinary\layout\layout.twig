{% extends "layout/base.twig" %}

{% block content %}
    <div id="content">
        <div id="left-column">
            {{ block('control_panel') }}
            {{ block('leftnav') }}
        </div>
        <div id="right-column">
            {{ block('menu') }}
            {% block below_menu '' %}
            <div id="page-content">
                {% block page_content '' %}
            </div>
            {{ block('footer') }}
        </div>
    </div>
{% endblock %}

{% block menu %}
    <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="{{ path('index.html') }}">{{ project.config('title') }} v{{ project.versions[0] }}</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="{{ path('classes.html') }}">Classes</a></li>
                    {% if has_namespaces %}
                        <li><a href="{{ path('namespaces.html') }}">Namespaces</a></li>
                    {% endif %}
                    <!--li><a href="{{ path('interfaces.html') }}">Interfaces</a></li--CLD_MOD-->
                    <!--li><a href="{{ path('traits.html') }}">Traits</a></li--CLD_MOD-->
                    <li><a href="{{ path('doc-index.html') }}">Index</a></li>
                    <li><a href="{{ path('search.html') }}">Search</a></li>
                    <li><a href="/documentation/sdks/php/php-transformation-builder/index.html">PHP Transformation Reference</a></li>
                    <li><a href="/documentation/php2_integration" target="_blank">SDK User Guide</a></li>
                </ul>
            </div>
        </div>
    </nav>
{% endblock %}

{% block leftnav %}
    <div id="api-tree"></div>
{% endblock %}

{% block control_panel %}
    <div id="control-panel">
        {% if project.versions|length > 1 %}
            <form action="#" method="GET">
                <select id="version-switcher" name="version">
                    {% for version in project.versions %}
                        <option value="{{ path('../' ~ version ~ '/index.html') }}" data-version="{{ version }}">{{ version.longname }}</option>
                    {% endfor %}
                </select>
            </form>
        {% endif %}
        <script>
            $('option[data-version="'+window.projectVersion+'"]').prop('selected', true);
        </script>
        <form id="search-form" action="{{ path('search.html') }}" method="GET">
            <span class="glyphicon glyphicon-search"></span>
            <input name="search"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search">
        </form>
    </div>
{% endblock %}

{% block footer %}
    <div id="footer">
        © <script>document.write(new Date().getFullYear())</script> Cloudinary. All rights reserved.<br>
        Generated by <a href="http://sami.sensiolabs.org/">Sami, the API Documentation Generator</a>.<br>
        {{ project.config('title') }} version {{ project.versions[0] }}
    </div>
{% endblock %}
