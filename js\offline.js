const retryBtn = document.getElementById('retryBtn');
const contactUsBtn = document.getElementById('contactUsBtn');

function updateConnectionStatusLogic() {
    if (navigator.onLine) {
        if (window.location.pathname === '/offline.html' && !retryBtn.disabled) {
            retryBtn.click();
        }
    }
}

retryBtn.addEventListener('click', () => {
    const originalHTML = retryBtn.innerHTML;
    retryBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking Now...';
    retryBtn.disabled = true;

    if (navigator.onLine) {
        setTimeout(() => {
            window.location.href = '/home.html';
        }, 500);
    } else {
        setTimeout(() => {
            retryBtn.innerHTML = originalHTML;
            retryBtn.disabled = false;
        }, 1000);
    }
});

contactUsBtn.addEventListener('click', () => {
    console.log('Contact Us button clicked. No message box enabled.');
});

window.addEventListener('online', updateConnectionStatusLogic);
window.addEventListener('offline', updateConnectionStatusLogic);

window.addEventListener('load', () => {
    setTimeout(() => {
        if (!retryBtn.disabled) {
            retryBtn.click();
        }
    }, 2000);
});
