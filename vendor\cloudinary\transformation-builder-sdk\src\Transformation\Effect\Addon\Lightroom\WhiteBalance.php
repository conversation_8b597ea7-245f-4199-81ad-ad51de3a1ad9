<?php
/**
 * This file is part of the Cloudinary PHP package.
 *
 * (c) Cloudinary
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Cloudinary\Transformation;

/**
 * Class WhiteBalance
 */
abstract class WhiteBalance
{
    public const AS_SHOT = 'as shot';
    public const AUTO = 'auto';
    public const CLOUDY  = 'cloudy';
    public const CUSTOM = 'custom';
    public const DAYLIGHT = 'daylight';
    public const FLASH    = 'flash';
    public const FLUORESCENT = 'fluorescent';
    public const SHADE       = 'shade';
    public const TUNGSTEN = 'tungsten';
}
