(async function() {
    const protectedPagesFile = '/protected_pages.txt';
    let protectedPages = [];

    try {
        const response = await fetch(protectedPagesFile);
        if (response.ok) {
            protectedPages = (await response.text())
                .split('\n')
                .map(line => line.trim())
                .filter(line => line && !line.startsWith('#'))
                .map(path => path.endsWith('/') && path.length > 1 ? path.slice(0, -1) : path);
        } else {
            console.error(`Failed to fetch protected pages list: ${response.statusText}`);
        }
    } catch (error) {
        console.error('Error fetching protected pages list:', error);
    }

    const currentPath = window.location.pathname.endsWith('/') && window.location.pathname.length > 1 ?
                        window.location.pathname.slice(0, -1) : window.location.pathname;

    if (protectedPages.includes(currentPath) && document.referrer === '') {
        console.log(`Direct access to ${currentPath} detected. Redirecting to index.html...`);
        window.location.replace('/index.html');
    }
})();
