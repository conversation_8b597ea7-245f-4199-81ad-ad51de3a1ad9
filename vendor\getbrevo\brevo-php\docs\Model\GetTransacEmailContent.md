# GetTransacEmailContent

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**email** | **string** | Email address to which transactional email has been sent | 
**subject** | **string** | Subject of the sent email | 
**templateId** | **int** | Id of the template | [optional] 
**date** | **string** | Date on which transactional email was sent | 
**events** | [**\Brevo\Client\Model\GetTransacEmailContentEvents[]**](GetTransacEmailContentEvents.md) | Series of events which occurred on the transactional email | 
**body** | **string** | Actual content of the transactional email that has been sent | 
**attachmentCount** | **int** | Count of the attachments that were sent in the email | 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


