        let isOnline = true;
        Object.defineProperty(navigator, 'onLine', {
            get: () => isOnline
        });

        window.addEventListener('load', () => {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/service-worker.js')
                    .then(registration => {
                        console.log('Service Worker registered with scope:', registration.scope);
                    })
                    .catch(error => {
                        console.error('Service Worker registration failed:', error);
                    });
            }
        const progressBar = document.getElementById('loadingProgressBar');
            let progress = 0;
            const intervalTime = 50;
            const totalLoadTime = 1500; 
            const increment = (100 / (totalLoadTime / intervalTime));

            const loadingInterval = setInterval(() => {
                progress += increment;
                progressBar.style.width = progress + '%';
                
                if (progress >= 100) {
                    clearInterval(loadingInterval);
                    
                    setTimeout(() => {
                        if (!navigator.onLine) {
                            window.location.href = '/offline.html'; 
                        } else {
                            window.location.href = '/home.html'; 
                        }
                    }, 300); 
                }
            }, intervalTime);
        });