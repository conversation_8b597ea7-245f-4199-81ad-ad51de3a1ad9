# GetWhatsappCampaignOverview

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | ID of the WhatsApp Campaign | 
**campaignName** | **string** | Name of the WhatsApp Campaign | 
**campaignStatus** | **string** | Status of the WhatsApp Campaign | 
**scheduledAt** | **string** | UTC date-time on which WhatsApp campaign is scheduled. Should be in YYYY-MM-DDTHH:mm:ss.SSSZ format | [optional] 
**senderNumber** | **string** | Sender of the WhatsApp Campaign | 
**stats** | [**\Brevo\Client\Model\WhatsappCampStats**](WhatsappCampStats.md) |  | [optional] 
**template** | [**\Brevo\Client\Model\WhatsappCampTemplate**](WhatsappCampTemplate.md) |  | 
**createdAt** | **string** | Creation UTC date-time of the WhatsApp campaign (YYYY-MM-DDTHH:mm:ss.SSSZ) | 
**modifiedAt** | **string** | UTC date-time of last modification of the WhatsApp campaign (YYYY-MM-DDTHH:mm:ss.SSSZ) | 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


