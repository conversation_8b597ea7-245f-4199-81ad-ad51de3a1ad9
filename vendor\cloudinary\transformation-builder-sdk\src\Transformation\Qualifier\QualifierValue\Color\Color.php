<?php
/**
 * This file is part of the Cloudinary PHP package.
 *
 * (c) Cloudinary
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Cloudinary\Transformation\Argument;

use Cloudinary\Transformation\BaseArgument;

/**
 *  Defines the available named colors.
 *
 * @api
 */
class Color extends BaseArgument
{
    public const SNOW = 'snow';
    public const SNOW1 = 'snow1';
    public const SNOW2 = 'snow2';
    public const ROSYBROWN1 = 'rosybrown1';
    public const ROSYBROWN2 = 'rosybrown2';
    public const SNOW3      = 'snow3';
    public const LIGHTCORAL = 'lightcoral';
    public const INDIANRED1 = 'indianred1';
    public const ROSYBROWN3 = 'rosybrown3';
    public const INDIANRED2 = 'indianred2';
    public const ROSYBROWN  = 'rosybrown';
    public const BROWN1    = 'brown1';
    public const FIREBRICK1 = 'firebrick1';
    public const BROWN2     = 'brown2';
    public const INDIANRED = 'indianred';
    public const INDIANRED3 = 'indianred3';
    public const FIREBRICK2 = 'firebrick2';
    public const SNOW4      = 'snow4';
    public const BROWN3 = 'brown3';
    public const RED    = 'red';
    public const RED1 = 'red1';
    public const ROSYBROWN4 = 'rosybrown4';
    public const FIREBRICK3 = 'firebrick3';
    public const RED2       = 'red2';
    public const FIREBRICK = 'firebrick';
    public const BROWN     = 'brown';
    public const RED3  = 'red3';
    public const INDIANRED4 = 'indianred4';
    public const BROWN4     = 'brown4';
    public const FIREBRICK4 = 'firebrick4';
    public const DARKRED    = 'darkred';
    public const RED4    = 'red4';
    public const LIGHTPINK1 = 'lightpink1';
    public const LIGHTPINK3 = 'lightpink3';
    public const LIGHTPINK4 = 'lightpink4';
    public const LIGHTPINK2 = 'lightpink2';
    public const LIGHTPINK  = 'lightpink';
    public const PINK      = 'pink';
    public const CRIMSON = 'crimson';
    public const PINK1   = 'pink1';
    public const PINK2 = 'pink2';
    public const PINK3 = 'pink3';
    public const PINK4 = 'pink4';
    public const PALEVIOLETRED4 = 'palevioletred4';
    public const PALEVIOLETRED  = 'palevioletred';
    public const PALEVIOLETRED2 = 'palevioletred2';
    public const PALEVIOLETRED1 = 'palevioletred1';
    public const PALEVIOLETRED3 = 'palevioletred3';
    public const LAVENDERBLUSH  = 'lavenderblush';
    public const LAVENDERBLUSH1 = 'lavenderblush1';
    public const LAVENDERBLUSH3 = 'lavenderblush3';
    public const LAVENDERBLUSH2 = 'lavenderblush2';
    public const LAVENDERBLUSH4 = 'lavenderblush4';
    public const MAROON         = 'maroon';
    public const HOTPINK3 = 'hotpink3';
    public const VIOLETRED3 = 'violetred3';
    public const VIOLETRED1 = 'violetred1';
    public const VIOLETRED2 = 'violetred2';
    public const VIOLETRED4 = 'violetred4';
    public const HOTPINK2   = 'hotpink2';
    public const HOTPINK1 = 'hotpink1';
    public const HOTPINK4 = 'hotpink4';
    public const HOTPINK  = 'hotpink';
    public const DEEPPINK = 'deeppink';
    public const DEEPPINK1 = 'deeppink1';
    public const DEEPPINK2 = 'deeppink2';
    public const DEEPPINK3 = 'deeppink3';
    public const DEEPPINK4 = 'deeppink4';
    public const MAROON1   = 'maroon1';
    public const MAROON2 = 'maroon2';
    public const MAROON3 = 'maroon3';
    public const MAROON4 = 'maroon4';
    public const MEDIUMVIOLETRED = 'mediumvioletred';
    public const VIOLETRED       = 'violetred';
    public const ORCHID2   = 'orchid2';
    public const ORCHID  = 'orchid';
    public const ORCHID1 = 'orchid1';
    public const ORCHID3 = 'orchid3';
    public const ORCHID4 = 'orchid4';
    public const THISTLE1 = 'thistle1';
    public const THISTLE2 = 'thistle2';
    public const PLUM1    = 'plum1';
    public const PLUM2 = 'plum2';
    public const THISTLE = 'thistle';
    public const THISTLE3 = 'thistle3';
    public const PLUM     = 'plum';
    public const VIOLET = 'violet';
    public const PLUM3  = 'plum3';
    public const THISTLE4 = 'thistle4';
    public const FUCHSIA  = 'fuchsia';
    public const MAGENTA = 'magenta';
    public const MAGENTA1 = 'magenta1';
    public const PLUM4    = 'plum4';
    public const MAGENTA2 = 'magenta2';
    public const MAGENTA3 = 'magenta3';
    public const DARKMAGENTA = 'darkmagenta';
    public const MAGENTA4    = 'magenta4';
    public const PURPLE   = 'purple';
    public const MEDIUMORCHID = 'mediumorchid';
    public const MEDIUMORCHID1 = 'mediumorchid1';
    public const MEDIUMORCHID2 = 'mediumorchid2';
    public const MEDIUMORCHID3 = 'mediumorchid3';
    public const MEDIUMORCHID4 = 'mediumorchid4';
    public const DARKVIOLET    = 'darkviolet';
    public const DARKORCHID = 'darkorchid';
    public const DARKORCHID1 = 'darkorchid1';
    public const DARKORCHID3 = 'darkorchid3';
    public const DARKORCHID2 = 'darkorchid2';
    public const DARKORCHID4 = 'darkorchid4';
    public const INDIGO      = 'indigo';
    public const BLUEVIOLET = 'blueviolet';
    public const PURPLE2    = 'purple2';
    public const PURPLE3 = 'purple3';
    public const PURPLE4 = 'purple4';
    public const PURPLE1 = 'purple1';
    public const MEDIUMPURPLE = 'mediumpurple';
    public const MEDIUMPURPLE1 = 'mediumpurple1';
    public const MEDIUMPURPLE2 = 'mediumpurple2';
    public const MEDIUMPURPLE3 = 'mediumpurple3';
    public const MEDIUMPURPLE4 = 'mediumpurple4';
    public const DARKSLATEBLUE = 'darkslateblue';
    public const LIGHTSLATEBLUE = 'lightslateblue';
    public const MEDIUMSLATEBLUE = 'mediumslateblue';
    public const SLATEBLUE       = 'slateblue';
    public const SLATEBLUE1 = 'slateblue1';
    public const SLATEBLUE2 = 'slateblue2';
    public const SLATEBLUE3 = 'slateblue3';
    public const SLATEBLUE4 = 'slateblue4';
    public const GHOSTWHITE = 'ghostwhite';
    public const LAVENDER   = 'lavender';
    public const BLUE     = 'blue';
    public const BLUE1 = 'blue1';
    public const BLUE2 = 'blue2';
    public const BLUE3 = 'blue3';
    public const MEDIUMBLUE = 'mediumblue';
    public const BLUE4      = 'blue4';
    public const DARKBLUE = 'darkblue';
    public const MIDNIGHTBLUE = 'midnightblue';
    public const NAVY         = 'navy';
    public const NAVYBLUE = 'navyblue';
    public const ROYALBLUE = 'royalblue';
    public const ROYALBLUE1 = 'royalblue1';
    public const ROYALBLUE2 = 'royalblue2';
    public const ROYALBLUE3 = 'royalblue3';
    public const ROYALBLUE4 = 'royalblue4';
    public const CORNFLOWERBLUE = 'cornflowerblue';
    public const LIGHTSTEELBLUE = 'lightsteelblue';
    public const LIGHTSTEELBLUE1 = 'lightsteelblue1';
    public const LIGHTSTEELBLUE2 = 'lightsteelblue2';
    public const LIGHTSTEELBLUE3 = 'lightsteelblue3';
    public const LIGHTSTEELBLUE4 = 'lightsteelblue4';
    public const SLATEGRAY4      = 'slategray4';
    public const SLATEGRAY1 = 'slategray1';
    public const SLATEGRAY2 = 'slategray2';
    public const SLATEGRAY3 = 'slategray3';
    public const LIGHTSLATEGRAY = 'lightslategray';
    public const LIGHTSLATEGREY = 'lightslategrey';
    public const SLATEGRAY      = 'slategray';
    public const SLATEGREY = 'slategrey';
    public const DODGERBLUE = 'dodgerblue';
    public const DODGERBLUE1 = 'dodgerblue1';
    public const DODGERBLUE2 = 'dodgerblue2';
    public const DODGERBLUE4 = 'dodgerblue4';
    public const DODGERBLUE3 = 'dodgerblue3';
    public const ALICEBLUE   = 'aliceblue';
    public const STEELBLUE4 = 'steelblue4';
    public const STEELBLUE  = 'steelblue';
    public const STEELBLUE1 = 'steelblue1';
    public const STEELBLUE2 = 'steelblue2';
    public const STEELBLUE3 = 'steelblue3';
    public const SKYBLUE4   = 'skyblue4';
    public const SKYBLUE1 = 'skyblue1';
    public const SKYBLUE2 = 'skyblue2';
    public const SKYBLUE3 = 'skyblue3';
    public const LIGHTSKYBLUE = 'lightskyblue';
    public const LIGHTSKYBLUE4 = 'lightskyblue4';
    public const LIGHTSKYBLUE1 = 'lightskyblue1';
    public const LIGHTSKYBLUE2 = 'lightskyblue2';
    public const LIGHTSKYBLUE3 = 'lightskyblue3';
    public const SKYBLUE       = 'skyblue';
    public const LIGHTBLUE3 = 'lightblue3';
    public const DEEPSKYBLUE = 'deepskyblue';
    public const DEEPSKYBLUE1 = 'deepskyblue1';
    public const DEEPSKYBLUE2 = 'deepskyblue2';
    public const DEEPSKYBLUE4 = 'deepskyblue4';
    public const DEEPSKYBLUE3 = 'deepskyblue3';
    public const LIGHTBLUE1   = 'lightblue1';
    public const LIGHTBLUE2 = 'lightblue2';
    public const LIGHTBLUE  = 'lightblue';
    public const LIGHTBLUE4 = 'lightblue4';
    public const POWDERBLUE = 'powderblue';
    public const CADETBLUE1 = 'cadetblue1';
    public const CADETBLUE2 = 'cadetblue2';
    public const CADETBLUE3 = 'cadetblue3';
    public const CADETBLUE4 = 'cadetblue4';
    public const TURQUOISE1 = 'turquoise1';
    public const TURQUOISE2 = 'turquoise2';
    public const TURQUOISE3 = 'turquoise3';
    public const TURQUOISE4 = 'turquoise4';
    public const CADETBLUE  = 'cadetblue';
    public const DARKTURQUOISE = 'darkturquoise';
    public const AZURE         = 'azure';
    public const AZURE1 = 'azure1';
    public const LIGHTCYAN1 = 'lightcyan1';
    public const LIGHTCYAN  = 'lightcyan';
    public const AZURE2    = 'azure2';
    public const LIGHTCYAN2 = 'lightcyan2';
    public const PALETURQUOISE1 = 'paleturquoise1';
    public const PALETURQUOISE  = 'paleturquoise';
    public const PALETURQUOISE2 = 'paleturquoise2';
    public const DARKSLATEGRAY1 = 'darkslategray1';
    public const AZURE3         = 'azure3';
    public const LIGHTCYAN3 = 'lightcyan3';
    public const DARKSLATEGRAY2 = 'darkslategray2';
    public const PALETURQUOISE3 = 'paleturquoise3';
    public const DARKSLATEGRAY3 = 'darkslategray3';
    public const AZURE4         = 'azure4';
    public const LIGHTCYAN4 = 'lightcyan4';
    public const AQUA       = 'aqua';
    public const CYAN = 'cyan';
    public const CYAN1 = 'cyan1';
    public const PALETURQUOISE4 = 'paleturquoise4';
    public const CYAN2          = 'cyan2';
    public const DARKSLATEGRAY4 = 'darkslategray4';
    public const CYAN3          = 'cyan3';
    public const CYAN4   = 'cyan4';
    public const DARKCYAN = 'darkcyan';
    public const TEAL     = 'teal';
    public const DARKSLATEGRAY = 'darkslategray';
    public const DARKSLATEGREY = 'darkslategrey';
    public const MEDIUMTURQUOISE = 'mediumturquoise';
    public const LIGHTSEAGREEN   = 'lightseagreen';
    public const TURQUOISE     = 'turquoise';
    public const AQUAMARINE4 = 'aquamarine4';
    public const AQUAMARINE  = 'aquamarine';
    public const AQUAMARINE1 = 'aquamarine1';
    public const AQUAMARINE2 = 'aquamarine2';
    public const AQUAMARINE3 = 'aquamarine3';
    public const MEDIUMAQUAMARINE = 'mediumaquamarine';
    public const MEDIUMSPRINGGREEN = 'mediumspringgreen';
    public const MINTCREAM         = 'mintcream';
    public const SPRINGGREEN = 'springgreen';
    public const SPRINGGREEN1 = 'springgreen1';
    public const SPRINGGREEN2 = 'springgreen2';
    public const SPRINGGREEN3 = 'springgreen3';
    public const SPRINGGREEN4 = 'springgreen4';
    public const MEDIUMSEAGREEN = 'mediumseagreen';
    public const SEAGREEN       = 'seagreen';
    public const SEAGREEN3 = 'seagreen3';
    public const SEAGREEN1 = 'seagreen1';
    public const SEAGREEN4 = 'seagreen4';
    public const SEAGREEN2 = 'seagreen2';
    public const MEDIUMFORESTGREEN = 'mediumforestgreen';
    public const HONEYDEW          = 'honeydew';
    public const HONEYDEW1  = 'honeydew1';
    public const HONEYDEW2 = 'honeydew2';
    public const DARKSEAGREEN1 = 'darkseagreen1';
    public const DARKSEAGREEN2 = 'darkseagreen2';
    public const PALEGREEN1    = 'palegreen1';
    public const PALEGREEN  = 'palegreen';
    public const HONEYDEW3 = 'honeydew3';
    public const LIGHTGREEN = 'lightgreen';
    public const PALEGREEN2 = 'palegreen2';
    public const DARKSEAGREEN3 = 'darkseagreen3';
    public const DARKSEAGREEN  = 'darkseagreen';
    public const PALEGREEN3   = 'palegreen3';
    public const HONEYDEW4  = 'honeydew4';
    public const GREEN1    = 'green1';
    public const LIME   = 'lime';
    public const LIMEGREEN = 'limegreen';
    public const DARKSEAGREEN4 = 'darkseagreen4';
    public const GREEN2        = 'green2';
    public const PALEGREEN4 = 'palegreen4';
    public const GREEN3     = 'green3';
    public const FORESTGREEN = 'forestgreen';
    public const GREEN4      = 'green4';
    public const GREEN  = 'green';
    public const DARKGREEN = 'darkgreen';
    public const LAWNGREEN = 'lawngreen';
    public const CHARTREUSE = 'chartreuse';
    public const CHARTREUSE1 = 'chartreuse1';
    public const CHARTREUSE2 = 'chartreuse2';
    public const CHARTREUSE3 = 'chartreuse3';
    public const CHARTREUSE4 = 'chartreuse4';
    public const GREENYELLOW = 'greenyellow';
    public const DARKOLIVEGREEN3 = 'darkolivegreen3';
    public const DARKOLIVEGREEN1 = 'darkolivegreen1';
    public const DARKOLIVEGREEN2 = 'darkolivegreen2';
    public const DARKOLIVEGREEN4 = 'darkolivegreen4';
    public const DARKOLIVEGREEN  = 'darkolivegreen';
    public const OLIVEDRAB      = 'olivedrab';
    public const OLIVEDRAB1 = 'olivedrab1';
    public const OLIVEDRAB2 = 'olivedrab2';
    public const OLIVEDRAB3 = 'olivedrab3';
    public const YELLOWGREEN = 'yellowgreen';
    public const OLIVEDRAB4  = 'olivedrab4';
    public const IVORY      = 'ivory';
    public const IVORY1 = 'ivory1';
    public const LIGHTYELLOW = 'lightyellow';
    public const LIGHTYELLOW1 = 'lightyellow1';
    public const BEIGE        = 'beige';
    public const IVORY2 = 'ivory2';
    public const LIGHTGOLDENRODYELLOW = 'lightgoldenrodyellow';
    public const LIGHTYELLOW2         = 'lightyellow2';
    public const IVORY3        = 'ivory3';
    public const LIGHTYELLOW3 = 'lightyellow3';
    public const IVORY4       = 'ivory4';
    public const LIGHTYELLOW4 = 'lightyellow4';
    public const YELLOW       = 'yellow';
    public const YELLOW1 = 'yellow1';
    public const YELLOW2 = 'yellow2';
    public const YELLOW3 = 'yellow3';
    public const YELLOW4 = 'yellow4';
    public const OLIVE   = 'olive';
    public const DARKKHAKI = 'darkkhaki';
    public const KHAKI2    = 'khaki2';
    public const LEMONCHIFFON4 = 'lemonchiffon4';
    public const KHAKI1        = 'khaki1';
    public const KHAKI3 = 'khaki3';
    public const KHAKI4 = 'khaki4';
    public const PALEGOLDENROD = 'palegoldenrod';
    public const LEMONCHIFFON  = 'lemonchiffon';
    public const LEMONCHIFFON1 = 'lemonchiffon1';
    public const KHAKI         = 'khaki';
    public const LEMONCHIFFON3 = 'lemonchiffon3';
    public const LEMONCHIFFON2 = 'lemonchiffon2';
    public const MEDIUMGOLDENROD = 'mediumgoldenrod';
    public const CORNSILK4       = 'cornsilk4';
    public const GOLD      = 'gold';
    public const GOLD1 = 'gold1';
    public const GOLD2 = 'gold2';
    public const GOLD3 = 'gold3';
    public const GOLD4 = 'gold4';
    public const LIGHTGOLDENROD = 'lightgoldenrod';
    public const LIGHTGOLDENROD4 = 'lightgoldenrod4';
    public const LIGHTGOLDENROD1 = 'lightgoldenrod1';
    public const LIGHTGOLDENROD3 = 'lightgoldenrod3';
    public const LIGHTGOLDENROD2 = 'lightgoldenrod2';
    public const CORNSILK3       = 'cornsilk3';
    public const CORNSILK2 = 'cornsilk2';
    public const CORNSILK  = 'cornsilk';
    public const CORNSILK1 = 'cornsilk1';
    public const GOLDENROD = 'goldenrod';
    public const GOLDENROD1 = 'goldenrod1';
    public const GOLDENROD2 = 'goldenrod2';
    public const GOLDENROD3 = 'goldenrod3';
    public const GOLDENROD4 = 'goldenrod4';
    public const DARKGOLDENROD = 'darkgoldenrod';
    public const DARKGOLDENROD1 = 'darkgoldenrod1';
    public const DARKGOLDENROD2 = 'darkgoldenrod2';
    public const DARKGOLDENROD3 = 'darkgoldenrod3';
    public const DARKGOLDENROD4 = 'darkgoldenrod4';
    public const FLORALWHITE    = 'floralwhite';
    public const WHEAT2      = 'wheat2';
    public const OLDLACE = 'oldlace';
    public const WHEAT   = 'wheat';
    public const WHEAT1 = 'wheat1';
    public const WHEAT3 = 'wheat3';
    public const ORANGE = 'orange';
    public const ORANGE1 = 'orange1';
    public const ORANGE2 = 'orange2';
    public const ORANGE3 = 'orange3';
    public const ORANGE4 = 'orange4';
    public const WHEAT4  = 'wheat4';
    public const MOCCASIN = 'moccasin';
    public const PAPAYAWHIP = 'papayawhip';
    public const NAVAJOWHITE3 = 'navajowhite3';
    public const BLANCHEDALMOND = 'blanchedalmond';
    public const NAVAJOWHITE    = 'navajowhite';
    public const NAVAJOWHITE1 = 'navajowhite1';
    public const NAVAJOWHITE2 = 'navajowhite2';
    public const NAVAJOWHITE4 = 'navajowhite4';
    public const ANTIQUEWHITE4 = 'antiquewhite4';
    public const ANTIQUEWHITE  = 'antiquewhite';
    public const TAN          = 'tan';
    public const BISQUE4 = 'bisque4';
    public const BURLYWOOD = 'burlywood';
    public const ANTIQUEWHITE2 = 'antiquewhite2';
    public const BURLYWOOD1    = 'burlywood1';
    public const BURLYWOOD3 = 'burlywood3';
    public const BURLYWOOD2 = 'burlywood2';
    public const ANTIQUEWHITE1 = 'antiquewhite1';
    public const BURLYWOOD4    = 'burlywood4';
    public const ANTIQUEWHITE3 = 'antiquewhite3';
    public const DARKORANGE    = 'darkorange';
    public const BISQUE2    = 'bisque2';
    public const BISQUE  = 'bisque';
    public const BISQUE1 = 'bisque1';
    public const BISQUE3 = 'bisque3';
    public const DARKORANGE1 = 'darkorange1';
    public const LINEN       = 'linen';
    public const DARKORANGE2 = 'darkorange2';
    public const DARKORANGE3 = 'darkorange3';
    public const DARKORANGE4 = 'darkorange4';
    public const PERU        = 'peru';
    public const TAN1 = 'tan1';
    public const TAN2 = 'tan2';
    public const TAN3 = 'tan3';
    public const TAN4 = 'tan4';
    public const PEACHPUFF = 'peachpuff';
    public const PEACHPUFF1 = 'peachpuff1';
    public const PEACHPUFF4 = 'peachpuff4';
    public const PEACHPUFF2 = 'peachpuff2';
    public const PEACHPUFF3 = 'peachpuff3';
    public const SANDYBROWN = 'sandybrown';
    public const SEASHELL4  = 'seashell4';
    public const SEASHELL2 = 'seashell2';
    public const SEASHELL3 = 'seashell3';
    public const CHOCOLATE = 'chocolate';
    public const CHOCOLATE1 = 'chocolate1';
    public const CHOCOLATE2 = 'chocolate2';
    public const CHOCOLATE3 = 'chocolate3';
    public const CHOCOLATE4 = 'chocolate4';
    public const SADDLEBROWN = 'saddlebrown';
    public const SEASHELL    = 'seashell';
    public const SEASHELL1 = 'seashell1';
    public const SIENNA4   = 'sienna4';
    public const SIENNA  = 'sienna';
    public const SIENNA1 = 'sienna1';
    public const SIENNA2 = 'sienna2';
    public const SIENNA3 = 'sienna3';
    public const LIGHTSALMON3 = 'lightsalmon3';
    public const LIGHTSALMON  = 'lightsalmon';
    public const LIGHTSALMON1 = 'lightsalmon1';
    public const LIGHTSALMON4 = 'lightsalmon4';
    public const LIGHTSALMON2 = 'lightsalmon2';
    public const CORAL        = 'coral';
    public const ORANGERED = 'orangered';
    public const ORANGERED1 = 'orangered1';
    public const ORANGERED2 = 'orangered2';
    public const ORANGERED3 = 'orangered3';
    public const ORANGERED4 = 'orangered4';
    public const DARKSALMON = 'darksalmon';
    public const SALMON1    = 'salmon1';
    public const SALMON2 = 'salmon2';
    public const SALMON3 = 'salmon3';
    public const SALMON4 = 'salmon4';
    public const CORAL1  = 'coral1';
    public const CORAL2 = 'coral2';
    public const CORAL3 = 'coral3';
    public const CORAL4 = 'coral4';
    public const TOMATO4 = 'tomato4';
    public const TOMATO  = 'tomato';
    public const TOMATO1 = 'tomato1';
    public const TOMATO2 = 'tomato2';
    public const TOMATO3 = 'tomato3';
    public const MISTYROSE4 = 'mistyrose4';
    public const MISTYROSE2 = 'mistyrose2';
    public const MISTYROSE  = 'mistyrose';
    public const MISTYROSE1 = 'mistyrose1';
    public const SALMON     = 'salmon';
    public const MISTYROSE3 = 'mistyrose3';
    public const WHITE      = 'white';
    public const GRAY100 = 'gray100';
    public const GREY100 = 'grey100';
    public const GRAY99  = 'gray99';
    public const GREY99 = 'grey99';
    public const GRAY98 = 'gray98';
    public const GREY98 = 'grey98';
    public const GRAY97 = 'gray97';
    public const GREY97 = 'grey97';
    public const GRAY96 = 'gray96';
    public const GREY96 = 'grey96';
    public const WHITESMOKE = 'whitesmoke';
    public const GRAY95     = 'gray95';
    public const GREY95 = 'grey95';
    public const GRAY94 = 'gray94';
    public const GREY94 = 'grey94';
    public const GRAY93 = 'gray93';
    public const GREY93 = 'grey93';
    public const GRAY92 = 'gray92';
    public const GREY92 = 'grey92';
    public const GRAY91 = 'gray91';
    public const GREY91 = 'grey91';
    public const GRAY90 = 'gray90';
    public const GREY90 = 'grey90';
    public const GRAY89 = 'gray89';
    public const GREY89 = 'grey89';
    public const GRAY88 = 'gray88';
    public const GREY88 = 'grey88';
    public const GRAY87 = 'gray87';
    public const GREY87 = 'grey87';
    public const GAINSBORO = 'gainsboro';
    public const GRAY86    = 'gray86';
    public const GREY86 = 'grey86';
    public const GRAY85 = 'gray85';
    public const GREY85 = 'grey85';
    public const GRAY84 = 'gray84';
    public const GREY84 = 'grey84';
    public const GRAY83 = 'gray83';
    public const GREY83 = 'grey83';
    public const LIGHTGRAY = 'lightgray';
    public const LIGHTGREY = 'lightgrey';
    public const GRAY82    = 'gray82';
    public const GREY82 = 'grey82';
    public const GRAY81 = 'gray81';
    public const GREY81 = 'grey81';
    public const GRAY80 = 'gray80';
    public const GREY80 = 'grey80';
    public const GRAY79 = 'gray79';
    public const GREY79 = 'grey79';
    public const GRAY78 = 'gray78';
    public const GREY78 = 'grey78';
    public const GRAY77 = 'gray77';
    public const GREY77 = 'grey77';
    public const GRAY76 = 'gray76';
    public const GREY76 = 'grey76';
    public const SILVER = 'silver';
    public const GRAY75 = 'gray75';
    public const GREY75 = 'grey75';
    public const GRAY74 = 'gray74';
    public const GREY74 = 'grey74';
    public const GRAY73 = 'gray73';
    public const GREY73 = 'grey73';
    public const GRAY72 = 'gray72';
    public const GREY72 = 'grey72';
    public const GRAY71 = 'gray71';
    public const GREY71 = 'grey71';
    public const GRAY70 = 'gray70';
    public const GREY70 = 'grey70';
    public const GRAY69 = 'gray69';
    public const GREY69 = 'grey69';
    public const GRAY68 = 'gray68';
    public const GREY68 = 'grey68';
    public const GRAY67 = 'gray67';
    public const GREY67 = 'grey67';
    public const DARKGRAY = 'darkgray';
    public const DARKGREY = 'darkgrey';
    public const GRAY66   = 'gray66';
    public const GREY66 = 'grey66';
    public const GRAY65 = 'gray65';
    public const GREY65 = 'grey65';
    public const GRAY64 = 'gray64';
    public const GREY64 = 'grey64';
    public const GRAY63 = 'gray63';
    public const GREY63 = 'grey63';
    public const GRAY62 = 'gray62';
    public const GREY62 = 'grey62';
    public const GRAY61 = 'gray61';
    public const GREY61 = 'grey61';
    public const GRAY60 = 'gray60';
    public const GREY60 = 'grey60';
    public const GRAY59 = 'gray59';
    public const GREY59 = 'grey59';
    public const GRAY58 = 'gray58';
    public const GREY58 = 'grey58';
    public const GRAY57 = 'gray57';
    public const GREY57 = 'grey57';
    public const GRAY56 = 'gray56';
    public const GREY56 = 'grey56';
    public const GRAY55 = 'gray55';
    public const GREY55 = 'grey55';
    public const GRAY54 = 'gray54';
    public const GREY54 = 'grey54';
    public const GRAY53 = 'gray53';
    public const GREY53 = 'grey53';
    public const GRAY52 = 'gray52';
    public const GREY52 = 'grey52';
    public const GRAY51 = 'gray51';
    public const GREY51 = 'grey51';
    public const FRACTAL = 'fractal';
    public const GRAY50  = 'gray50';
    public const GREY50 = 'grey50';
    public const GRAY   = 'gray';
    public const GREY = 'grey';
    public const GRAY49 = 'gray49';
    public const GREY49 = 'grey49';
    public const GRAY48 = 'gray48';
    public const GREY48 = 'grey48';
    public const GRAY47 = 'gray47';
    public const GREY47 = 'grey47';
    public const GRAY46 = 'gray46';
    public const GREY46 = 'grey46';
    public const GRAY45 = 'gray45';
    public const GREY45 = 'grey45';
    public const GRAY44 = 'gray44';
    public const GREY44 = 'grey44';
    public const GRAY43 = 'gray43';
    public const GREY43 = 'grey43';
    public const GRAY42 = 'gray42';
    public const GREY42 = 'grey42';
    public const DIMGRAY = 'dimgray';
    public const DIMGREY = 'dimgrey';
    public const GRAY41  = 'gray41';
    public const GREY41 = 'grey41';
    public const GRAY40 = 'gray40';
    public const GREY40 = 'grey40';
    public const GRAY39 = 'gray39';
    public const GREY39 = 'grey39';
    public const GRAY38 = 'gray38';
    public const GREY38 = 'grey38';
    public const GRAY37 = 'gray37';
    public const GREY37 = 'grey37';
    public const GRAY36 = 'gray36';
    public const GREY36 = 'grey36';
    public const GRAY35 = 'gray35';
    public const GREY35 = 'grey35';
    public const GRAY34 = 'gray34';
    public const GREY34 = 'grey34';
    public const GRAY33 = 'gray33';
    public const GREY33 = 'grey33';
    public const GRAY32 = 'gray32';
    public const GREY32 = 'grey32';
    public const GRAY31 = 'gray31';
    public const GREY31 = 'grey31';
    public const GRAY30 = 'gray30';
    public const GREY30 = 'grey30';
    public const GRAY29 = 'gray29';
    public const GREY29 = 'grey29';
    public const GRAY28 = 'gray28';
    public const GREY28 = 'grey28';
    public const GRAY27 = 'gray27';
    public const GREY27 = 'grey27';
    public const GRAY26 = 'gray26';
    public const GREY26 = 'grey26';
    public const GRAY25 = 'gray25';
    public const GREY25 = 'grey25';
    public const GRAY24 = 'gray24';
    public const GREY24 = 'grey24';
    public const GRAY23 = 'gray23';
    public const GREY23 = 'grey23';
    public const GRAY22 = 'gray22';
    public const GREY22 = 'grey22';
    public const GRAY21 = 'gray21';
    public const GREY21 = 'grey21';
    public const GRAY20 = 'gray20';
    public const GREY20 = 'grey20';
    public const GRAY19 = 'gray19';
    public const GREY19 = 'grey19';
    public const GRAY18 = 'gray18';
    public const GREY18 = 'grey18';
    public const GRAY17 = 'gray17';
    public const GREY17 = 'grey17';
    public const GRAY16 = 'gray16';
    public const GREY16 = 'grey16';
    public const GRAY15 = 'gray15';
    public const GREY15 = 'grey15';
    public const GRAY14 = 'gray14';
    public const GREY14 = 'grey14';
    public const GRAY13 = 'gray13';
    public const GREY13 = 'grey13';
    public const GRAY12 = 'gray12';
    public const GREY12 = 'grey12';
    public const GRAY11 = 'gray11';
    public const GREY11 = 'grey11';
    public const GRAY10 = 'gray10';
    public const GREY10 = 'grey10';
    public const GRAY9  = 'gray9';
    public const GREY9 = 'grey9';
    public const GRAY8 = 'gray8';
    public const GREY8 = 'grey8';
    public const GRAY7 = 'gray7';
    public const GREY7 = 'grey7';
    public const GRAY6 = 'gray6';
    public const GREY6 = 'grey6';
    public const GRAY5 = 'gray5';
    public const GREY5 = 'grey5';
    public const GRAY4 = 'gray4';
    public const GREY4 = 'grey4';
    public const GRAY3 = 'gray3';
    public const GREY3 = 'grey3';
    public const GRAY2 = 'gray2';
    public const GREY2 = 'grey2';
    public const GRAY1 = 'gray1';
    public const GREY1 = 'grey1';
    public const BLACK = 'black';
    public const GRAY0 = 'gray0';
    public const GREY0 = 'grey0';
    public const OPAQUE = 'opaque';
    public const NONE   = 'none';
    public const TRANSPARENT = 'transparent';

    use ColorTrait;
}
