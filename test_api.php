<?php
/**
 * Simple test endpoint to verify API is working
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

try {
    echo json_encode([
        'success' => true,
        'message' => 'API is working!',
        'timestamp' => time(),
        'server_time' => date('Y-m-d H:i:s'),
        'php_version' => PHP_VERSION
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
