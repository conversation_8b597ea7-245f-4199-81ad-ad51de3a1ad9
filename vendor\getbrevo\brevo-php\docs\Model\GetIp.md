# GetIp

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | ID of the dedicated IP | 
**ip** | **string** | Dedicated IP | 
**active** | **bool** | Status of the IP (true&#x3D;active, false&#x3D;inactive) | 
**domain** | **string** | Domain associated to the IP | 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


