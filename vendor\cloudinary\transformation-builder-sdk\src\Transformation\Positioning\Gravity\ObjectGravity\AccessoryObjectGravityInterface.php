<?php
/**
 * This file is part of the Cloudinary PHP package.
 *
 * (c) Cloudinary
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Cloudinary\Transformation;

/**
 * Interface AccessoryObjectGravityInterface
 *
 * @api
 */
interface AccessoryObjectGravityInterface
{
    //Accessory Category
    public const ACCESSORY = 'accessory';
    public const FRISBEE   = 'frisbee';
    public const SKIS    = 'skis';
    public const SNOWBOARD = 'snowboard';
    public const SPORTS_BALL = 'sportsball';
    public const KITE        = 'kite';
    public const BASEBALL_BAT = 'baseballbat';
    public const BASEBALL_GLOVE = 'baseballglove';
    public const SKATEBOARD     = 'skateboard';
    public const SURFBOARD  = 'surfboard';
    public const TENNIS_RACKET = 'tennisracket';
}
