# SendSmtpEmailAttachment

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**url** | **string** | Absolute url of the attachment (no local file). | [optional] 
**content** | **string** | Base64 encoded chunk data of the attachment generated on the fly | [optional] 
**name** | **string** | Required if content is passed. Name of the attachment | [optional] 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


