// pwa-install.js - Handles the PWA installation prompt and custom UI

let deferredPrompt; // Variable to store the beforeinstallprompt event
let installAppButton; // Reference to the dynamically created install button
let installNote; // Reference to the iOS install note element
let installNoteTimeout; // Timeout for hiding the iOS install note

// Function to dynamically inject CSS for all PWA-related UI
function injectPwaStyles() {
    const styleId = 'pwa-dynamic-styles';
    if (!document.getElementById(styleId)) {
        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
            /* Custom Message Box Styles */
            .custom-message-box {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background-color: white;
                padding: 25px;
                border-radius: 15px;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
                z-index: 10000;
                max-width: 350px;
                width: 90%;
                text-align: center;
                animation: fadeInScale 0.3s ease-out forwards;
                font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
            }

            .custom-message-box p {
                margin-bottom: 20px;
                font-size: 1.1em;
                color: #333;
                line-height: 1.5;
            }

            .message-box-buttons {
                display: flex;
                justify-content: center;
                gap: 10px;
                flex-wrap: wrap;
            }

            .custom-message-box button {
                padding: 10px 20px;
                border: none;
                border-radius: 8px;
                font-size: 1em;
                cursor: pointer;
                transition: background-color 0.2s ease, transform 0.2s ease;
                font-weight: 600;
            }

            .custom-message-box .btn-primary-popup {
                background-color: #FF6B35;
                color: white;
                box-shadow: 0 4px 10px rgba(255, 107, 53, 0.3);
            }

            .custom-message-box .btn-primary-popup:hover {
                background-color: #FF9F1C;
                transform: translateY(-2px);
            }

            .custom-message-box .close-btn {
                background-color: #f0f0f0;
                color: #555;
                border: 1px solid #ddd;
            }

            .custom-message-box .close-btn:hover {
                background-color: #e0e0e0;
                transform: translateY(-2px);
            }

            @keyframes fadeInScale {
                from { opacity: 0; transform: translate(-50%, -40%) scale(0.9); }
                to { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            }

            /* Install App Button Styles (for Android/Desktop) */
            #installAppButton {
                position: fixed;
                bottom: 80px; /* Adjust based on your bottom navigation */
                right: 20px;
                background-color: #FF6B35;
                color: white;
                width: 55px;
                height: 55px;
                border-radius: 50%;
                border: none;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
                font-size: 1.8rem;
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                z-index: 999;
                opacity: 0;
                transform: scale(0.8);
                transition: opacity 0.3s ease, transform 0.3s ease;
            }

            #installAppButton.show {
                opacity: 1;
                transform: scale(1);
            }

            /* iOS Install Note Styles */
            .install-note {
                position: fixed;
                bottom: 140px; /* Above the install button if present */
                right: 20px;
                background-color: #fff;
                color: #333;
                padding: 10px 15px;
                border-radius: 10px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                font-size: 0.85rem;
                max-width: 200px;
                text-align: center;
                z-index: 998;
                opacity: 0;
                transform: translateY(20px);
                transition: opacity 0.3s ease, transform 0.3s ease;
            }

            .install-note.show {
                opacity: 1;
                transform: translateY(0);
            }

            .install-note::after {
                content: '';
                position: absolute;
                bottom: -10px;
                right: 20px;
                width: 0;
                height: 0;
                border-left: 10px solid transparent;
                border-right: 10px solid transparent;
                border-top: 10px solid #fff;
            }

            /* iOS Instructions Modal Styles */
            .ios-modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.7);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000; /* Higher z-index for modal */
                opacity: 0;
                transition: opacity 0.3s ease;
            }
            .ios-modal-overlay.show {
                opacity: 1;
            }
            .ios-modal-content {
                background-color: #fff;
                border-radius: 1.5rem;
                box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
                padding: 2rem 1.5rem;
                text-align: center;
                max-width: 400px;
                width: 90%;
                display: flex;
                flex-direction: column;
                align-items: center;
                color: #333;
                position: relative;
                transform: translateY(20px);
                opacity: 0;
                transition: transform 0.3s ease, opacity 0.3s ease;
            }
            .ios-modal-overlay.show .ios-modal-content {
                transform: translateY(0);
                opacity: 1;
            }
            .ios-app-icon-placeholder {
                background: linear-gradient(135deg, #FF6B35 0%, #FF9F1C 100%);
                width: 80px;
                height: 80px;
                border-radius: 18px;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-bottom: 1rem;
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            }
            .ios-app-icon-placeholder .fas {
                font-size: 3rem;
                color: white;
                animation: pulse-scale 2s infinite ease-in-out;
            }
            .ios-modal-title {
                font-size: 1.5rem;
                font-weight: 700;
                color: #333;
                margin-bottom: 0.5rem;
            }
            .ios-modal-subtitle {
                font-size: 1.1rem;
                color: #555;
                margin-bottom: 1.5rem;
            }
            .ios-step {
                display: flex;
                align-items: center;
                text-align: left;
                margin-bottom: 1rem;
                width: 100%;
            }
            .ios-step-number {
                font-size: 1.2rem;
                font-weight: 600;
                color: #FF6B35;
                min-width: 2rem;
                text-align: center;
                margin-right: 0.75rem;
            }
            .ios-step-text {
                flex-grow: 1;
                font-size: 0.95rem;
                line-height: 1.4;
                color: #444;
            }
            .ios-step-icon {
                font-size: 1.5rem;
                color: #FF6B35;
                margin-left: 0.75rem;
                flex-shrink: 0;
            }
            .ios-modal-close-btn {
                position: absolute;
                top: 10px;
                right: 10px;
                background: none;
                border: none;
                font-size: 1.5rem;
                color: #ccc;
                cursor: pointer;
                padding: 5px;
                border-radius: 50%;
                transition: color 0.2s ease;
            }
            .ios-modal-close-btn:hover {
                color: #888;
            }

            @keyframes pulse-scale {
                0% { transform: scale(1); opacity: 1; }
                50% { transform: scale(1.05); opacity: 0.9; }
                100% { transform: scale(1); opacity: 1; }
            }

            @media (max-width: 640px) {
                .ios-modal-content {
                    padding: 1.5rem 1rem;
                }
                .ios-modal-title {
                    font-size: 1.3rem;
                }
                .ios-modal-subtitle {
                    font-size: 1rem;
                }
                .ios-step-text {
                    font-size: 0.85rem;
                }
                .ios-step-icon {
                    font-size: 1.3rem;
                }
            }
        `;
        document.head.appendChild(style);
    }
}

// Function to show a custom message box
function showMessageBox(message, buttons = [{ text: 'OK', callback: null, className: 'btn-primary-popup' }]) {
    const existingBox = document.querySelector('.custom-message-box');
    if (existingBox) existingBox.remove();

    const messageBox = document.createElement('div');
    messageBox.classList.add('custom-message-box');
    
    let buttonHtml = buttons.map((btn, index) => 
        `<button class="${btn.className || ''}" data-callback-index="${index}">${btn.text}</button>`
    ).join('');

    messageBox.innerHTML = `
        <p>${message}</p>
        <div class="message-box-buttons">${buttonHtml}</div>
    `;
    document.body.appendChild(messageBox);

    buttons.forEach((btn, index) => {
        messageBox.querySelector(`[data-callback-index="${index}"]`).addEventListener('click', () => {
            messageBox.remove();
            if (btn.callback) {
                btn.callback();
            }
        });
    });
}

// Function to detect if the user is on an iOS device
function isIOS() {
    const userAgent = window.navigator.userAgent;
    return /iPad|iPhone|iPod/.test(userAgent) && !window.MSStream && !/CriOS/.test(userAgent);
}

// Function to dynamically show/hide the iOS install note
function toggleIOSInstallNote(show) {
    if (!installNote) {
        installNote = document.createElement('div');
        installNote.id = 'installNote';
        installNote.className = 'install-note';
        installNote.textContent = 'Please Install Our App for a Better Experience';
        document.body.appendChild(installNote);
    }

    clearTimeout(installNoteTimeout);

    if (show) {
        installNote.classList.add('show');
        installNoteTimeout = setTimeout(() => {
            installNote.classList.remove('show');
        }, 5000); // Note disappears after 5 seconds
    } else {
        installNote.classList.remove('show');
    }
}

// HTML content for the iOS Add to Home Screen instructions modal
const iosInstructionsHtmlContent = `
    <div class="ios-modal-content">
        <button class="ios-modal-close-btn" aria-label="Close instructions"><i class="fas fa-times-circle"></i></button>
        <div class="ios-app-icon-placeholder">
            <i class="fas fa-utensils"></i>
        </div>
        <h2 class="ios-modal-title">Install MIKO App</h2>
        <p class="ios-modal-subtitle">
            Add MIKO to your Home Screen for quick access and the best experience.
        </p>

        <div class="ios-step">
            <div class="ios-step-number">1</div>
            <div class="ios-step-text">
                Tap the <span class="font-bold" style="color: #FF6B35;">Share button</span> 
                <br>at the bottom of your Safari browser.
            </div>
            <i class="ios-step-icon fas fa-share-square"></i>
        </div>

        <div class="ios-step">
            <div class="ios-step-number">2</div>
            <div class="ios-step-text">
                Scroll down and select <span class="font-bold" style="color: #FF6B35;">"Add to Home Screen"</span>.
            </div>
            <i class="ios-step-icon fas fa-plus-square"></i>
        </div>

        <div class="ios-step">
            <div class="ios-step-number">3</div>
            <div class="ios-step-text">
                Tap <span class="font-bold" style="color: #FF6B35;">"Add"</span> in the top right corner.
            </div>
            <i class="ios-step-icon fas fa-check-circle"></i>
        </div>
    </div>
`;

// Function to show the iOS instructions as a modal
function showIosInstructionsModal() {
    const modalOverlay = document.createElement('div');
    modalOverlay.classList.add('ios-modal-overlay');
    modalOverlay.innerHTML = iosInstructionsHtmlContent;
    document.body.appendChild(modalOverlay);

    // Add event listener to close button
    const closeButton = modalOverlay.querySelector('.ios-modal-close-btn');
    if (closeButton) {
        closeButton.addEventListener('click', () => {
            modalOverlay.classList.remove('show');
            setTimeout(() => modalOverlay.remove(), 300); // Remove after transition
        });
    }

    // Show the modal with a slight delay to allow CSS transition
    setTimeout(() => {
        modalOverlay.classList.add('show');
    }, 10);
}


document.addEventListener('DOMContentLoaded', () => {
    // Inject all necessary styles when the DOM is ready
    injectPwaStyles();

    // Dynamically create the install button
    installAppButton = document.createElement('button');
    installAppButton.id = 'installAppButton';
    installAppButton.setAttribute('aria-label', 'Install App');
    installAppButton.innerHTML = '<i class="fas fa-download"></i>'; // Icon for the button
    document.body.appendChild(installAppButton);

    // Check if the app is already installed (running in standalone mode)
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches;

    // Logic for iOS devices
    if (isIOS()) {
        // On iOS, the 'beforeinstallprompt' event does not fire.
        // We always show our custom install note/button if not in standalone mode.
        if (!isStandalone) {
            installAppButton.classList.add('show');
            toggleIOSInstallNote(true); // Show the floating note
        }

        installAppButton.addEventListener('click', () => {
            showIosInstructionsModal(); // Show the detailed iOS instructions modal
        });

    } else {
        // Logic for Android/Desktop (where beforeinstallprompt fires)
        window.addEventListener('beforeinstallprompt', (e) => {
            // Prevent the default browser prompt
            e.preventDefault();
            // Stash the event so it can be triggered later
            deferredPrompt = e;
            console.log('beforeinstallprompt event fired. Deferred prompt saved.');

            // Only show the install button if not already in standalone mode
            if (!isStandalone) {
                installAppButton.classList.add('show'); // Make the custom install button visible

                // Show a message box to prompt installation
                showMessageBox(
                    'Unlock the full MIKO experience! Install our app for lightning-fast orders and exclusive deals!',
                    [
                        { 
                            text: 'Install App', 
                            callback: async () => {
                                if (installAppButton) installAppButton.classList.remove('show'); // Hide button before prompt
                                deferredPrompt.prompt(); // Show the browser's install prompt
                                const { outcome } = await deferredPrompt.userChoice;
                                if (outcome === 'dismissed') {
                                    console.log('User dismissed the A2HS prompt');
                                    if (installAppButton) installAppButton.classList.add('show'); // Re-show if dismissed
                                } else {
                                    console.log('User accepted the A2HS prompt');
                                }
                                deferredPrompt = null; // Clear the deferred prompt
                            },
                            className: 'btn-primary-popup' 
                        },
                        { 
                            text: 'No Thanks', 
                            callback: () => {
                                console.log('User declined A2HS prompt via custom message');
                                if (installAppButton) installAppButton.classList.add('show'); // Re-show if declined
                            },
                            className: 'close-btn' 
                        }
                    ]
                );
            }
        });

        // Handle click on the install button for non-iOS devices
        installAppButton.addEventListener('click', async () => {
            if (deferredPrompt) {
                installAppButton.classList.remove('show'); // Hide button before prompt
                deferredPrompt.prompt(); // Show the browser's install prompt
                const { outcome } = await deferredPrompt.userChoice;
                if (outcome === 'dismissed') {
                    console.log('User dismissed the A2HS prompt via button click');
                    installAppButton.classList.add('show'); // Re-show if dismissed
                } else {
                    console.log('User accepted the A2HS prompt via button click');
                }
                deferredPrompt = null; // Clear the deferred prompt
            } else {
                // If deferredPrompt is null, it means either already installed or not installable yet
                console.log('No deferred install prompt available. PWA might already be installed or criteria not met.');
                // Optionally, inform the user they can install via browser menu if not already installed
                if (!isStandalone) {
                     showMessageBox('To install, use your browser\'s "Add to Home Screen" option in the menu.', [{ text: 'OK', callback: null, className: 'btn-primary-popup' }]);
                }
            }
        });
    }

    // Event listener for when the PWA is successfully installed
    window.addEventListener('appinstalled', () => {
        console.log('PWA was successfully installed!');
        if (installAppButton) installAppButton.classList.remove('show'); // Hide the install button
        if (isIOS()) {
            toggleIOSInstallNote(false); // Hide the iOS note
        }
        showMessageBox('MIKO has been successfully installed! You can now launch it from your home screen or app drawer.', [{ text: 'Great!', callback: null, className: 'btn-primary-popup' }]);
    });

    // If the app is already running in standalone mode (installed), hide the install button and iOS note
    if (isStandalone) {
        if (installAppButton) installAppButton.style.display = 'none';
        if (isIOS()) {
            toggleIOSInstallNote(false);
        }
    }
});
