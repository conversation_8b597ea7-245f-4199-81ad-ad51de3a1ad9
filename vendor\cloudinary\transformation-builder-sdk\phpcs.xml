<?xml version="1.0"?>
<ruleset name="php-transformation-builder-sdk coding standard">
    <description>php-transformation-builder-sdk coding standard</description>

    <!-- display progress -->
    <arg value="p"/>
    <arg name="colors"/>

    <!-- inherit rules from: -->
    <rule ref="PSR2"/>

    <rule ref="Squiz.WhiteSpace.SuperfluousWhitespace">
        <properties>
            <property name="ignoreBlankLines" value="false"/>
        </properties>
    </rule>

    <!-- Paths to check -->
    <file>src</file>
    <file>tests</file>
</ruleset>
