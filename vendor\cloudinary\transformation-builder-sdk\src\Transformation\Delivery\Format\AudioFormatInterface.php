<?php
/**
 * This file is part of the Cloudinary PHP package.
 *
 * (c) Cloudinary
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Cloudinary\Transformation;

/**
 * Interface AudioFormatInterface
 *
 * @api
 */
interface AudioFormatInterface
{
    public const MP3 = 'mp3';
    public const AAC = 'aac';
    public const M4A = 'm4a';
    public const OGG = 'ogg';
    public const WAV = 'wav';
    public const AIFF = 'aiff';
    public const FLAC = 'flac';
    public const AMR  = 'amr';
    public const MIDI = 'midi';
}
