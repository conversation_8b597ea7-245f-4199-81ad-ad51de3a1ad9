       /* Basic Reset & Variables */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        :root {
            --primary-accent: #FF6B35;
            --secondary-accent: #FF9F1C;
            --neutral-base: #F7F7F7;
            --text-dark-elements: #333333;
            --subtle-detail: #A7A7A7;
            --white: #FFFFFF;
            --border-gray: #E0E0E0;
            --shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        body {
            background-color: var(--primary-accent);
            color: var(--text-dark-elements);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1.5rem;
            overflow-x: hidden;
        }

        .reset-container {
            background-color: var(--white);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--shadow);
            width: 100%;
            max-width: 450px;
            text-align: center;
        }

        .reset-container h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--primary-accent);
        }

        .reset-container p {
            font-size: 0.95rem;
            color: var(--subtle-detail);
            margin-bottom: 2rem;
        }

        .input-group {
            margin-bottom: 1.5rem;
            text-align: left;
        }

        .input-group label {
            display: block;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-dark-elements);
        }

        .input-group input {
            width: 100%;
            padding: 1rem;
            border: 1px solid var(--border-gray);
            border-radius: 12px;
            font-size: 1rem;
            color: var(--text-dark-elements);
            background-color: var(--neutral-base);
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.08);
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        .input-group input:focus {
            outline: none;
            border-color: var(--primary-accent);
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.08), 0 0 0 2px rgba(255, 107, 53, 0.2);
        }

        .password-input-container {
            position: relative;
        }

        .password-toggle-btn {
            position: absolute;
            right: 1rem;
            top: 50%; /* Adjusted from 50% to align with input center */
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--subtle-detail);
            cursor: pointer;
            font-size: 1.2rem;
            padding: 0.2rem;
        }

        .password-toggle-btn:hover {
            color: var(--text-dark-elements);
        }

        .submit-btn {
            background-color: var(--primary-accent);
            color: var(--white);
            border: none;
            padding: 1rem;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            width: 100%;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: 0 4px 10px rgba(255, 107, 53, 0.3);
        }

        .submit-btn:hover {
            background-color: var(--secondary-accent);
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(255, 107, 53, 0.4);
        }

        .message-box {
            margin-top: 1.5rem;
            padding: 1rem;
            border-radius: 12px;
            font-size: 0.9rem;
            display: none; /* Hidden by default */
            text-align: left;
        }

        .message-box.success {
            background-color: #e6ffe6;
            color: #28a745;
            border: 1px solid #28a745;
        }

        .message-box.error {
            background-color: #ffe6e6;
            color: #dc3545;
            border: 1px solid #dc3545;
        }

        .loading-dots {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 1em;
        }

        .loading-dots span {
            display: inline-block;
            width: 8px;
            height: 8px;
            background-color: var(--white);
            border-radius: 50%;
            margin: 0 4px;
            animation: bounce 0.6s infinite alternate;
        }

        .loading-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .loading-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes bounce {
            0% {
                transform: translateY(0);
            }
            100% {
                transform: translateY(-8px);
            }
        }

        @media (max-width: 480px) {
            .reset-container {
                padding: 1.5rem;
            }
            .reset-container h1 {
                font-size: 1.8rem;
            }
        }