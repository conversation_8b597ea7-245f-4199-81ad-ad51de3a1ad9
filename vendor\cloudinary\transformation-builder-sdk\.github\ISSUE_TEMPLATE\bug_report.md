---
name: Bug report
about: Bug report for Cloudinary PHP Transformation Builder SDK
title: ''
labels: ''
assignees: ''

---

## Bug report for Cloudinary PHP Transformation Builder SDK
Before proceeding, please update to the latest version and test if the issue persists

## Describe the bug in a sentence or two.
…

## Issue Type (Can be multiple)
- [ ] Build - Can’t install or import the SDK
- [ ] Performance - Performance issues
- [ ] Behaviour - Functions aren’t working as expected (such as generate URL)
- [ ] Documentation - Inconsistency between the docs and behaviour
- [ ] Other (Specify)

## Steps to reproduce
… if applicable

## Error screenshots or Stack Trace (if applicable)
…

## Operating System
- [ ] Linux
- [ ] Windows
- [ ] macOS
- [ ] All

## Environment and Frameworks (fill in the version numbers)

- PHP Cloudinary SDK version - 0.0.0
- PHP Version - 0.0.0 
- Framework (Laravel, Symphony, etc) - 0.0.0

## Repository

If possible, please provide a link to a reproducible repository that showcases the problem
