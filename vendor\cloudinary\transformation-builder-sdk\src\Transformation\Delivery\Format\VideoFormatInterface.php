<?php
/**
 * This file is part of the Cloudinary PHP package.
 *
 * (c) Cloudinary
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Cloudinary\Transformation;

/**
 * Interface ImageFormatInterface
 *
 * @api
 */
interface VideoFormatInterface
{
    public const AVI = 'avi';
    public const MP4 = 'mp4';
    public const WEBM = 'webm';
    public const MOV  = 'mov';
    public const OGV = 'ogv';
    public const F_3GP = '3gp';
    public const F_3G2 = '3g2';
    public const WMV   = 'wmv';
    public const MPEG = 'mpeg';
    public const FLV  = 'flv';
    public const M3U8 = 'm3u8';
    public const TS   = 'ts';
    public const MKV = 'mkv';
    public const MPD = 'mpd';
}
