<?php
/**
 * This file is part of the Cloudinary PHP package.
 *
 * (c) Cloudinary
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Cloudinary\Api\Upload;

/**
 * Class UploadEndPoint
 */
class UploadEndPoint
{
    public const UPLOAD           = 'upload';
    public const DESTROY          = 'destroy';
    public const RENAME           = 'rename';
    public const EXPLICIT         = 'explicit';
    public const SPRITE           = 'sprite';
    public const MULTI            = 'multi';
    public const EXPLODE          = 'explode';
    public const TAGS             = 'tags';
    public const CONTEXT          = 'context';
    public const TEXT             = 'text';
    public const GENERATE_ARCHIVE = 'generate_archive';
    public const CREATE_SLIDESHOW = 'create_slideshow';
    public const METADATA         = 'metadata';
    public const DOWNLOAD         = 'download';
    public const DOWNLOAD_BACKUP  = 'download_backup';
}
