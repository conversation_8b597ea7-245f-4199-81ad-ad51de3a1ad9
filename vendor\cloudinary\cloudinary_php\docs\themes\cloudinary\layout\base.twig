<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>{% block title project.config('title') %}</title>

    {% block head %}
        <link rel="stylesheet" type="text/css" href="{{ path('css/bootstrap.min.css') }}">
        <link rel="stylesheet" type="text/css" href="{{ path('css/bootstrap-theme.min.css') }}">
        <!--link rel="stylesheet" type="text/css" href="{{ path('css/sami.css') }}"-->
        <link rel="stylesheet" type="text/css" href="{{ path('css/cloudinary.css') }}">
        <script src="{{ path('js/jquery-1.11.1.min.js') }}"></script>
        <script src="{{ path('js/bootstrap.min.js') }}"></script>
        <script src="{{ path('js/typeahead.min.js') }}"></script>
        <script src="{{ path('sami.js') }}"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">
    {% endblock %}

    <link rel="shortcut icon" href="https://cloudinary-res.cloudinary.com/image/upload/docsite/brand-assets/cloudinary_favicon_32x32.png" />

    {% if project.config('base_url') %}
        {%- for version in project.versions -%}
            <link rel="search"
                  type="application/opensearchdescription+xml"
                  href="{{ project.config('base_url')|replace({'%version%': version}) }}/opensearch.xml"
                  title="{{ project.config('title') }} ({{ version }})" />
        {% endfor -%}
    {% endif %}
</head>

{% block html %}
    <body id="{% block body_class '' %}" data-name="{% block page_id '' %}" data-root-path="{{ root_path }}">
        {% block content '' %}
    </body>
{% endblock %}

</html>
