<?php
/**
 * This file is part of the Cloudinary PHP package.
 *
 * (c) Cloudinary
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Cloudinary\Api\Upload;

/**
 * Class TagCommand
 */
class TagCommand
{
    public const ADD = 'add';
    public const REMOVE = 'remove';
    public const REPLACE = 'replace';
    public const SET_EXCLUSIVE = 'set_exclusive';
    public const REMOVE_ALL    = 'remove_all';
}
