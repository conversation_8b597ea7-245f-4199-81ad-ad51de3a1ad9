/**
 * Performance Monitor for MIKO App
 * Lightweight performance tracking for home.html
 */

class PerformanceMonitor {
    constructor() {
        this.metrics = {};
        this.startTime = performance.now();
        this.init();
    }

    init() {
        // Track page load metrics
        window.addEventListener('load', () => {
            this.trackPageMetrics();
        });

        // Track DOM content loaded
        document.addEventListener('DOMContentLoaded', () => {
            this.metrics.domReady = performance.now() - this.startTime;
            console.log(`📊 DOM Ready: ${this.metrics.domReady.toFixed(2)}ms`);
        });

        // Track first paint and contentful paint
        this.trackPaintMetrics();
    }

    trackPageMetrics() {
        const navigation = performance.getEntriesByType('navigation')[0];
        
        this.metrics.pageLoad = navigation.loadEventEnd - navigation.fetchStart;
        this.metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.fetchStart;
        this.metrics.firstByte = navigation.responseStart - navigation.fetchStart;
        
        console.log(`📊 Performance Metrics:
        - Page Load: ${this.metrics.pageLoad.toFixed(2)}ms
        - DOM Content Loaded: ${this.metrics.domContentLoaded.toFixed(2)}ms
        - First Byte: ${this.metrics.firstByte.toFixed(2)}ms`);
    }

    trackPaintMetrics() {
        // Use PerformanceObserver for paint metrics
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.name === 'first-paint') {
                        this.metrics.firstPaint = entry.startTime;
                        console.log(`🎨 First Paint: ${entry.startTime.toFixed(2)}ms`);
                    }
                    if (entry.name === 'first-contentful-paint') {
                        this.metrics.firstContentfulPaint = entry.startTime;
                        console.log(`🎨 First Contentful Paint: ${entry.startTime.toFixed(2)}ms`);
                    }
                }
            });
            observer.observe({ entryTypes: ['paint'] });
        }
    }

    // Track API call performance
    trackAPI(name, startTime, endTime, dataSize = 0) {
        const duration = endTime - startTime;
        this.metrics[name] = duration;
        
        const sizeInfo = dataSize > 0 ? ` (${dataSize} items)` : '';
        console.log(`🚀 ${name}: ${duration.toFixed(2)}ms${sizeInfo}`);
        
        // Performance rating
        let rating = '🐌 Slow';
        if (duration < 100) rating = '⚡ Excellent';
        else if (duration < 300) rating = '🚀 Good';
        else if (duration < 500) rating = '⏳ Fair';
        
        console.log(`   Rating: ${rating}`);
    }

    // Track DOM manipulation performance
    trackDOMOperation(name, operation) {
        const startTime = performance.now();
        const result = operation();
        const endTime = performance.now();
        
        this.trackAPI(`DOM: ${name}`, startTime, endTime);
        return result;
    }

    // Get performance summary
    getSummary() {
        const summary = {
            ...this.metrics,
            totalTime: performance.now() - this.startTime
        };
        
        console.table(summary);
        return summary;
    }

    // Check if performance is good
    isPerformanceGood() {
        const thresholds = {
            firstContentfulPaint: 1500,
            domContentLoaded: 2000,
            pageLoad: 3000
        };

        const issues = [];
        for (const [metric, threshold] of Object.entries(thresholds)) {
            if (this.metrics[metric] && this.metrics[metric] > threshold) {
                issues.push(`${metric}: ${this.metrics[metric].toFixed(2)}ms (target: <${threshold}ms)`);
            }
        }

        if (issues.length === 0) {
            console.log('✅ Performance is excellent!');
            return true;
        } else {
            console.warn('⚠️ Performance issues detected:', issues);
            return false;
        }
    }
}

// Create global performance monitor
window.PerfMonitor = new PerformanceMonitor();

// Add helper functions for easy tracking
window.trackAPI = (name, promise) => {
    const startTime = performance.now();
    return promise.then(result => {
        const endTime = performance.now();
        const dataSize = Array.isArray(result) ? result.length : 0;
        window.PerfMonitor.trackAPI(name, startTime, endTime, dataSize);
        return result;
    }).catch(error => {
        const endTime = performance.now();
        window.PerfMonitor.trackAPI(`${name} (FAILED)`, startTime, endTime);
        throw error;
    });
};

window.trackDOM = (name, operation) => {
    return window.PerfMonitor.trackDOMOperation(name, operation);
};

// Auto-generate performance report after 5 seconds
setTimeout(() => {
    console.log('\n📊 === PERFORMANCE REPORT ===');
    window.PerfMonitor.getSummary();
    window.PerfMonitor.isPerformanceGood();
    console.log('=== END REPORT ===\n');
}, 5000);
