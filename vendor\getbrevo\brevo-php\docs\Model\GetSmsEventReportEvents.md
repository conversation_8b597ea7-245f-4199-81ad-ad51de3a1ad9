# GetSmsEventReportEvents

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**phoneNumber** | **string** | Phone number which has generated the event | [optional] 
**date** | **string** | UTC date-time on which the event has been generated | [optional] 
**messageId** | **string** | Message ID which generated the event | [optional] 
**event** | **string** | Event which occurred | [optional] 
**reason** | **string** | Reason of bounce (only available if the event is hardbounce or softbounce) | [optional] 
**reply** | **string** |  | [optional] 
**tag** | **string** | Tag of the SMS which generated the event | [optional] 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


