<?php
/**
 * This file is part of the Cloudinary PHP package.
 *
 * (c) Cloudinary
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Cloudinary\Transformation;

/**
 * Interface AnimalObjectGravityInterface
 *
 * @api
 */
interface AnimalObjectGravityInterface
{
    //Animal Category
    public const ANIMAL = 'animal';
    public const BIRD   = 'bird';
    public const CAT  = 'cat';
    public const DOG = 'dog';
    public const HORSE = 'horse';
    public const SHEEP = 'sheep';
    public const COW   = 'cow';
    public const ELEPHANT = 'elephant';
    public const BEAR     = 'bear';
    public const ZEBRA = 'zebra';
    public const GIRAFFE = 'giraffe';
}
