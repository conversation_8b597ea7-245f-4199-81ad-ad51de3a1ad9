<?php
/**
 * This file is part of the Cloudinary PHP package.
 *
 * (c) Cloudinary
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Cloudinary\Transformation;

/**
 * Interface ApplianceObjectGravityInterface
 *
 * @api
 */
interface ApplianceObjectGravityInterface
{
    //Appliance Category
    public const APPLIANCE = 'appliance';
    public const MICROWAVE = 'microwave';
    public const OVEN      = 'oven';
    public const TOASTER = 'toaster';
    public const SINK    = 'sink';
    public const REFRIGERATOR = 'refrigerator';
}
