html, body, #content {
    height: 100%;
    font-family: "Inter", Helvetica, Arial, sans-serif;
    color: #0C163B;
}

/* Cloudinary overrides of bootstrap styles*/
a {
    color: #FF5050;
    text-decoration: none;
}

a:hover, a:focus {
    color: #FF5050;
    text-decoration: underline;
}

a:visited {
    color: #D92B2B;
    text-decoration: none;
}

h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
    line-height: 1.5;
    font-family: "sofia-pro", Helvetica, Arial, sans-serif;
    color: #0C163B;
}

.form-control {
    border: 1px solid #3448C5;
}

.btn-link:hover, .btn-link:focus {
    color: #3448C5;
}

.navbar-default .navbar-nav > li > a:hover, .navbar-default .navbar-nav > li > a:focus {
    color: #D92B2B;
}

.navbar-inverse .navbar-nav > li > a:hover, .navbar-inverse .navbar-nav > li > a:focus {
    color: #FF5050;
}

.pagination > li > a, .pagination > li > span {
    color: #FF5050;
}

.pagination > li > a:hover, .pagination > li > span:hover, .pagination > li > a:focus, .pagination > li > span:focus {
    color: #FFF;
}

.pagination > .active > a, .pagination > .active > span, .pagination > .active > a:hover, .pagination > .active > span:hover, .pagination > .active > a:focus, .pagination > .active > span:focus {
    background-color: #FF5050;
    border-color: #FF5050;
}

/* Site menu */

#site-nav.navbar-default {
    margin: 0;
    border-radius: 0;
    border-bottom: 1px solid #ccc;
    background-color: #FFF;
    background-image: none;
}

#site-nav.navbar-default .navbar-nav > li > a {
    color: #0c163b;
}

#site-nav.navbar-default .navbar-brand {
    color: #3448C5;
}

.navbar-brand {
    background-image: url("https://cloudinary-res.cloudinary.com/image/upload/docsite/brand-assets/cloudinary_cloud_glyph_4ref.svg");
    background-repeat: no-repeat;
    background-position: left center;
    padding-left: 60px;
}

#site-nav.navbar-default .navbar-nav > li > a:hover {
    text-decoration: underline;
    color: #FF5050;
}

#navbar-elements {
    float: right;
}

@media (max-width: 768px) {
    #navbar-elements {
        float: none !important;
    }
}

/* Namespace breadcrumbs */

.namespace-breadcrumbs .breadcrumb {
    margin: 0 0 12px;
    border-radius: 0 0 4px 4px;
    padding-left: 35px;
}

.breadcrumb {
    background-color: #3448C5;
}

.breadcrumb > li > a {
    color: #FFF;
}

.breadcrumb > li {
    color: #FFF;
}

.namespace-breadcrumbs .breadcrumb > li + li:before {
    content: "";
}

.namespace-breadcrumbs .breadcrumb > .backslash {
    color: #fff;
}

/* Site columns */

#right-column {
    margin-left: 20%;
}

#page-content {
    padding: 0 30px;
}

#left-column {
    width: 20%;
    position: fixed;
    height: 100%;
    border-right: 1px solid #ccc;
    line-height: 18px;
    font-size: 13px;
    display: flex;
    flex-flow: column;
}

@media (max-width: 991px) {
    #left-column {
        display: none;
    }

    #right-column {
        width: 100%;
        margin-left: 0;
    }
}

/* API Tree */

#api-tree {
    background: linear-gradient(
            to bottom,
            #FFF,
            #FFF 50%,
            #f0f2f5 50%,
            #f0f2f5
    );
    background-size: 100% 56px;
    overflow: auto;
    height: 100%;
    background-attachment: local;
}

#api-tree ul {
    list-style-type: none;
    margin: 0;
    padding: 0;
}

#api-tree ul li {
    padding: 0;
    margin: 0;
}

/* Prevents the menu from jittering on lad */
#api-tree .glyphicon-play {
    width: 26px;
}

#api-tree ul li .hd {
    padding: 5px;
}

#api-tree li .hd:nth-child(even) {
    background-color: #EDF3FE;
}

#api-tree ul li.opened > .hd span {
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
}

#api-tree .bd {
    display: none;
}

#api-tree li.opened > .bd {
    display: block;
}

#api-tree li .hd:hover {
    background-color: #b6d2ff;
}

#api-tree li.active > .hd {
    background-color: #3448C5;
}

#api-tree li.active > .hd a {
    color: #ffffff;
    font-weight: bold;
}

#api-tree a {
    color: #222;
}

#api-tree div.leaf a {
    margin-left: 20px;
}

#api-tree .hd span {
    padding: 2px 8px;
    font-size: 10px;
    line-height: 85%;
}

/* Control panel, search form, version drop-down */

#control-panel {
    background: #F1F2F9;
    border-bottom: 1px solid #F1F2F9;
    padding: 4px;
}

#control-panel form {
    margin: 4px 4px 5px 4px;
}

#search-form {
    position: relative;
}

#search-form input {
    width: 100%;
    padding-left: 28px;
}

#search-form span.glyphicon-search {
    position: absolute;
    left: 9px;
    top: 9px;
    font-size: 14px;
    z-index: 2;
}

/* Typeahead */

.twitter-typeahead {
    width: 100%;
    z-index: 1;
}

.tt-dropdown-menu {
    overflow: auto;
    max-height: 260px;
    margin-top: 9px;
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 8px;
    box-shadow: 0 5px 10px rgba(0, 0, 0, .2);
    padding: 8px;
}

.tt-dropdown-menu p {
    margin: 0;
    padding: 0;
}

.tt-suggestion {
    padding: 8px;
    border-bottom: 1px solid #ccc;
    font-size: 1.1em;
}

.tt-cursor {
    background-color: #3875D7;
    color: #fff;
}

/** General typography **/

.navbar {
    border-bottom: 0;
}

.page-header {
    margin: 0 0 20px;
}

abbr[title], abbr[data-original-title], abbr {
    border-bottom: none;
    cursor: pointer;
}

a abbr {
    cursor: pointer;
    color: #FF5050;
}



.method-description table, .description table {
    border: solid 1px #ccc;
    padding: 1em;
    margin: 1em;
}

.method-description td, .method-description th,
.description td, .description th {
    padding: 0.75em 1.25em;
}

.method-description tbody tr:nth-child(even),
.description tbody tr:nth-child(even) {
    background: #F1F2F9;
}

.method-description tbody tr:nth-child(odd),
.description tbody tr:nth-child(odd) {
    background: #fff;
}

.method-description thead tr,
.description thead tr {
    background: #edf3fe;
}

/** General Sami styling **/

.underlined > .row {
    padding: 8px 0;
    border-bottom: 1px solid #ddd;
}

#footer {
    text-align: right;
    margin: 30px;
    font-size: 11px;
}

.description {
    margin: 10px 0;
    padding: 10px;
    background-color: #F1F2F9;
}

.description p {
    padding: 0;
    margin: 8px 0;
}

.method-description {
    margin: 0 0 24px 0;
}

.details {
    padding-left: 30px;
}

#method-details .method-item {
    margin-bottom: 30px;
}

.method-item h3,
.method-item h3 code {
    background-color: #eee;
}

.method-item h3 {
    padding: 4px;
    margin-bottom: 16px;
    font-size: 16px;
}

.location {
    font-size: 11px;
    float: right;
    font-style: italic;
}

.namespace-list a {
    padding: 3px 8px;
    margin: 0 5px 5px 0;
    border: 1px solid #ddd;
    color: #3448C5;
    background-color: #f9f9f9;
    display: inline-block;
    border-radius: 4px;
}

.no-description {
    color: #ccc;
    font-size: 90%;
}

/* Namespaces page */

.namespaces {
    clear: both;
}

.namespaces .namespace-container {
    float: left;
    margin: 0 14px 14px 0;
    min-width: 30%;
}

.namespaces h2 {
    margin: 0 0 20px 0;
}

@media (max-width: 991px) {
    .namespaces .namespace-container {
        margin-right: 0;
        width: 100%;
    }
}

/** Code and pre tags **/

tt, code, pre {
    font-family: 'Roboto Mono', Consolas, Menlo, Monaco, "Lucida Console", "Liberation Mono", "DejaVu Sans Mono", "Bitstream Vera Sans Mono", "Courier New", monospace, serif;
}

code {
    padding: 0;
    padding-top: 0.2em;
    padding-bottom: 0.2em;
    margin: 0;
    font-size: 85%;
    background-color: rgba(0, 0, 0, 0.04);
    border-radius: 3px;
    color: #333;
}

pre {
    padding: 16px;
    overflow: auto;
    font-size: 85%;
    line-height: 1.45;
    background-color: #f7f7f7;
    border-radius: 3px;
}

h2 {
    padding: 4px 4px 4px 8px;
    font-size: 25px;
    margin: 20px 0;
}

/** Doc index **/

dt {
    font-weight: normal;
}

dd {
    margin-left: 30px;
    line-height: 1.5em;
}

#doc-index h2 {
    font-weight: bold;
    margin: 30px 0;
}

#doc-index .pagination {
    margin: 0;
}

/* Search page */

.search-results {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.search-results li {
    list-style-type: none;
    margin: 0;
    padding: 14px 0;
    border-bottom: 1px solid #ccc;
}

.search-results h2 {
    background: none;
    margin: 0;
    padding: 0;
    font-size: 18px;
}

.search-results h2 a {
    float: left;
    display: block;
    margin: 0 0 4px 0;
}

.search-results .search-type {
    float: right;
    margin: 0 0 4px 0;
}

.search-results .search-from {
    margin: 0 0 12px 0;
    font-size: 12px;
    color: #999;
}

.search-results .search-from a {
    font-style: italic;
}

.search-results .search-description {
    margin: 8px 0 0 30px;
}
