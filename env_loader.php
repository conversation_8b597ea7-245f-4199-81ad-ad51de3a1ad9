<?php
/**
 * Simple environment variable loader
 * Loads variables from .env file into $_ENV superglobal
 */
function loadEnv($path = '.env') {
    if (!file_exists($path)) {
        return;
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        // Skip comments and empty lines
        if (empty($line) || $line[0] === '#' || $line[0] === ';') {
            continue;
        }
        
        // Split on first = only
        $pos = strpos($line, '=');
        if ($pos === false) {
            continue;
        }
        
        $key = trim(substr($line, 0, $pos));
        $value = trim(substr($line, $pos + 1));
        
        // Remove quotes if present
        if (($value[0] === '"' && $value[-1] === '"') || 
            ($value[0] === "'" && $value[-1] === "'")) {
            $value = substr($value, 1, -1);
        }
        
        $_ENV[$key] = $value;
    }
}
?>