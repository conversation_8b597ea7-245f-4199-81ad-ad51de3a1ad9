<?php
/**
 * ResellerApi
 * PHP version 5
 *
 * @category Class
 * @package  Brevo\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * Brevo API
 *
 * Brevo provide a RESTFul API that can be used with any languages. With this API, you will be able to :   - Manage your campaigns and get the statistics   - Manage your contacts   - Send transactional Emails and SMS   - and much more...  You can download our wrappers at https://github.com/orgs/brevo  **Possible responses**   | Code | Message |   | :-------------: | ------------- |   | 200  | OK. Successful Request  |   | 201  | OK. Successful Creation |   | 202  | OK. Request accepted |   | 204  | OK. Successful Update/Deletion  |   | 400  | Error. Bad Request  |   | 401  | Error. Authentication Needed  |   | 402  | Error. Not enough credit, plan upgrade needed  |   | 403  | Error. Permission denied  |   | 404  | Error. Object does not exist |   | 405  | Error. Method not allowed  |   | 406  | Error. Not Acceptable  |
 *
 * OpenAPI spec version: 3.0.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.29
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Brevo\Client\Api;

use GuzzleHttp\Client;
use GuzzleHttp\ClientInterface;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\MultipartStream;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\RequestOptions;
use Brevo\Client\ApiException;
use Brevo\Client\Configuration;
use Brevo\Client\HeaderSelector;
use Brevo\Client\ObjectSerializer;

/**
 * ResellerApi Class Doc Comment
 *
 * @category Class
 * @package  Brevo\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class ResellerApi
{
    /**
     * @var ClientInterface
     */
    protected $client;

    /**
     * @var Configuration
     */
    protected $config;

    /**
     * @var HeaderSelector
     */
    protected $headerSelector;

    /**
     * @param ClientInterface $client
     * @param Configuration   $config
     * @param HeaderSelector  $selector
     */
    public function __construct(
        ClientInterface $client = null,
        Configuration $config = null,
        HeaderSelector $selector = null
    ) {
        $this->client = $client ?: new Client();
        $this->config = $config ?: new Configuration();
        $this->headerSelector = $selector ?: new HeaderSelector();
    }

    /**
     * @return Configuration
     */
    public function getConfig()
    {
        return $this->config;
    }

    /**
     * Operation addCredits
     *
     * Add Email and/or SMS credits to a specific child account
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\AddCredits $addCredits Values to post to add credit to a specific child account (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return \Brevo\Client\Model\RemainingCreditModel
     */
    public function addCredits($childIdentifier, $addCredits)
    {
        list($response) = $this->addCreditsWithHttpInfo($childIdentifier, $addCredits);
        return $response;
    }

    /**
     * Operation addCreditsWithHttpInfo
     *
     * Add Email and/or SMS credits to a specific child account
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\AddCredits $addCredits Values to post to add credit to a specific child account (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of \Brevo\Client\Model\RemainingCreditModel, HTTP status code, HTTP response headers (array of strings)
     */
    public function addCreditsWithHttpInfo($childIdentifier, $addCredits)
    {
        $returnType = '\Brevo\Client\Model\RemainingCreditModel';
        $request = $this->addCreditsRequest($childIdentifier, $addCredits);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            $responseBody = $response->getBody();
            if ($returnType === '\SplFileObject') {
                $content = $responseBody; //stream goes to serializer
            } else {
                $content = $responseBody->getContents();
                if ($returnType !== 'string') {
                    $content = json_decode($content);
                }
            }

            return [
                ObjectSerializer::deserialize($content, $returnType, []),
                $response->getStatusCode(),
                $response->getHeaders()
            ];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 200:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\RemainingCreditModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 400:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 403:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation addCreditsAsync
     *
     * Add Email and/or SMS credits to a specific child account
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\AddCredits $addCredits Values to post to add credit to a specific child account (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function addCreditsAsync($childIdentifier, $addCredits)
    {
        return $this->addCreditsAsyncWithHttpInfo($childIdentifier, $addCredits)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation addCreditsAsyncWithHttpInfo
     *
     * Add Email and/or SMS credits to a specific child account
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\AddCredits $addCredits Values to post to add credit to a specific child account (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function addCreditsAsyncWithHttpInfo($childIdentifier, $addCredits)
    {
        $returnType = '\Brevo\Client\Model\RemainingCreditModel';
        $request = $this->addCreditsRequest($childIdentifier, $addCredits);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    $responseBody = $response->getBody();
                    if ($returnType === '\SplFileObject') {
                        $content = $responseBody; //stream goes to serializer
                    } else {
                        $content = $responseBody->getContents();
                        if ($returnType !== 'string') {
                            $content = json_decode($content);
                        }
                    }

                    return [
                        ObjectSerializer::deserialize($content, $returnType, []),
                        $response->getStatusCode(),
                        $response->getHeaders()
                    ];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'addCredits'
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\AddCredits $addCredits Values to post to add credit to a specific child account (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function addCreditsRequest($childIdentifier, $addCredits)
    {
        // verify the required parameter 'childIdentifier' is set
        if ($childIdentifier === null || (is_array($childIdentifier) && count($childIdentifier) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $childIdentifier when calling addCredits'
            );
        }
        // verify the required parameter 'addCredits' is set
        if ($addCredits === null || (is_array($addCredits) && count($addCredits) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $addCredits when calling addCredits'
            );
        }

        $resourcePath = '/reseller/children/{childIdentifier}/credits/add';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($childIdentifier !== null) {
            $resourcePath = str_replace(
                '{' . 'childIdentifier' . '}',
                ObjectSerializer::toPathValue($childIdentifier),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;
        if (isset($addCredits)) {
            $_tempBody = $addCredits;
        }

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            
            if($headers['Content-Type'] === 'application/json') {
                // \stdClass has no __toString(), so we should encode it manually
                if ($httpBody instanceof \stdClass) {
                    $httpBody = \GuzzleHttp\json_encode($httpBody);
                }
                // array has no __toString(), so we should encode it manually
                if(is_array($httpBody)) {
                    $httpBody = \GuzzleHttp\json_encode(ObjectSerializer::sanitizeForSerialization($httpBody));
                }
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\Query::build($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('api-key');
        if ($apiKey !== null) {
            $headers['api-key'] = $apiKey;
        }
        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('partner-key');
        if ($apiKey !== null) {
            $headers['partner-key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\Query::build($queryParams);
        return new Request(
            'POST',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation associateIpToChild
     *
     * Associate a dedicated IP to the child
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\ManageIp $ip IP to associate (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return void
     */
    public function associateIpToChild($childIdentifier, $ip)
    {
        $this->associateIpToChildWithHttpInfo($childIdentifier, $ip);
    }

    /**
     * Operation associateIpToChildWithHttpInfo
     *
     * Associate a dedicated IP to the child
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\ManageIp $ip IP to associate (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of null, HTTP status code, HTTP response headers (array of strings)
     */
    public function associateIpToChildWithHttpInfo($childIdentifier, $ip)
    {
        $returnType = '';
        $request = $this->associateIpToChildRequest($childIdentifier, $ip);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            return [null, $statusCode, $response->getHeaders()];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 400:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation associateIpToChildAsync
     *
     * Associate a dedicated IP to the child
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\ManageIp $ip IP to associate (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function associateIpToChildAsync($childIdentifier, $ip)
    {
        return $this->associateIpToChildAsyncWithHttpInfo($childIdentifier, $ip)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation associateIpToChildAsyncWithHttpInfo
     *
     * Associate a dedicated IP to the child
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\ManageIp $ip IP to associate (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function associateIpToChildAsyncWithHttpInfo($childIdentifier, $ip)
    {
        $returnType = '';
        $request = $this->associateIpToChildRequest($childIdentifier, $ip);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    return [null, $response->getStatusCode(), $response->getHeaders()];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'associateIpToChild'
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\ManageIp $ip IP to associate (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function associateIpToChildRequest($childIdentifier, $ip)
    {
        // verify the required parameter 'childIdentifier' is set
        if ($childIdentifier === null || (is_array($childIdentifier) && count($childIdentifier) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $childIdentifier when calling associateIpToChild'
            );
        }
        // verify the required parameter 'ip' is set
        if ($ip === null || (is_array($ip) && count($ip) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $ip when calling associateIpToChild'
            );
        }

        $resourcePath = '/reseller/children/{childIdentifier}/ips/associate';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($childIdentifier !== null) {
            $resourcePath = str_replace(
                '{' . 'childIdentifier' . '}',
                ObjectSerializer::toPathValue($childIdentifier),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;
        if (isset($ip)) {
            $_tempBody = $ip;
        }

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            
            if($headers['Content-Type'] === 'application/json') {
                // \stdClass has no __toString(), so we should encode it manually
                if ($httpBody instanceof \stdClass) {
                    $httpBody = \GuzzleHttp\json_encode($httpBody);
                }
                // array has no __toString(), so we should encode it manually
                if(is_array($httpBody)) {
                    $httpBody = \GuzzleHttp\json_encode(ObjectSerializer::sanitizeForSerialization($httpBody));
                }
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\Query::build($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('api-key');
        if ($apiKey !== null) {
            $headers['api-key'] = $apiKey;
        }
        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('partner-key');
        if ($apiKey !== null) {
            $headers['partner-key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\Query::build($queryParams);
        return new Request(
            'POST',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation createChildDomain
     *
     * Create a domain for a child account
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\AddChildDomain $addChildDomain Sender domain to add for a specific child account. This will not be displayed to the parent account. (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return void
     */
    public function createChildDomain($childIdentifier, $addChildDomain)
    {
        $this->createChildDomainWithHttpInfo($childIdentifier, $addChildDomain);
    }

    /**
     * Operation createChildDomainWithHttpInfo
     *
     * Create a domain for a child account
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\AddChildDomain $addChildDomain Sender domain to add for a specific child account. This will not be displayed to the parent account. (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of null, HTTP status code, HTTP response headers (array of strings)
     */
    public function createChildDomainWithHttpInfo($childIdentifier, $addChildDomain)
    {
        $returnType = '';
        $request = $this->createChildDomainRequest($childIdentifier, $addChildDomain);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            return [null, $statusCode, $response->getHeaders()];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 400:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 403:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation createChildDomainAsync
     *
     * Create a domain for a child account
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\AddChildDomain $addChildDomain Sender domain to add for a specific child account. This will not be displayed to the parent account. (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function createChildDomainAsync($childIdentifier, $addChildDomain)
    {
        return $this->createChildDomainAsyncWithHttpInfo($childIdentifier, $addChildDomain)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation createChildDomainAsyncWithHttpInfo
     *
     * Create a domain for a child account
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\AddChildDomain $addChildDomain Sender domain to add for a specific child account. This will not be displayed to the parent account. (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function createChildDomainAsyncWithHttpInfo($childIdentifier, $addChildDomain)
    {
        $returnType = '';
        $request = $this->createChildDomainRequest($childIdentifier, $addChildDomain);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    return [null, $response->getStatusCode(), $response->getHeaders()];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'createChildDomain'
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\AddChildDomain $addChildDomain Sender domain to add for a specific child account. This will not be displayed to the parent account. (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function createChildDomainRequest($childIdentifier, $addChildDomain)
    {
        // verify the required parameter 'childIdentifier' is set
        if ($childIdentifier === null || (is_array($childIdentifier) && count($childIdentifier) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $childIdentifier when calling createChildDomain'
            );
        }
        // verify the required parameter 'addChildDomain' is set
        if ($addChildDomain === null || (is_array($addChildDomain) && count($addChildDomain) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $addChildDomain when calling createChildDomain'
            );
        }

        $resourcePath = '/reseller/children/{childIdentifier}/domains';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($childIdentifier !== null) {
            $resourcePath = str_replace(
                '{' . 'childIdentifier' . '}',
                ObjectSerializer::toPathValue($childIdentifier),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;
        if (isset($addChildDomain)) {
            $_tempBody = $addChildDomain;
        }

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            
            if($headers['Content-Type'] === 'application/json') {
                // \stdClass has no __toString(), so we should encode it manually
                if ($httpBody instanceof \stdClass) {
                    $httpBody = \GuzzleHttp\json_encode($httpBody);
                }
                // array has no __toString(), so we should encode it manually
                if(is_array($httpBody)) {
                    $httpBody = \GuzzleHttp\json_encode(ObjectSerializer::sanitizeForSerialization($httpBody));
                }
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\Query::build($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('api-key');
        if ($apiKey !== null) {
            $headers['api-key'] = $apiKey;
        }
        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('partner-key');
        if ($apiKey !== null) {
            $headers['partner-key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\Query::build($queryParams);
        return new Request(
            'POST',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation createResellerChild
     *
     * Creates a reseller child
     *
     * @param  \Brevo\Client\Model\CreateChild $resellerChild reseller child to add (optional)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return \Brevo\Client\Model\CreateReseller
     */
    public function createResellerChild($resellerChild = null)
    {
        list($response) = $this->createResellerChildWithHttpInfo($resellerChild);
        return $response;
    }

    /**
     * Operation createResellerChildWithHttpInfo
     *
     * Creates a reseller child
     *
     * @param  \Brevo\Client\Model\CreateChild $resellerChild reseller child to add (optional)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of \Brevo\Client\Model\CreateReseller, HTTP status code, HTTP response headers (array of strings)
     */
    public function createResellerChildWithHttpInfo($resellerChild = null)
    {
        $returnType = '\Brevo\Client\Model\CreateReseller';
        $request = $this->createResellerChildRequest($resellerChild);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            $responseBody = $response->getBody();
            if ($returnType === '\SplFileObject') {
                $content = $responseBody; //stream goes to serializer
            } else {
                $content = $responseBody->getContents();
                if ($returnType !== 'string') {
                    $content = json_decode($content);
                }
            }

            return [
                ObjectSerializer::deserialize($content, $returnType, []),
                $response->getStatusCode(),
                $response->getHeaders()
            ];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 201:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\CreateReseller',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 400:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 403:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation createResellerChildAsync
     *
     * Creates a reseller child
     *
     * @param  \Brevo\Client\Model\CreateChild $resellerChild reseller child to add (optional)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function createResellerChildAsync($resellerChild = null)
    {
        return $this->createResellerChildAsyncWithHttpInfo($resellerChild)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation createResellerChildAsyncWithHttpInfo
     *
     * Creates a reseller child
     *
     * @param  \Brevo\Client\Model\CreateChild $resellerChild reseller child to add (optional)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function createResellerChildAsyncWithHttpInfo($resellerChild = null)
    {
        $returnType = '\Brevo\Client\Model\CreateReseller';
        $request = $this->createResellerChildRequest($resellerChild);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    $responseBody = $response->getBody();
                    if ($returnType === '\SplFileObject') {
                        $content = $responseBody; //stream goes to serializer
                    } else {
                        $content = $responseBody->getContents();
                        if ($returnType !== 'string') {
                            $content = json_decode($content);
                        }
                    }

                    return [
                        ObjectSerializer::deserialize($content, $returnType, []),
                        $response->getStatusCode(),
                        $response->getHeaders()
                    ];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'createResellerChild'
     *
     * @param  \Brevo\Client\Model\CreateChild $resellerChild reseller child to add (optional)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function createResellerChildRequest($resellerChild = null)
    {

        $resourcePath = '/reseller/children';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;



        // body params
        $_tempBody = null;
        if (isset($resellerChild)) {
            $_tempBody = $resellerChild;
        }

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            
            if($headers['Content-Type'] === 'application/json') {
                // \stdClass has no __toString(), so we should encode it manually
                if ($httpBody instanceof \stdClass) {
                    $httpBody = \GuzzleHttp\json_encode($httpBody);
                }
                // array has no __toString(), so we should encode it manually
                if(is_array($httpBody)) {
                    $httpBody = \GuzzleHttp\json_encode(ObjectSerializer::sanitizeForSerialization($httpBody));
                }
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\Query::build($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('api-key');
        if ($apiKey !== null) {
            $headers['api-key'] = $apiKey;
        }
        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('partner-key');
        if ($apiKey !== null) {
            $headers['partner-key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\Query::build($queryParams);
        return new Request(
            'POST',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation deleteChildDomain
     *
     * Delete the sender domain of the reseller child based on the childIdentifier and domainName passed
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  string $domainName Pass the existing domain that needs to be deleted (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return void
     */
    public function deleteChildDomain($childIdentifier, $domainName)
    {
        $this->deleteChildDomainWithHttpInfo($childIdentifier, $domainName);
    }

    /**
     * Operation deleteChildDomainWithHttpInfo
     *
     * Delete the sender domain of the reseller child based on the childIdentifier and domainName passed
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  string $domainName Pass the existing domain that needs to be deleted (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of null, HTTP status code, HTTP response headers (array of strings)
     */
    public function deleteChildDomainWithHttpInfo($childIdentifier, $domainName)
    {
        $returnType = '';
        $request = $this->deleteChildDomainRequest($childIdentifier, $domainName);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            return [null, $statusCode, $response->getHeaders()];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 403:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 400:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation deleteChildDomainAsync
     *
     * Delete the sender domain of the reseller child based on the childIdentifier and domainName passed
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  string $domainName Pass the existing domain that needs to be deleted (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function deleteChildDomainAsync($childIdentifier, $domainName)
    {
        return $this->deleteChildDomainAsyncWithHttpInfo($childIdentifier, $domainName)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation deleteChildDomainAsyncWithHttpInfo
     *
     * Delete the sender domain of the reseller child based on the childIdentifier and domainName passed
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  string $domainName Pass the existing domain that needs to be deleted (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function deleteChildDomainAsyncWithHttpInfo($childIdentifier, $domainName)
    {
        $returnType = '';
        $request = $this->deleteChildDomainRequest($childIdentifier, $domainName);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    return [null, $response->getStatusCode(), $response->getHeaders()];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'deleteChildDomain'
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  string $domainName Pass the existing domain that needs to be deleted (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function deleteChildDomainRequest($childIdentifier, $domainName)
    {
        // verify the required parameter 'childIdentifier' is set
        if ($childIdentifier === null || (is_array($childIdentifier) && count($childIdentifier) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $childIdentifier when calling deleteChildDomain'
            );
        }
        // verify the required parameter 'domainName' is set
        if ($domainName === null || (is_array($domainName) && count($domainName) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $domainName when calling deleteChildDomain'
            );
        }

        $resourcePath = '/reseller/children/{childIdentifier}/domains/{domainName}';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($childIdentifier !== null) {
            $resourcePath = str_replace(
                '{' . 'childIdentifier' . '}',
                ObjectSerializer::toPathValue($childIdentifier),
                $resourcePath
            );
        }
        // path params
        if ($domainName !== null) {
            $resourcePath = str_replace(
                '{' . 'domainName' . '}',
                ObjectSerializer::toPathValue($domainName),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            
            if($headers['Content-Type'] === 'application/json') {
                // \stdClass has no __toString(), so we should encode it manually
                if ($httpBody instanceof \stdClass) {
                    $httpBody = \GuzzleHttp\json_encode($httpBody);
                }
                // array has no __toString(), so we should encode it manually
                if(is_array($httpBody)) {
                    $httpBody = \GuzzleHttp\json_encode(ObjectSerializer::sanitizeForSerialization($httpBody));
                }
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\Query::build($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('api-key');
        if ($apiKey !== null) {
            $headers['api-key'] = $apiKey;
        }
        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('partner-key');
        if ($apiKey !== null) {
            $headers['partner-key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\Query::build($queryParams);
        return new Request(
            'DELETE',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation deleteResellerChild
     *
     * Delete a single reseller child based on the child identifier supplied
     *
     * @param  string $childIdentifier Either auth key or child id of reseller&#39;s child (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return void
     */
    public function deleteResellerChild($childIdentifier)
    {
        $this->deleteResellerChildWithHttpInfo($childIdentifier);
    }

    /**
     * Operation deleteResellerChildWithHttpInfo
     *
     * Delete a single reseller child based on the child identifier supplied
     *
     * @param  string $childIdentifier Either auth key or child id of reseller&#39;s child (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of null, HTTP status code, HTTP response headers (array of strings)
     */
    public function deleteResellerChildWithHttpInfo($childIdentifier)
    {
        $returnType = '';
        $request = $this->deleteResellerChildRequest($childIdentifier);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            return [null, $statusCode, $response->getHeaders()];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 403:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 400:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation deleteResellerChildAsync
     *
     * Delete a single reseller child based on the child identifier supplied
     *
     * @param  string $childIdentifier Either auth key or child id of reseller&#39;s child (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function deleteResellerChildAsync($childIdentifier)
    {
        return $this->deleteResellerChildAsyncWithHttpInfo($childIdentifier)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation deleteResellerChildAsyncWithHttpInfo
     *
     * Delete a single reseller child based on the child identifier supplied
     *
     * @param  string $childIdentifier Either auth key or child id of reseller&#39;s child (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function deleteResellerChildAsyncWithHttpInfo($childIdentifier)
    {
        $returnType = '';
        $request = $this->deleteResellerChildRequest($childIdentifier);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    return [null, $response->getStatusCode(), $response->getHeaders()];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'deleteResellerChild'
     *
     * @param  string $childIdentifier Either auth key or child id of reseller&#39;s child (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function deleteResellerChildRequest($childIdentifier)
    {
        // verify the required parameter 'childIdentifier' is set
        if ($childIdentifier === null || (is_array($childIdentifier) && count($childIdentifier) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $childIdentifier when calling deleteResellerChild'
            );
        }

        $resourcePath = '/reseller/children/{childIdentifier}';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($childIdentifier !== null) {
            $resourcePath = str_replace(
                '{' . 'childIdentifier' . '}',
                ObjectSerializer::toPathValue($childIdentifier),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            
            if($headers['Content-Type'] === 'application/json') {
                // \stdClass has no __toString(), so we should encode it manually
                if ($httpBody instanceof \stdClass) {
                    $httpBody = \GuzzleHttp\json_encode($httpBody);
                }
                // array has no __toString(), so we should encode it manually
                if(is_array($httpBody)) {
                    $httpBody = \GuzzleHttp\json_encode(ObjectSerializer::sanitizeForSerialization($httpBody));
                }
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\Query::build($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('api-key');
        if ($apiKey !== null) {
            $headers['api-key'] = $apiKey;
        }
        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('partner-key');
        if ($apiKey !== null) {
            $headers['partner-key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\Query::build($queryParams);
        return new Request(
            'DELETE',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation dissociateIpFromChild
     *
     * Dissociate a dedicated IP to the child
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\ManageIp $ip IP to dissociate (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return void
     */
    public function dissociateIpFromChild($childIdentifier, $ip)
    {
        $this->dissociateIpFromChildWithHttpInfo($childIdentifier, $ip);
    }

    /**
     * Operation dissociateIpFromChildWithHttpInfo
     *
     * Dissociate a dedicated IP to the child
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\ManageIp $ip IP to dissociate (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of null, HTTP status code, HTTP response headers (array of strings)
     */
    public function dissociateIpFromChildWithHttpInfo($childIdentifier, $ip)
    {
        $returnType = '';
        $request = $this->dissociateIpFromChildRequest($childIdentifier, $ip);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            return [null, $statusCode, $response->getHeaders()];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 400:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation dissociateIpFromChildAsync
     *
     * Dissociate a dedicated IP to the child
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\ManageIp $ip IP to dissociate (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function dissociateIpFromChildAsync($childIdentifier, $ip)
    {
        return $this->dissociateIpFromChildAsyncWithHttpInfo($childIdentifier, $ip)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation dissociateIpFromChildAsyncWithHttpInfo
     *
     * Dissociate a dedicated IP to the child
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\ManageIp $ip IP to dissociate (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function dissociateIpFromChildAsyncWithHttpInfo($childIdentifier, $ip)
    {
        $returnType = '';
        $request = $this->dissociateIpFromChildRequest($childIdentifier, $ip);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    return [null, $response->getStatusCode(), $response->getHeaders()];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'dissociateIpFromChild'
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\ManageIp $ip IP to dissociate (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function dissociateIpFromChildRequest($childIdentifier, $ip)
    {
        // verify the required parameter 'childIdentifier' is set
        if ($childIdentifier === null || (is_array($childIdentifier) && count($childIdentifier) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $childIdentifier when calling dissociateIpFromChild'
            );
        }
        // verify the required parameter 'ip' is set
        if ($ip === null || (is_array($ip) && count($ip) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $ip when calling dissociateIpFromChild'
            );
        }

        $resourcePath = '/reseller/children/{childIdentifier}/ips/dissociate';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($childIdentifier !== null) {
            $resourcePath = str_replace(
                '{' . 'childIdentifier' . '}',
                ObjectSerializer::toPathValue($childIdentifier),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;
        if (isset($ip)) {
            $_tempBody = $ip;
        }

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            
            if($headers['Content-Type'] === 'application/json') {
                // \stdClass has no __toString(), so we should encode it manually
                if ($httpBody instanceof \stdClass) {
                    $httpBody = \GuzzleHttp\json_encode($httpBody);
                }
                // array has no __toString(), so we should encode it manually
                if(is_array($httpBody)) {
                    $httpBody = \GuzzleHttp\json_encode(ObjectSerializer::sanitizeForSerialization($httpBody));
                }
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\Query::build($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('api-key');
        if ($apiKey !== null) {
            $headers['api-key'] = $apiKey;
        }
        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('partner-key');
        if ($apiKey !== null) {
            $headers['partner-key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\Query::build($queryParams);
        return new Request(
            'POST',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation getChildAccountCreationStatus
     *
     * Get the status of a reseller's child account creation, whether it is successfully created (exists) or not based on the identifier supplied
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return \Brevo\Client\Model\GetChildAccountCreationStatus
     */
    public function getChildAccountCreationStatus($childIdentifier)
    {
        list($response) = $this->getChildAccountCreationStatusWithHttpInfo($childIdentifier);
        return $response;
    }

    /**
     * Operation getChildAccountCreationStatusWithHttpInfo
     *
     * Get the status of a reseller's child account creation, whether it is successfully created (exists) or not based on the identifier supplied
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of \Brevo\Client\Model\GetChildAccountCreationStatus, HTTP status code, HTTP response headers (array of strings)
     */
    public function getChildAccountCreationStatusWithHttpInfo($childIdentifier)
    {
        $returnType = '\Brevo\Client\Model\GetChildAccountCreationStatus';
        $request = $this->getChildAccountCreationStatusRequest($childIdentifier);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            $responseBody = $response->getBody();
            if ($returnType === '\SplFileObject') {
                $content = $responseBody; //stream goes to serializer
            } else {
                $content = $responseBody->getContents();
                if ($returnType !== 'string') {
                    $content = json_decode($content);
                }
            }

            return [
                ObjectSerializer::deserialize($content, $returnType, []),
                $response->getStatusCode(),
                $response->getHeaders()
            ];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 200:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\GetChildAccountCreationStatus',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 400:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 403:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation getChildAccountCreationStatusAsync
     *
     * Get the status of a reseller's child account creation, whether it is successfully created (exists) or not based on the identifier supplied
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function getChildAccountCreationStatusAsync($childIdentifier)
    {
        return $this->getChildAccountCreationStatusAsyncWithHttpInfo($childIdentifier)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation getChildAccountCreationStatusAsyncWithHttpInfo
     *
     * Get the status of a reseller's child account creation, whether it is successfully created (exists) or not based on the identifier supplied
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function getChildAccountCreationStatusAsyncWithHttpInfo($childIdentifier)
    {
        $returnType = '\Brevo\Client\Model\GetChildAccountCreationStatus';
        $request = $this->getChildAccountCreationStatusRequest($childIdentifier);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    $responseBody = $response->getBody();
                    if ($returnType === '\SplFileObject') {
                        $content = $responseBody; //stream goes to serializer
                    } else {
                        $content = $responseBody->getContents();
                        if ($returnType !== 'string') {
                            $content = json_decode($content);
                        }
                    }

                    return [
                        ObjectSerializer::deserialize($content, $returnType, []),
                        $response->getStatusCode(),
                        $response->getHeaders()
                    ];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'getChildAccountCreationStatus'
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function getChildAccountCreationStatusRequest($childIdentifier)
    {
        // verify the required parameter 'childIdentifier' is set
        if ($childIdentifier === null || (is_array($childIdentifier) && count($childIdentifier) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $childIdentifier when calling getChildAccountCreationStatus'
            );
        }

        $resourcePath = '/reseller/children/{childIdentifier}/accountCreationStatus';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($childIdentifier !== null) {
            $resourcePath = str_replace(
                '{' . 'childIdentifier' . '}',
                ObjectSerializer::toPathValue($childIdentifier),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            
            if($headers['Content-Type'] === 'application/json') {
                // \stdClass has no __toString(), so we should encode it manually
                if ($httpBody instanceof \stdClass) {
                    $httpBody = \GuzzleHttp\json_encode($httpBody);
                }
                // array has no __toString(), so we should encode it manually
                if(is_array($httpBody)) {
                    $httpBody = \GuzzleHttp\json_encode(ObjectSerializer::sanitizeForSerialization($httpBody));
                }
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\Query::build($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('api-key');
        if ($apiKey !== null) {
            $headers['api-key'] = $apiKey;
        }
        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('partner-key');
        if ($apiKey !== null) {
            $headers['partner-key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\Query::build($queryParams);
        return new Request(
            'GET',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation getChildDomains
     *
     * Get all sender domains for a specific child account
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return \Brevo\Client\Model\GetChildDomains
     */
    public function getChildDomains($childIdentifier)
    {
        list($response) = $this->getChildDomainsWithHttpInfo($childIdentifier);
        return $response;
    }

    /**
     * Operation getChildDomainsWithHttpInfo
     *
     * Get all sender domains for a specific child account
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of \Brevo\Client\Model\GetChildDomains, HTTP status code, HTTP response headers (array of strings)
     */
    public function getChildDomainsWithHttpInfo($childIdentifier)
    {
        $returnType = '\Brevo\Client\Model\GetChildDomains';
        $request = $this->getChildDomainsRequest($childIdentifier);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            $responseBody = $response->getBody();
            if ($returnType === '\SplFileObject') {
                $content = $responseBody; //stream goes to serializer
            } else {
                $content = $responseBody->getContents();
                if ($returnType !== 'string') {
                    $content = json_decode($content);
                }
            }

            return [
                ObjectSerializer::deserialize($content, $returnType, []),
                $response->getStatusCode(),
                $response->getHeaders()
            ];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 200:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\GetChildDomains',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 403:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 400:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation getChildDomainsAsync
     *
     * Get all sender domains for a specific child account
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function getChildDomainsAsync($childIdentifier)
    {
        return $this->getChildDomainsAsyncWithHttpInfo($childIdentifier)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation getChildDomainsAsyncWithHttpInfo
     *
     * Get all sender domains for a specific child account
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function getChildDomainsAsyncWithHttpInfo($childIdentifier)
    {
        $returnType = '\Brevo\Client\Model\GetChildDomains';
        $request = $this->getChildDomainsRequest($childIdentifier);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    $responseBody = $response->getBody();
                    if ($returnType === '\SplFileObject') {
                        $content = $responseBody; //stream goes to serializer
                    } else {
                        $content = $responseBody->getContents();
                        if ($returnType !== 'string') {
                            $content = json_decode($content);
                        }
                    }

                    return [
                        ObjectSerializer::deserialize($content, $returnType, []),
                        $response->getStatusCode(),
                        $response->getHeaders()
                    ];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'getChildDomains'
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function getChildDomainsRequest($childIdentifier)
    {
        // verify the required parameter 'childIdentifier' is set
        if ($childIdentifier === null || (is_array($childIdentifier) && count($childIdentifier) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $childIdentifier when calling getChildDomains'
            );
        }

        $resourcePath = '/reseller/children/{childIdentifier}/domains';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($childIdentifier !== null) {
            $resourcePath = str_replace(
                '{' . 'childIdentifier' . '}',
                ObjectSerializer::toPathValue($childIdentifier),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            
            if($headers['Content-Type'] === 'application/json') {
                // \stdClass has no __toString(), so we should encode it manually
                if ($httpBody instanceof \stdClass) {
                    $httpBody = \GuzzleHttp\json_encode($httpBody);
                }
                // array has no __toString(), so we should encode it manually
                if(is_array($httpBody)) {
                    $httpBody = \GuzzleHttp\json_encode(ObjectSerializer::sanitizeForSerialization($httpBody));
                }
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\Query::build($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('api-key');
        if ($apiKey !== null) {
            $headers['api-key'] = $apiKey;
        }
        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('partner-key');
        if ($apiKey !== null) {
            $headers['partner-key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\Query::build($queryParams);
        return new Request(
            'GET',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation getChildInfo
     *
     * Get a child account's details
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return \Brevo\Client\Model\GetChildInfo
     */
    public function getChildInfo($childIdentifier)
    {
        list($response) = $this->getChildInfoWithHttpInfo($childIdentifier);
        return $response;
    }

    /**
     * Operation getChildInfoWithHttpInfo
     *
     * Get a child account's details
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of \Brevo\Client\Model\GetChildInfo, HTTP status code, HTTP response headers (array of strings)
     */
    public function getChildInfoWithHttpInfo($childIdentifier)
    {
        $returnType = '\Brevo\Client\Model\GetChildInfo';
        $request = $this->getChildInfoRequest($childIdentifier);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            $responseBody = $response->getBody();
            if ($returnType === '\SplFileObject') {
                $content = $responseBody; //stream goes to serializer
            } else {
                $content = $responseBody->getContents();
                if ($returnType !== 'string') {
                    $content = json_decode($content);
                }
            }

            return [
                ObjectSerializer::deserialize($content, $returnType, []),
                $response->getStatusCode(),
                $response->getHeaders()
            ];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 200:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\GetChildInfo',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 403:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 400:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation getChildInfoAsync
     *
     * Get a child account's details
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function getChildInfoAsync($childIdentifier)
    {
        return $this->getChildInfoAsyncWithHttpInfo($childIdentifier)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation getChildInfoAsyncWithHttpInfo
     *
     * Get a child account's details
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function getChildInfoAsyncWithHttpInfo($childIdentifier)
    {
        $returnType = '\Brevo\Client\Model\GetChildInfo';
        $request = $this->getChildInfoRequest($childIdentifier);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    $responseBody = $response->getBody();
                    if ($returnType === '\SplFileObject') {
                        $content = $responseBody; //stream goes to serializer
                    } else {
                        $content = $responseBody->getContents();
                        if ($returnType !== 'string') {
                            $content = json_decode($content);
                        }
                    }

                    return [
                        ObjectSerializer::deserialize($content, $returnType, []),
                        $response->getStatusCode(),
                        $response->getHeaders()
                    ];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'getChildInfo'
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function getChildInfoRequest($childIdentifier)
    {
        // verify the required parameter 'childIdentifier' is set
        if ($childIdentifier === null || (is_array($childIdentifier) && count($childIdentifier) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $childIdentifier when calling getChildInfo'
            );
        }

        $resourcePath = '/reseller/children/{childIdentifier}';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($childIdentifier !== null) {
            $resourcePath = str_replace(
                '{' . 'childIdentifier' . '}',
                ObjectSerializer::toPathValue($childIdentifier),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            
            if($headers['Content-Type'] === 'application/json') {
                // \stdClass has no __toString(), so we should encode it manually
                if ($httpBody instanceof \stdClass) {
                    $httpBody = \GuzzleHttp\json_encode($httpBody);
                }
                // array has no __toString(), so we should encode it manually
                if(is_array($httpBody)) {
                    $httpBody = \GuzzleHttp\json_encode(ObjectSerializer::sanitizeForSerialization($httpBody));
                }
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\Query::build($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('api-key');
        if ($apiKey !== null) {
            $headers['api-key'] = $apiKey;
        }
        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('partner-key');
        if ($apiKey !== null) {
            $headers['partner-key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\Query::build($queryParams);
        return new Request(
            'GET',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation getResellerChilds
     *
     * Get the list of all children accounts
     *
     * @param  int $limit Number of documents for child accounts information per page (optional, default to 10)
     * @param  int $offset Index of the first document in the page (optional, default to 0)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return \Brevo\Client\Model\GetChildrenList
     */
    public function getResellerChilds($limit = '10', $offset = '0')
    {
        list($response) = $this->getResellerChildsWithHttpInfo($limit, $offset);
        return $response;
    }

    /**
     * Operation getResellerChildsWithHttpInfo
     *
     * Get the list of all children accounts
     *
     * @param  int $limit Number of documents for child accounts information per page (optional, default to 10)
     * @param  int $offset Index of the first document in the page (optional, default to 0)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of \Brevo\Client\Model\GetChildrenList, HTTP status code, HTTP response headers (array of strings)
     */
    public function getResellerChildsWithHttpInfo($limit = '10', $offset = '0')
    {
        $returnType = '\Brevo\Client\Model\GetChildrenList';
        $request = $this->getResellerChildsRequest($limit, $offset);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            $responseBody = $response->getBody();
            if ($returnType === '\SplFileObject') {
                $content = $responseBody; //stream goes to serializer
            } else {
                $content = $responseBody->getContents();
                if ($returnType !== 'string') {
                    $content = json_decode($content);
                }
            }

            return [
                ObjectSerializer::deserialize($content, $returnType, []),
                $response->getStatusCode(),
                $response->getHeaders()
            ];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 200:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\GetChildrenList',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 403:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation getResellerChildsAsync
     *
     * Get the list of all children accounts
     *
     * @param  int $limit Number of documents for child accounts information per page (optional, default to 10)
     * @param  int $offset Index of the first document in the page (optional, default to 0)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function getResellerChildsAsync($limit = '10', $offset = '0')
    {
        return $this->getResellerChildsAsyncWithHttpInfo($limit, $offset)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation getResellerChildsAsyncWithHttpInfo
     *
     * Get the list of all children accounts
     *
     * @param  int $limit Number of documents for child accounts information per page (optional, default to 10)
     * @param  int $offset Index of the first document in the page (optional, default to 0)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function getResellerChildsAsyncWithHttpInfo($limit = '10', $offset = '0')
    {
        $returnType = '\Brevo\Client\Model\GetChildrenList';
        $request = $this->getResellerChildsRequest($limit, $offset);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    $responseBody = $response->getBody();
                    if ($returnType === '\SplFileObject') {
                        $content = $responseBody; //stream goes to serializer
                    } else {
                        $content = $responseBody->getContents();
                        if ($returnType !== 'string') {
                            $content = json_decode($content);
                        }
                    }

                    return [
                        ObjectSerializer::deserialize($content, $returnType, []),
                        $response->getStatusCode(),
                        $response->getHeaders()
                    ];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'getResellerChilds'
     *
     * @param  int $limit Number of documents for child accounts information per page (optional, default to 10)
     * @param  int $offset Index of the first document in the page (optional, default to 0)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function getResellerChildsRequest($limit = '10', $offset = '0')
    {
        if ($limit !== null && $limit > 20) {
            throw new \InvalidArgumentException('invalid value for "$limit" when calling ResellerApi.getResellerChilds, must be smaller than or equal to 20.');
        }
        if ($limit !== null && $limit < 0) {
            throw new \InvalidArgumentException('invalid value for "$limit" when calling ResellerApi.getResellerChilds, must be bigger than or equal to 0.');
        }


        $resourcePath = '/reseller/children';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;

        // query params
        if ($limit !== null) {
            $queryParams['limit'] = ObjectSerializer::toQueryValue($limit);
        }
        // query params
        if ($offset !== null) {
            $queryParams['offset'] = ObjectSerializer::toQueryValue($offset);
        }


        // body params
        $_tempBody = null;

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            
            if($headers['Content-Type'] === 'application/json') {
                // \stdClass has no __toString(), so we should encode it manually
                if ($httpBody instanceof \stdClass) {
                    $httpBody = \GuzzleHttp\json_encode($httpBody);
                }
                // array has no __toString(), so we should encode it manually
                if(is_array($httpBody)) {
                    $httpBody = \GuzzleHttp\json_encode(ObjectSerializer::sanitizeForSerialization($httpBody));
                }
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\Query::build($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('api-key');
        if ($apiKey !== null) {
            $headers['api-key'] = $apiKey;
        }
        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('partner-key');
        if ($apiKey !== null) {
            $headers['partner-key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\Query::build($queryParams);
        return new Request(
            'GET',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation getSsoToken
     *
     * Get session token to access Brevo (SSO)
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return \Brevo\Client\Model\GetSsoToken
     */
    public function getSsoToken($childIdentifier)
    {
        list($response) = $this->getSsoTokenWithHttpInfo($childIdentifier);
        return $response;
    }

    /**
     * Operation getSsoTokenWithHttpInfo
     *
     * Get session token to access Brevo (SSO)
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of \Brevo\Client\Model\GetSsoToken, HTTP status code, HTTP response headers (array of strings)
     */
    public function getSsoTokenWithHttpInfo($childIdentifier)
    {
        $returnType = '\Brevo\Client\Model\GetSsoToken';
        $request = $this->getSsoTokenRequest($childIdentifier);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            $responseBody = $response->getBody();
            if ($returnType === '\SplFileObject') {
                $content = $responseBody; //stream goes to serializer
            } else {
                $content = $responseBody->getContents();
                if ($returnType !== 'string') {
                    $content = json_decode($content);
                }
            }

            return [
                ObjectSerializer::deserialize($content, $returnType, []),
                $response->getStatusCode(),
                $response->getHeaders()
            ];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 200:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\GetSsoToken',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 403:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 400:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation getSsoTokenAsync
     *
     * Get session token to access Brevo (SSO)
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function getSsoTokenAsync($childIdentifier)
    {
        return $this->getSsoTokenAsyncWithHttpInfo($childIdentifier)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation getSsoTokenAsyncWithHttpInfo
     *
     * Get session token to access Brevo (SSO)
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function getSsoTokenAsyncWithHttpInfo($childIdentifier)
    {
        $returnType = '\Brevo\Client\Model\GetSsoToken';
        $request = $this->getSsoTokenRequest($childIdentifier);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    $responseBody = $response->getBody();
                    if ($returnType === '\SplFileObject') {
                        $content = $responseBody; //stream goes to serializer
                    } else {
                        $content = $responseBody->getContents();
                        if ($returnType !== 'string') {
                            $content = json_decode($content);
                        }
                    }

                    return [
                        ObjectSerializer::deserialize($content, $returnType, []),
                        $response->getStatusCode(),
                        $response->getHeaders()
                    ];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'getSsoToken'
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function getSsoTokenRequest($childIdentifier)
    {
        // verify the required parameter 'childIdentifier' is set
        if ($childIdentifier === null || (is_array($childIdentifier) && count($childIdentifier) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $childIdentifier when calling getSsoToken'
            );
        }

        $resourcePath = '/reseller/children/{childIdentifier}/auth';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($childIdentifier !== null) {
            $resourcePath = str_replace(
                '{' . 'childIdentifier' . '}',
                ObjectSerializer::toPathValue($childIdentifier),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            
            if($headers['Content-Type'] === 'application/json') {
                // \stdClass has no __toString(), so we should encode it manually
                if ($httpBody instanceof \stdClass) {
                    $httpBody = \GuzzleHttp\json_encode($httpBody);
                }
                // array has no __toString(), so we should encode it manually
                if(is_array($httpBody)) {
                    $httpBody = \GuzzleHttp\json_encode(ObjectSerializer::sanitizeForSerialization($httpBody));
                }
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\Query::build($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('api-key');
        if ($apiKey !== null) {
            $headers['api-key'] = $apiKey;
        }
        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('partner-key');
        if ($apiKey !== null) {
            $headers['partner-key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\Query::build($queryParams);
        return new Request(
            'GET',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation removeCredits
     *
     * Remove Email and/or SMS credits from a specific child account
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\RemoveCredits $removeCredits Values to post to remove email or SMS credits from a specific child account (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return \Brevo\Client\Model\RemainingCreditModel
     */
    public function removeCredits($childIdentifier, $removeCredits)
    {
        list($response) = $this->removeCreditsWithHttpInfo($childIdentifier, $removeCredits);
        return $response;
    }

    /**
     * Operation removeCreditsWithHttpInfo
     *
     * Remove Email and/or SMS credits from a specific child account
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\RemoveCredits $removeCredits Values to post to remove email or SMS credits from a specific child account (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of \Brevo\Client\Model\RemainingCreditModel, HTTP status code, HTTP response headers (array of strings)
     */
    public function removeCreditsWithHttpInfo($childIdentifier, $removeCredits)
    {
        $returnType = '\Brevo\Client\Model\RemainingCreditModel';
        $request = $this->removeCreditsRequest($childIdentifier, $removeCredits);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            $responseBody = $response->getBody();
            if ($returnType === '\SplFileObject') {
                $content = $responseBody; //stream goes to serializer
            } else {
                $content = $responseBody->getContents();
                if ($returnType !== 'string') {
                    $content = json_decode($content);
                }
            }

            return [
                ObjectSerializer::deserialize($content, $returnType, []),
                $response->getStatusCode(),
                $response->getHeaders()
            ];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 200:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\RemainingCreditModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 400:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 403:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation removeCreditsAsync
     *
     * Remove Email and/or SMS credits from a specific child account
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\RemoveCredits $removeCredits Values to post to remove email or SMS credits from a specific child account (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function removeCreditsAsync($childIdentifier, $removeCredits)
    {
        return $this->removeCreditsAsyncWithHttpInfo($childIdentifier, $removeCredits)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation removeCreditsAsyncWithHttpInfo
     *
     * Remove Email and/or SMS credits from a specific child account
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\RemoveCredits $removeCredits Values to post to remove email or SMS credits from a specific child account (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function removeCreditsAsyncWithHttpInfo($childIdentifier, $removeCredits)
    {
        $returnType = '\Brevo\Client\Model\RemainingCreditModel';
        $request = $this->removeCreditsRequest($childIdentifier, $removeCredits);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    $responseBody = $response->getBody();
                    if ($returnType === '\SplFileObject') {
                        $content = $responseBody; //stream goes to serializer
                    } else {
                        $content = $responseBody->getContents();
                        if ($returnType !== 'string') {
                            $content = json_decode($content);
                        }
                    }

                    return [
                        ObjectSerializer::deserialize($content, $returnType, []),
                        $response->getStatusCode(),
                        $response->getHeaders()
                    ];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'removeCredits'
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\RemoveCredits $removeCredits Values to post to remove email or SMS credits from a specific child account (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function removeCreditsRequest($childIdentifier, $removeCredits)
    {
        // verify the required parameter 'childIdentifier' is set
        if ($childIdentifier === null || (is_array($childIdentifier) && count($childIdentifier) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $childIdentifier when calling removeCredits'
            );
        }
        // verify the required parameter 'removeCredits' is set
        if ($removeCredits === null || (is_array($removeCredits) && count($removeCredits) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $removeCredits when calling removeCredits'
            );
        }

        $resourcePath = '/reseller/children/{childIdentifier}/credits/remove';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($childIdentifier !== null) {
            $resourcePath = str_replace(
                '{' . 'childIdentifier' . '}',
                ObjectSerializer::toPathValue($childIdentifier),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;
        if (isset($removeCredits)) {
            $_tempBody = $removeCredits;
        }

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            
            if($headers['Content-Type'] === 'application/json') {
                // \stdClass has no __toString(), so we should encode it manually
                if ($httpBody instanceof \stdClass) {
                    $httpBody = \GuzzleHttp\json_encode($httpBody);
                }
                // array has no __toString(), so we should encode it manually
                if(is_array($httpBody)) {
                    $httpBody = \GuzzleHttp\json_encode(ObjectSerializer::sanitizeForSerialization($httpBody));
                }
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\Query::build($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('api-key');
        if ($apiKey !== null) {
            $headers['api-key'] = $apiKey;
        }
        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('partner-key');
        if ($apiKey !== null) {
            $headers['partner-key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\Query::build($queryParams);
        return new Request(
            'POST',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation updateChildAccountStatus
     *
     * Update info of reseller's child account status based on the childIdentifier supplied
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\UpdateChildAccountStatus $updateChildAccountStatus values to update in child account status (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return void
     */
    public function updateChildAccountStatus($childIdentifier, $updateChildAccountStatus)
    {
        $this->updateChildAccountStatusWithHttpInfo($childIdentifier, $updateChildAccountStatus);
    }

    /**
     * Operation updateChildAccountStatusWithHttpInfo
     *
     * Update info of reseller's child account status based on the childIdentifier supplied
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\UpdateChildAccountStatus $updateChildAccountStatus values to update in child account status (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of null, HTTP status code, HTTP response headers (array of strings)
     */
    public function updateChildAccountStatusWithHttpInfo($childIdentifier, $updateChildAccountStatus)
    {
        $returnType = '';
        $request = $this->updateChildAccountStatusRequest($childIdentifier, $updateChildAccountStatus);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            return [null, $statusCode, $response->getHeaders()];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 400:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 403:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation updateChildAccountStatusAsync
     *
     * Update info of reseller's child account status based on the childIdentifier supplied
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\UpdateChildAccountStatus $updateChildAccountStatus values to update in child account status (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function updateChildAccountStatusAsync($childIdentifier, $updateChildAccountStatus)
    {
        return $this->updateChildAccountStatusAsyncWithHttpInfo($childIdentifier, $updateChildAccountStatus)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation updateChildAccountStatusAsyncWithHttpInfo
     *
     * Update info of reseller's child account status based on the childIdentifier supplied
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\UpdateChildAccountStatus $updateChildAccountStatus values to update in child account status (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function updateChildAccountStatusAsyncWithHttpInfo($childIdentifier, $updateChildAccountStatus)
    {
        $returnType = '';
        $request = $this->updateChildAccountStatusRequest($childIdentifier, $updateChildAccountStatus);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    return [null, $response->getStatusCode(), $response->getHeaders()];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'updateChildAccountStatus'
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\UpdateChildAccountStatus $updateChildAccountStatus values to update in child account status (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function updateChildAccountStatusRequest($childIdentifier, $updateChildAccountStatus)
    {
        // verify the required parameter 'childIdentifier' is set
        if ($childIdentifier === null || (is_array($childIdentifier) && count($childIdentifier) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $childIdentifier when calling updateChildAccountStatus'
            );
        }
        // verify the required parameter 'updateChildAccountStatus' is set
        if ($updateChildAccountStatus === null || (is_array($updateChildAccountStatus) && count($updateChildAccountStatus) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $updateChildAccountStatus when calling updateChildAccountStatus'
            );
        }

        $resourcePath = '/reseller/children/{childIdentifier}/accountStatus';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($childIdentifier !== null) {
            $resourcePath = str_replace(
                '{' . 'childIdentifier' . '}',
                ObjectSerializer::toPathValue($childIdentifier),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;
        if (isset($updateChildAccountStatus)) {
            $_tempBody = $updateChildAccountStatus;
        }

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            
            if($headers['Content-Type'] === 'application/json') {
                // \stdClass has no __toString(), so we should encode it manually
                if ($httpBody instanceof \stdClass) {
                    $httpBody = \GuzzleHttp\json_encode($httpBody);
                }
                // array has no __toString(), so we should encode it manually
                if(is_array($httpBody)) {
                    $httpBody = \GuzzleHttp\json_encode(ObjectSerializer::sanitizeForSerialization($httpBody));
                }
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\Query::build($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('api-key');
        if ($apiKey !== null) {
            $headers['api-key'] = $apiKey;
        }
        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('partner-key');
        if ($apiKey !== null) {
            $headers['partner-key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\Query::build($queryParams);
        return new Request(
            'PUT',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation updateChildDomain
     *
     * Update the sender domain of reseller's child based on the childIdentifier and domainName passed
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  string $domainName Pass the existing domain that needs to be updated (required)
     * @param  \Brevo\Client\Model\UpdateChildDomain $updateChildDomain value to update for sender domain (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return void
     */
    public function updateChildDomain($childIdentifier, $domainName, $updateChildDomain)
    {
        $this->updateChildDomainWithHttpInfo($childIdentifier, $domainName, $updateChildDomain);
    }

    /**
     * Operation updateChildDomainWithHttpInfo
     *
     * Update the sender domain of reseller's child based on the childIdentifier and domainName passed
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  string $domainName Pass the existing domain that needs to be updated (required)
     * @param  \Brevo\Client\Model\UpdateChildDomain $updateChildDomain value to update for sender domain (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of null, HTTP status code, HTTP response headers (array of strings)
     */
    public function updateChildDomainWithHttpInfo($childIdentifier, $domainName, $updateChildDomain)
    {
        $returnType = '';
        $request = $this->updateChildDomainRequest($childIdentifier, $domainName, $updateChildDomain);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            return [null, $statusCode, $response->getHeaders()];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 400:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 403:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation updateChildDomainAsync
     *
     * Update the sender domain of reseller's child based on the childIdentifier and domainName passed
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  string $domainName Pass the existing domain that needs to be updated (required)
     * @param  \Brevo\Client\Model\UpdateChildDomain $updateChildDomain value to update for sender domain (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function updateChildDomainAsync($childIdentifier, $domainName, $updateChildDomain)
    {
        return $this->updateChildDomainAsyncWithHttpInfo($childIdentifier, $domainName, $updateChildDomain)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation updateChildDomainAsyncWithHttpInfo
     *
     * Update the sender domain of reseller's child based on the childIdentifier and domainName passed
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  string $domainName Pass the existing domain that needs to be updated (required)
     * @param  \Brevo\Client\Model\UpdateChildDomain $updateChildDomain value to update for sender domain (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function updateChildDomainAsyncWithHttpInfo($childIdentifier, $domainName, $updateChildDomain)
    {
        $returnType = '';
        $request = $this->updateChildDomainRequest($childIdentifier, $domainName, $updateChildDomain);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    return [null, $response->getStatusCode(), $response->getHeaders()];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'updateChildDomain'
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  string $domainName Pass the existing domain that needs to be updated (required)
     * @param  \Brevo\Client\Model\UpdateChildDomain $updateChildDomain value to update for sender domain (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function updateChildDomainRequest($childIdentifier, $domainName, $updateChildDomain)
    {
        // verify the required parameter 'childIdentifier' is set
        if ($childIdentifier === null || (is_array($childIdentifier) && count($childIdentifier) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $childIdentifier when calling updateChildDomain'
            );
        }
        // verify the required parameter 'domainName' is set
        if ($domainName === null || (is_array($domainName) && count($domainName) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $domainName when calling updateChildDomain'
            );
        }
        // verify the required parameter 'updateChildDomain' is set
        if ($updateChildDomain === null || (is_array($updateChildDomain) && count($updateChildDomain) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $updateChildDomain when calling updateChildDomain'
            );
        }

        $resourcePath = '/reseller/children/{childIdentifier}/domains/{domainName}';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($childIdentifier !== null) {
            $resourcePath = str_replace(
                '{' . 'childIdentifier' . '}',
                ObjectSerializer::toPathValue($childIdentifier),
                $resourcePath
            );
        }
        // path params
        if ($domainName !== null) {
            $resourcePath = str_replace(
                '{' . 'domainName' . '}',
                ObjectSerializer::toPathValue($domainName),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;
        if (isset($updateChildDomain)) {
            $_tempBody = $updateChildDomain;
        }

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            
            if($headers['Content-Type'] === 'application/json') {
                // \stdClass has no __toString(), so we should encode it manually
                if ($httpBody instanceof \stdClass) {
                    $httpBody = \GuzzleHttp\json_encode($httpBody);
                }
                // array has no __toString(), so we should encode it manually
                if(is_array($httpBody)) {
                    $httpBody = \GuzzleHttp\json_encode(ObjectSerializer::sanitizeForSerialization($httpBody));
                }
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\Query::build($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('api-key');
        if ($apiKey !== null) {
            $headers['api-key'] = $apiKey;
        }
        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('partner-key');
        if ($apiKey !== null) {
            $headers['partner-key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\Query::build($queryParams);
        return new Request(
            'PUT',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation updateResellerChild
     *
     * Update info of reseller's child based on the child identifier supplied
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\UpdateChild $resellerChild values to update in child profile (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return void
     */
    public function updateResellerChild($childIdentifier, $resellerChild)
    {
        $this->updateResellerChildWithHttpInfo($childIdentifier, $resellerChild);
    }

    /**
     * Operation updateResellerChildWithHttpInfo
     *
     * Update info of reseller's child based on the child identifier supplied
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\UpdateChild $resellerChild values to update in child profile (required)
     *
     * @throws \Brevo\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of null, HTTP status code, HTTP response headers (array of strings)
     */
    public function updateResellerChildWithHttpInfo($childIdentifier, $resellerChild)
    {
        $returnType = '';
        $request = $this->updateResellerChildRequest($childIdentifier, $resellerChild);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            return [null, $statusCode, $response->getHeaders()];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 400:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 403:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Brevo\Client\Model\ErrorModel',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation updateResellerChildAsync
     *
     * Update info of reseller's child based on the child identifier supplied
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\UpdateChild $resellerChild values to update in child profile (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function updateResellerChildAsync($childIdentifier, $resellerChild)
    {
        return $this->updateResellerChildAsyncWithHttpInfo($childIdentifier, $resellerChild)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation updateResellerChildAsyncWithHttpInfo
     *
     * Update info of reseller's child based on the child identifier supplied
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\UpdateChild $resellerChild values to update in child profile (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function updateResellerChildAsyncWithHttpInfo($childIdentifier, $resellerChild)
    {
        $returnType = '';
        $request = $this->updateResellerChildRequest($childIdentifier, $resellerChild);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    return [null, $response->getStatusCode(), $response->getHeaders()];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'updateResellerChild'
     *
     * @param  string $childIdentifier Either auth key or id of reseller&#39;s child (required)
     * @param  \Brevo\Client\Model\UpdateChild $resellerChild values to update in child profile (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function updateResellerChildRequest($childIdentifier, $resellerChild)
    {
        // verify the required parameter 'childIdentifier' is set
        if ($childIdentifier === null || (is_array($childIdentifier) && count($childIdentifier) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $childIdentifier when calling updateResellerChild'
            );
        }
        // verify the required parameter 'resellerChild' is set
        if ($resellerChild === null || (is_array($resellerChild) && count($resellerChild) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $resellerChild when calling updateResellerChild'
            );
        }

        $resourcePath = '/reseller/children/{childIdentifier}';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($childIdentifier !== null) {
            $resourcePath = str_replace(
                '{' . 'childIdentifier' . '}',
                ObjectSerializer::toPathValue($childIdentifier),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;
        if (isset($resellerChild)) {
            $_tempBody = $resellerChild;
        }

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            
            if($headers['Content-Type'] === 'application/json') {
                // \stdClass has no __toString(), so we should encode it manually
                if ($httpBody instanceof \stdClass) {
                    $httpBody = \GuzzleHttp\json_encode($httpBody);
                }
                // array has no __toString(), so we should encode it manually
                if(is_array($httpBody)) {
                    $httpBody = \GuzzleHttp\json_encode(ObjectSerializer::sanitizeForSerialization($httpBody));
                }
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\Query::build($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('api-key');
        if ($apiKey !== null) {
            $headers['api-key'] = $apiKey;
        }
        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('partner-key');
        if ($apiKey !== null) {
            $headers['partner-key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\Query::build($queryParams);
        return new Request(
            'PUT',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Create http client option
     *
     * @throws \RuntimeException on file opening failure
     * @return array of http client options
     */
    protected function createHttpClientOption()
    {
        $options = [];
        if ($this->config->getDebug()) {
            $options[RequestOptions::DEBUG] = fopen($this->config->getDebugFile(), 'a');
            if (!$options[RequestOptions::DEBUG]) {
                throw new \RuntimeException('Failed to open the debug file: ' . $this->config->getDebugFile());
            }
        }

        return $options;
    }
}
