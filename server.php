<?php
ob_start();
session_start();
error_reporting(0);
ini_set('display_errors', 'Off');
set_exception_handler(function ($exception) {
    ob_clean();
    header('Content-Type: application/json');
    http_response_code(500);
    $response = [
        "success" => false,
        "message" => "An unhandled server error occurred.",
        "error_details" => $exception->getMessage()
    ];
    error_log("Unhandled exception: " . $exception->getMessage() . " in " . $exception->getFile() . " on line " . $exception->getLine());
    echo json_encode($response);
    exit();
});
ini_set('log_errors', 'On');
ini_set('error_log', __DIR__ . '/logs/php_errors.log');
$allowedOrigins = [
    'http://localhost:8080',
    'https://efd948bb4834.ngrok-free.app',
    'https://semi-automated-load-unload-extravaganza.kesug.com',
];
error_log("Configured Allowed Origins: " . implode(', ', $allowedOrigins));
$requestOrigin = $_SERVER['HTTP_ORIGIN'] ?? '';
if (!empty($requestOrigin) && in_array($requestOrigin, $allowedOrigins)) {
    header("Access-Control-Allow-Origin: " . $requestOrigin);
} else {
    error_log("CORS: Request from disallowed origin: " . $requestOrigin);
}
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    ob_end_clean();
    exit();
}
header('Content-Type: application/json');
$brevoConfig = [
    "api_key" => "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",
    "sender_email" => "<EMAIL>",
    "sender_name" => "MIKO App"
];
$brevoApiUrl = "https://api.brevo.com/v3/smtp/email";
$dbPath = 'users.db';
function getDbConnection() {
    global $dbPath;
    try {
        $db = new SQLite3($dbPath);
        $db->enableExceptions(true);
        return $db;
    } catch (Exception $e) {
        error_log("Database connection error: " . $e->getMessage());
        throw new Exception("Database error: Could not open database.");
    }
}
function initializeDatabaseSchema() {
    $db = getDbConnection();
    try {
        $db->exec('CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email TEXT UNIQUE NOT NULL,
            password TEXT
        )');
        $db->exec('CREATE TABLE IF NOT EXISTS access_tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_email TEXT NOT NULL,
            token TEXT UNIQUE,
            otp TEXT UNIQUE,
            type TEXT NOT NULL,
            expires_at INTEGER NOT NULL,
            created_at INTEGER NOT NULL,
            status TEXT NOT NULL DEFAULT "pending"
        )');
        error_log("Database schema initialized/checked.");
    } catch (Exception $e) {
        error_log("Database schema initialization error: " . $e->getMessage());
        throw new Exception("Database schema error: Could not create tables.");
    } finally {
        if ($db) {
            $db->close();
        }
    }
}
function get_user_by_email($email) {
    $db = getDbConnection();
    $stmt = $db->prepare('SELECT id, email, password FROM users WHERE email = :email');
    $stmt->bindValue(':email', $email, SQLITE3_TEXT);
    $result = $stmt->execute();
    $user = $result->fetchArray(SQLITE3_ASSOC);
    $db->close();
    return $user;
}
function create_user($email, $password = null) {
    $db = getDbConnection();
    try {
        $stmt = $db->prepare('INSERT INTO users (email, password) VALUES (:email, :password)');
        $stmt->bindValue(':email', $email, SQLITE3_TEXT);
        $stmt->bindValue(':password', $password, SQLITE3_TEXT);
        $stmt->execute();
        $userId = $db->lastInsertRowID();
        return $userId;
    } catch (Exception $e) {
        error_log("Error creating user {$email}: " . $e->getMessage());
        return false;
    } finally {
        $db->close();
    }
}
function generateAndStoreToken($email, $type, $expiresInMinutes = 60) {
    $db = getDbConnection();
    $token = bin2hex(random_bytes(32));
    $expiresAt = time() + ($expiresInMinutes * 60);
    try {
        $stmtDelete = $db->prepare('DELETE FROM access_tokens WHERE user_email = :email AND type = :type AND status = "pending"');
        $stmtDelete->bindValue(':email', $email, SQLITE3_TEXT);
        $stmtDelete->bindValue(':type', $type, SQLITE3_TEXT);
        $stmtDelete->execute();
        $stmt = $db->prepare('INSERT INTO access_tokens (user_email, token, type, expires_at, created_at, status) VALUES (:user_email, :token, :type, :expires_at, :created_at, "pending")');
        $stmt->bindValue(':user_email', $email, SQLITE3_TEXT);
        $stmt->bindValue(':token', $token, SQLITE3_TEXT);
        $stmt->bindValue(':type', $type, SQLITE3_TEXT);
        $stmt->bindValue(':expires_at', $expiresAt, SQLITE3_INTEGER);
        $stmt->bindValue(':created_at', time(), SQLITE3_INTEGER);
        $stmt->execute();
        return $token;
    } catch (Exception $e) {
        error_log("Failed to store token in database: " . $e->getMessage());
        throw new Exception("Database error: Could not store token.");
    } finally {
        $db->close();
    }
}
function generateAndStoreOtp($email, $expiresInMinutes = 5) {
    $db = getDbConnection();
    $otp = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
    $expiresAt = time() + ($expiresInMinutes * 60);
    try {
        $stmtDelete = $db->prepare('DELETE FROM access_tokens WHERE user_email = :email AND type = "otp_verification" AND status = "pending"');
        $stmtDelete->bindValue(':email', $email, SQLITE3_TEXT);
        $stmtDelete->execute();
        $stmt = $db->prepare('INSERT INTO access_tokens (user_email, otp, type, expires_at, created_at, status) VALUES (:user_email, :otp, "otp_verification", :expires_at, :created_at, "pending")');
        $stmt->bindValue(':user_email', $email, SQLITE3_TEXT);
        $stmt->bindValue(':otp', $otp, SQLITE3_TEXT);
        $stmt->bindValue(':expires_at', $expiresAt, SQLITE3_INTEGER);
        $stmt->bindValue(':created_at', time(), SQLITE3_INTEGER);
        $stmt->execute();
        return $otp;
    } catch (Exception $e) {
        error_log("Failed to store OTP in database: " . $e->getMessage());
        throw new Exception("Database error: Could not store OTP.");
    } finally {
        $db->close();
    }
}
function validateToken($email, $token, $type) {
    $db = getDbConnection();
    try {
        $stmt = $db->prepare('SELECT id, expires_at, status FROM access_tokens WHERE user_email = :email AND token = :token AND type = :type AND status = "pending"');
        $stmt->bindValue(':email', $email, SQLITE3_TEXT);
        $stmt->bindValue(':token', $token, SQLITE3_TEXT);
        $stmt->bindValue(':type', $type, SQLITE3_TEXT);
        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_ASSOC);
        if ($row) {
            if ($row['expires_at'] > time()) {
                if ($type === 'login_link') {
                    $stmtUpdate = $db->prepare('UPDATE access_tokens SET status = "used" WHERE id = :id');
                    $stmtUpdate->bindValue(':id', $row['id'], SQLITE3_INTEGER);
                    $stmtUpdate->execute();
                    $dbUsers = getDbConnection();
                    $stmtUser = $dbUsers->prepare('SELECT id FROM users WHERE email = :email');
                    $stmtUser->bindValue(':email', $email, SQLITE3_TEXT);
                    $userResult = $stmtUser->execute();
                    $userRow = $userResult->fetchArray(SQLITE3_ASSOC);
                    $dbUsers->close();
                    if ($userRow) {
                        $_SESSION['user_id'] = $userRow['id'];
                        $_SESSION['user_email'] = $email;
                        error_log("User {$email} (ID: {$userRow['id']}) logged in via magic link. Session set.");
                        return ['success' => true, 'userId' => $userRow['id']];
                    } else {
                        error_log("User not found for email {$email} after login_link token validation. Cannot log in.");
                        return ['success' => false, 'userId' => null, 'message' => 'User not found after token validation.'];
                    }
                } elseif ($type === 'password_reset') {
                    return ['success' => true, 'userId' => null, 'message' => 'Password reset token is valid.'];
                }
            } else {
                $stmtUpdate = $db->prepare('UPDATE access_tokens SET status = "expired" WHERE id = :id');
                $stmtUpdate->bindValue(':id', $row['id'], SQLITE3_INTEGER);
                $stmtUpdate->execute();
                error_log("Token for {$email} of type {$type} expired.");
                return ['success' => false, 'userId' => null, 'message' => 'Token expired.'];
            }
        } else {
            error_log("Token for {$email} of type {$type} not found or already used/invalid.");
        }
        return ['success' => false, 'userId' => null, 'message' => 'Invalid or already used token.'];
    } catch (Exception $e) {
        error_log("Error validating token: " . $e->getMessage());
        throw new Exception("Database error: Could not validate token.");
    } finally {
        $db->close();
    }
}
function validateOtp($email, $otp) {
    $db = getDbConnection();
    try {
        $stmt = $db->prepare('SELECT id, expires_at, status FROM access_tokens WHERE user_email = :email AND otp = :otp AND type = "otp_verification" AND status = "pending"');
        $stmt->bindValue(':email', $email, SQLITE3_TEXT);
        $stmt->bindValue(':otp', $otp, SQLITE3_TEXT);
        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_ASSOC);
        if ($row) {
            if ($row['expires_at'] > time()) {
                $stmtUpdate = $db->prepare('UPDATE access_tokens SET status = "used" WHERE id = :id');
                $stmtUpdate->bindValue(':id', $row['id'], SQLITE3_INTEGER);
                $stmtUpdate->execute();
                return true;
            } else {
                $stmtUpdate = $db->prepare('UPDATE access_tokens SET status = "expired" WHERE id = :id');
                $stmtUpdate->bindValue(':id', $row['id'], SQLITE3_INTEGER);
                $stmtUpdate->execute();
                error_log("OTP for {$email} expired.");
            }
        } else {
            error_log("OTP for {$email} not found or already used/invalid.");
        }
        return false;
    } catch (Exception $e) {
        error_log("Error validating OTP: " . $e->getMessage());
        throw new Exception("Database error: Could not validate OTP.");
    } finally {
        $db->close();
    }
}
function get_email_template($templateName, $placeholders = []) {
    $templatePath = __DIR__ . '/email_templates/' . $templateName;
    if (!file_exists($templatePath)) {
        error_log("Email template not found: " . $templatePath);
        throw new Exception("Email template not found: " . $templateName);
    }
    $htmlContent = file_get_contents($templatePath);
    if ($htmlContent === false) {
        error_log("Failed to read email template: " . $templatePath);
        throw new Exception("Failed to read email template: " . $templateName);
    }
    foreach ($placeholders as $placeholder => $value) {
        $htmlContent = str_replace($placeholder, $value, $htmlContent);
    }
    return $htmlContent;
}
function generateQrCodeUrl($dataToEncode) {
    $qrCodeApiUrl = "https://chart.googleapis.com/chart?chs=200x200&cht=qr&chl=" . urlencode($dataToEncode);
    return $qrCodeApiUrl;
}
function sendEmailViaBrevo($recipientEmail, $subject, $htmlContent) {
    global $brevoConfig, $brevoApiUrl;
    if (empty($brevoConfig["api_key"])) {
        error_log("Brevo API Key is not configured.");
        return ["success" => false, "message" => "Brevo API key missing. Please configure it in server.php."];
    }
    $headers = [
        "accept: application/json",
        "api-key: " . $brevoConfig["api_key"],
        "content-type: application/json"
    ];
    $payload = [
        "sender" => [
            "name" => $brevoConfig["sender_name"],
            "email" => $brevoConfig["sender_email"]
        ],
        "to" => [
            [
                "email" => $recipientEmail
            ]
        ],
        "subject" => $subject,
        "htmlContent" => $htmlContent
    ];
    $ch = curl_init($brevoApiUrl);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    if ($curlError) {
        error_log("Network or Brevo API connection error: " . $curlError);
        return ["success" => false, "message" => "Network error or Brevo API connection issue: " . $curlError];
    }
    $responseData = json_decode($response, true);
    if ($httpCode == 201 && isset($responseData["messageId"])) {
        error_log("Brevo: Email successfully sent to " . $recipientEmail . " with subject '" . $subject . "'. Message ID: " . $responseData['messageId']);
        return ["success" => true, "message" => "Email sent successfully via Brevo."];
    } else {
        $errorMessage = isset($responseData['message']) ? $responseData['message'] : 'Unknown Brevo error';
        error_log("Brevo API error sending email to " . $recipientEmail . ": " . $errorMessage . ". Response: " . $response);
        return ["success" => false, "message" => "Brevo API error: " . $errorMessage];
    }
}
$input = json_decode(file_get_contents('php://input'), true);
$email = $input['email'] ?? '';
$path = $_GET['path'] ?? '';
error_log("Received path parameter: '" . $path . "'");
$response = ["success" => false, "message" => "Invalid API endpoint or missing email."];
try {
    initializeDatabaseSchema();
} catch (Exception $e) {
    error_log("Initial database schema setup failed: " . $e->getMessage());
    ob_clean();
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(["success" => false, "message" => "Server setup error: " . $e->getMessage()]);
    ob_end_flush();
    exit();
}
try {
    if (!empty($email)) {
        switch ($path) {
            case '/api/send-login-link':
                try {
                    $token = generateAndStoreToken($email, 'login_link');
                    $currentOrigin = $_SERVER['HTTP_ORIGIN'] ?? 'https://efd948bb4834.ngrok-free.app';
                    $loginLink = "{$currentOrigin}/login.html?token={$token}&email={$email}";
                    $qrCodeUrl = generateQrCodeUrl($loginLink);
                    $subject = "Your MIKO Login Link";
                    $htmlContent = get_email_template('login_link.html', [
                        '{{login_link}}' => $loginLink,
                        '{{qr_code_url}}' => $qrCodeUrl,
                        '{{year}}' => date("Y")
                    ]);
                    $response = sendEmailViaBrevo($email, $subject, $htmlContent);
                } catch (Exception $e) {
                    error_log("Error sending login link: " . $e->getMessage());
                    $response = ["success" => false, "message" => "Failed to send login link: " . $e->getMessage()];
                }
                break;
            case '/api/send-password-reset':
                try {
                    $token = generateAndStoreToken($email, 'password_reset');
                    $currentOrigin = $_SERVER['HTTP_ORIGIN'] ?? 'https://efd948bb4834.ngrok-free.app';
                    $resetLink = "{$currentOrigin}/reset-password-form.html?token={$token}&email={$email}";
                    $qrCodeUrl = generateQrCodeUrl($resetLink);
                    $subject = "MIKO Password Reset Request";
                    $htmlContent = get_email_template('password_reset.html', [
                        '{{reset_link}}' => $resetLink,
                        '{{qr_code_url}}' => $qrCodeUrl,
                        '{{year}}' => date("Y")
                    ]);
                    $response = sendEmailViaBrevo($email, $subject, $htmlContent);
                } catch (Exception $e) {
                    error_log("Error sending password reset link: " . $e->getMessage());
                    $response = ["success" => false, "message" => "Failed to send password reset link: " . $e->getMessage()];
                }
                break;
            case '/api/send-verification':
                try {
                    $otp = generateAndStoreOtp($email);
                    $qrCodeUrl = generateQrCodeUrl("MIKO OTP: {$otp}");
                    $subject = "Verify Your MIKO Account Email - Your OTP";
                    $htmlContent = get_email_template('otp_verification.html', [
                        '{{otp}}' => $otp,
                        '{{qr_code_url}}' => $qrCodeUrl,
                        '{{year}}' => date("Y")
                    ]);
                    $response = sendEmailViaBrevo($email, $subject, $htmlContent);
                } catch (Exception $e) {
                    error_log("Error sending verification OTP: " . $e->getMessage());
                    $response = ["success" => false, "message" => "Failed to send verification OTP: " . $e->getMessage()];
                }
                break;
            case '/api/verify-token':
                $token = $input['token'] ?? '';
                $type = $input['type'] ?? '';
                if (empty($token) || empty($type)) {
                    $response = ["success" => false, "message" => "Token and type are required for verification."];
                } else {
                    try {
                        $validationResult = validateToken($email, $token, $type);
                        $response = $validationResult;
                    } catch (Exception $e) {
                        error_log("Error validating token: " . $e->getMessage());
                        $response = ["success" => false, "message" => "Failed to validate token: " . $e->getMessage()];
                    }
                }
                break;
            case '/api/verify-otp':
                $otp = $input['otp'] ?? '';
                if (empty($otp)) {
                    $response = ["success" => false, "message" => "OTP is required for verification."];
                } else {
                    try {
                        if (validateOtp($email, $otp)) {
                            $response = ["success" => true, "message" => "OTP validated successfully."];
                        } else {
                            $response = ["success" => false, "message" => "Invalid or expired OTP."];
                        }
                    } catch (Exception $e) {
                        error_log("Error validating OTP: " . $e->getMessage());
                        $response = ["success" => false, "message" => "Failed to validate OTP: " . $e->getMessage()];
                    }
                }
                break;
            case '/api/reset-password':
                $token = $input['token'] ?? '';
                $newPassword = $input['new_password'] ?? '';
                if (empty($email) || empty($token) || empty($newPassword)) {
                    $response = ["success" => false, "message" => "Email, token, and new password are required."];
                    break;
                }
                if (strlen($newPassword) < 6) {
                    $response = ["success" => false, "message" => "New password must be at least 6 characters long."];
                    break;
                }
                try {
                    $validationResult = validateToken($email, $token, 'password_reset');
                    if ($validationResult['success']) {
                        $dbUsers = getDbConnection();
                        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                        $stmtUpdate = $dbUsers->prepare('UPDATE users SET password = :password WHERE email = :email');
                        $stmtUpdate->bindValue(':password', $hashedPassword, SQLITE3_TEXT);
                        $stmtUpdate->bindValue(':email', $email, SQLITE3_TEXT);
                        if ($stmtUpdate->execute()) {
                            $dbAccessTokens = getDbConnection();
                            $stmtMarkUsed = $dbAccessTokens->prepare('UPDATE access_tokens SET status = "used" WHERE user_email = :email AND token = :token AND type = "password_reset"');
                            $stmtMarkUsed->bindValue(':email', $email, SQLITE3_TEXT);
                            $stmtMarkUsed->bindValue(':token', $token, SQLITE3_TEXT);
                            $stmtMarkUsed->execute();
                            $dbAccessTokens->close();
                            $response = ["success" => true, "message" => "Password reset successfully."];
                            error_log("Password for {$email} reset successfully and token marked as used.");
                        } else {
                            $response = ["success" => false, "message" => "Failed to update password in database."];
                            error_log("Failed to update password for {$email}: " . $dbUsers->lastErrorMsg());
                        }
                        $dbUsers->close();
                    } else {
                        $response = ["success" => false, "message" => $validationResult['message'] ?? "Invalid or expired password reset token."];
                    }
                } catch (Exception $e) {
                    error_log("Error during password reset: " . $e->getMessage());
                    $response = ["success" => false, "message" => "An error occurred during password reset: " . $e->getMessage()];
                }
                break;
            default:
                $response = ["success" => false, "message" => "Unknown API endpoint."];
                break;
        }
    }
} catch (Exception $e) {
    error_log("Unhandled exception in server.php: " . $e->getMessage());
    $response = ["success" => false, "message" => "An unexpected server error occurred: " . $e->getMessage()];
}
ob_end_clean();
echo json_encode($response);
ob_end_flush();
exit();
?>
