import qrcode
import base64
import json
import tkinter as tk
from tkinter import messagebox, filedialog
from PIL import Image, ImageTk

hidden_fields = []

def add_hidden_field():
    field_frame = tk.Frame(hidden_container)
    field_frame.pack(pady=2, anchor="w")

    field_name = tk.Entry(field_frame, width=20)
    field_name.pack(side="left", padx=5)
    field_name.insert(0, "Field")

    field_value = tk.Entry(field_frame, width=35)
    field_value.pack(side="left", padx=5)
    field_value.insert(0, "Value")

    hidden_fields.append((field_name, field_value))

def generate_qr():
    public_text = public_entry.get()

    if not public_text:
        messagebox.showerror("Error", "Public field is required.")
        return

    hidden_dict = {}
    for field_name_entry, field_value_entry in hidden_fields:
        key = field_name_entry.get().strip()
        value = field_value_entry.get().strip()
        if key:
            hidden_dict[key] = value

    if not hidden_dict:
        messagebox.showerror("Error", "At least one hidden field is required.")
        return

    try:
        hidden_json = json.dumps(hidden_dict)
        hidden_encoded = base64.b64encode(hidden_json.encode()).decode()

        payload = {
            "public": public_text,
            "hidden": hidden_encoded
        }

        qr = qrcode.QRCode(
            version=2,
            error_correction=qrcode.constants.ERROR_CORRECT_M,
            box_size=10,
            border=4
        )
        qr.add_data(json.dumps(payload))
        qr.make(fit=True)

        img = qr.make_image(fill_color="black", back_color="white")

        file_path = filedialog.asksaveasfilename(defaultextension=".png",
                                                 filetypes=[("PNG files", "*.png")],
                                                 title="Save QR Code")
        if file_path:
            img.save(file_path)
            messagebox.showinfo("Success", f"QR code saved to:\n{file_path}")
            show_qr(img)
    except Exception as e:
        messagebox.showerror("Error", f"Failed to generate QR code:\n{e}")

def show_qr(pil_image):
    qr_img = pil_image.resize((200, 200))
    tk_img = ImageTk.PhotoImage(qr_img)
    qr_label.config(image=tk_img)
    qr_label.image = tk_img

# --- GUI Setup ---
root = tk.Tk()
root.title("QR Generator with Structured Hidden Fields")
root.geometry("500x600")

tk.Label(root, text="Public Data (visible):").pack(pady=5)
public_entry = tk.Entry(root, width=60)
public_entry.pack(pady=5)

tk.Label(root, text="Hidden Data (Add Fields):").pack(pady=10)

hidden_container = tk.Frame(root)
hidden_container.pack()

tk.Button(root, text="+ Add Field", command=add_hidden_field, bg="lightblue").pack(pady=5)

tk.Button(root, text="Generate QR Code", command=generate_qr, bg="green", fg="white").pack(pady=20)

qr_label = tk.Label(root)
qr_label.pack(pady=10)

# Add one default hidden field
add_hidden_field()

root.mainloop()
