window.validator = {
    /**
     * @param {string} email - 
     * @returns {boolean} - 
     */
    isValidEmail: function(email) {
        // Basic regex for email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    /**
     * @param {string} password - 
     * @returns {boolean} - 
     */
    isValidPassword: function(password) {
        return password && password.length >= 6;
    },

    /**
     * @param {string} otp -
     * @returns {boolean} -
     */
    isValidOtp: function(otp) {
        const otpRegex = /^\d{6}$/;
        return otpRegex.test(otp);
    },

    /**
     * @param {string} phoneNumber
     * @returns {boolean}
     */
    isValidPhoneNumber: function(phoneNumber) {
        const phoneRegex = /^\+?\d{7,15}$/;
        return phoneRegex.test(phoneNumber);
    }
};
