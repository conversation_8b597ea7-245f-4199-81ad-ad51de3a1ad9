<?php
/**
 * This file is part of the Cloudinary PHP package.
 *
 * (c) Cloudinary
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Cloudinary\Transformation;

/**
 * Interface ImageFormatInterface
 *
 * @api
 */
interface ImageFormatInterface
{
    public const GIF = 'gif';
    public const PNG = 'png';
    public const JPG = 'jpg';
    public const BMP = 'bmp';
    public const ICO = 'ico';
    public const PDF = 'pdf';
    public const TIFF = 'tiff';
    public const EPS  = 'eps';
    public const JPC = 'jpc';
    public const JP2 = 'jp2';
    public const PSD = 'psd';
    public const WEBP = 'webp';
    public const SVG  = 'svg';
    public const WDP = 'wdp';
    public const HPX = 'hpx';
    public const DJVU = 'djvu';
    public const AI   = 'ai';
    public const FLIF = 'flif';
    public const BPG  = 'bpg';
    public const MIFF = 'miff';
    public const TGA  = 'tga';
    public const CR2 = 'cr2';
    public const ARW = 'arw';
    public const DNG = 'dng';
    public const HEIC = 'heic';
    public const GLTZ = 'gltz';
    public const GLTF = 'gltf';
    public const GLB  = 'glb';
    public const FBXZ = 'fbxz';
    public const INDD = 'indd';
    public const IDML = 'idml';
    public const AVIF = 'avif';
    public const OBJZ = 'objz';
    public const USDZ = 'usdz';
}
