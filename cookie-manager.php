<?php
/**
 * Secure Cookie Manager for MIKO App
 * Handles encrypted cookie storage for preloaded data
 */

class SecureCookieManager {
    private static $encryption_key;
    private static $cookie_prefix = 'miko_';
    private static $cookie_options = [
        'expires' => 0, // Will be set dynamically
        'path' => '/',
        'domain' => '',
        'secure' => false, // Will be set based on HTTPS
        'httponly' => true,
        'samesite' => 'Strict'
    ];

    /**
     * Initialize the cookie manager
     */
    public static function init() {
        // Generate or retrieve encryption key
        self::$encryption_key = self::getEncryptionKey();
        
        // Set secure flag based on HTTPS
        self::$cookie_options['secure'] = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';
    }

    /**
     * Get or generate encryption key
     */
    private static function getEncryptionKey() {
        $key_file = __DIR__ . '/.miko_key';
        
        if (file_exists($key_file)) {
            return file_get_contents($key_file);
        }
        
        // Generate new key
        $key = base64_encode(random_bytes(32));
        file_put_contents($key_file, $key);
        chmod($key_file, 0600); // Restrict access
        
        return $key;
    }

    /**
     * Encrypt data
     */
    private static function encrypt($data) {
        $key = base64_decode(self::$encryption_key);
        $iv = random_bytes(16);
        $encrypted = openssl_encrypt(json_encode($data), 'AES-256-CBC', $key, 0, $iv);
        return base64_encode($iv . $encrypted);
    }

    /**
     * Decrypt data
     */
    private static function decrypt($encrypted_data) {
        try {
            $key = base64_decode(self::$encryption_key);
            $data = base64_decode($encrypted_data);
            $iv = substr($data, 0, 16);
            $encrypted = substr($data, 16);
            $decrypted = openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
            return json_decode($decrypted, true);
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Set secure cookie with encrypted data
     */
    public static function setCookie($name, $data, $expires_minutes = 60) {
        $encrypted_data = self::encrypt($data);
        $cookie_name = self::$cookie_prefix . $name;
        
        $options = self::$cookie_options;
        $options['expires'] = time() + ($expires_minutes * 60);
        
        return setcookie($cookie_name, $encrypted_data, $options);
    }

    /**
     * Get and decrypt cookie data
     */
    public static function getCookie($name) {
        $cookie_name = self::$cookie_prefix . $name;
        
        if (!isset($_COOKIE[$cookie_name])) {
            return null;
        }
        
        return self::decrypt($_COOKIE[$cookie_name]);
    }

    /**
     * Delete cookie
     */
    public static function deleteCookie($name) {
        $cookie_name = self::$cookie_prefix . $name;
        $options = self::$cookie_options;
        $options['expires'] = time() - 3600; // Set to past time
        
        return setcookie($cookie_name, '', $options);
    }

    /**
     * Check if cookie exists and is valid
     */
    public static function hasCookie($name) {
        $data = self::getCookie($name);
        return $data !== null;
    }

    /**
     * Set cache data with metadata
     */
    public static function setCacheData($data) {
        $cache_data = [
            'timestamp' => time(),
            'data' => $data,
            'version' => '1.0'
        ];
        
        return self::setCookie('cache', $cache_data, 60); // 1 hour expiry
    }

    /**
     * Get cache data if valid
     */
    public static function getCacheData($max_age_minutes = 60) {
        $cache_data = self::getCookie('cache');
        
        if (!$cache_data || !isset($cache_data['timestamp'])) {
            return null;
        }
        
        // Check if cache is still valid
        $age_minutes = (time() - $cache_data['timestamp']) / 60;
        if ($age_minutes > $max_age_minutes) {
            self::deleteCookie('cache');
            return null;
        }
        
        return $cache_data['data'];
    }

    /**
     * Clear all MIKO cookies
     */
    public static function clearAllCookies() {
        foreach ($_COOKIE as $name => $value) {
            if (strpos($name, self::$cookie_prefix) === 0) {
                $clean_name = substr($name, strlen(self::$cookie_prefix));
                self::deleteCookie($clean_name);
            }
        }
    }
}

// Initialize the cookie manager
SecureCookieManager::init();
?>
