name: Tests
on: [push, pull_request]
jobs:
  php:
    name: PHP ${{ matrix.php-versions }}
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        php-versions:
          - '8.0'
          - '8.1'
          - '8.2'
          - '8.3'
          - '8.4'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup PHP, with composer and extensions
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}
          extensions: json
          coverage: xdebug
      - name: Install Composer dependencies
        run: composer update -n
      - name: Run Tests
        run: |
          export CLOUDINARY_URL=$(bash tools/get_test_cloud.sh);
          echo cloud_name: "$(echo $CLOUDINARY_URL | cut -d'@' -f2)"
          vendor/bin/simple-phpunit
