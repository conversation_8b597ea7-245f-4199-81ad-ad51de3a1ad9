# WhatsappCampTemplate

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **string** | name of the template | [optional] 
**category** | **string** | description of the template | [optional] 
**language** | **string** | language of the template | [optional] 
**containsButton** | **bool** |  | [optional] 
**displayHeader** | **bool** |  | [optional] 
**headerType** | **string** | type of header | [optional] 
**components** | [**\Brevo\Client\Model\ComponentItems[]**](ComponentItems.md) | array of component item objects | [optional] 
**headerVariables** | [**\Brevo\Client\Model\VariablesItems[]**](VariablesItems.md) | array of variables item object | [optional] 
**bodyVariables** | [**\Brevo\Client\Model\VariablesItems[]**](VariablesItems.md) | array of variables item variables | [optional] 
**buttonType** | **string** |  | [optional] 
**hideFooter** | **bool** |  | [optional] 

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)


