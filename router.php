<?php
$document_root = __DIR__;
$uri = urldecode(parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH));

$routes = [
    '/' => '/index.html', '' => '/index.html', 
    '/login' => '/login.html',
    '/home' => '/home.html',

    '/offline' => '/offline.html',
    '/reset-password' => '/reset-password-form.html'
];

if ($uri === '/setup') { header('Location: /login'); exit; }

if (in_array($uri, ['/privacy-policy', '/terms-and-conditions'])) {
    $file = $document_root . '/login.html';
    if (file_exists($file)) {
        header('Content-Type: text/html');
        echo file_get_contents($file) . "<script>window.location.hash = '" . substr($uri, 1) . "';</script>";
        exit;
    }
}

if (isset($routes[$uri])) {
    $file = $document_root . $routes[$uri];
    if (file_exists($file)) {
        header('Content-Type: text/html');
        readfile($file);
        exit;
    }
}

$file = $document_root . $uri;
if (file_exists($file)) return false;
http_response_code(404);
echo "404 Not Found";
?>
?>