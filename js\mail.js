async function sendLoginLinkEmail(email) {
    try {
        const response = await fetch(`server.php?path=/api/send-login-link`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email: email })
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`HTTP error! Status: ${response.status}, Response: ${errorText}`);
            return { success: false, message: `Server error: ${response.status}. Details: ${errorText.substring(0, 200)}...` };
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error sending login link email:', error);
        return { success: false, message: `Network error: ${error.message}. Please check your server.` };
    }
}
window.sendLoginLinkEmail = sendLoginLinkEmail;

async function sendPasswordResetEmail(email) {
    try {
        const response = await fetch(`server.php?path=/api/send-password-reset`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email: email })
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`HTTP error! Status: ${response.status}, Response: ${errorText}`);
            return { success: false, message: `Server error: ${response.status}. Details: ${errorText.substring(0, 200)}...` };
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error sending password reset email:', error);
        return { success: false, message: `Network error: ${error.message}. Please check your server.` };
    }
}
window.sendPasswordResetEmail = sendPasswordResetEmail;

async function sendVerificationEmail(email) {
    try {
        const response = await fetch(`server.php?path=/api/send-verification`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email: email })
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`HTTP error! Status: ${response.status}, Response: ${errorText}`);
            return { success: false, message: `Server error: ${response.status}. Details: ${errorText.substring(0, 200)}...` };
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error sending verification email:', error);
        return { success: false, message: `Network error: ${error.message}. Please check your server.` };
    }
}
window.sendVerificationEmail = sendVerificationEmail;

async function verifyOtp(email, otp) {
    try {
        const response = await fetch(`server.php?path=/api/verify-otp`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email: email, otp: otp })
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`HTTP error! Status: ${response.status}, Response: ${errorText}`);
            return { success: false, message: `Server error: ${response.status}. Details: ${errorText.substring(0, 200)}...` };
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error verifying OTP:', error);
        return { success: false, message: `Network error: ${error.message}. Please check your server.` };
    }
}
window.verifyOtp = verifyOtp;

async function handleMagicLinkLogin(email, token) {
    try {
        document.body.style.cursor = 'wait';

        const response = await fetch(`server.php?path=/api/verify-token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email: email, token: token, type: 'login_link' })
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`HTTP error! Status: ${response.status}, Response: ${errorText}`);
            throw new Error(`HTTP error! status: ${response.status}. Details: ${errorText.substring(0, 200)}...`);
        }

        const data = await response.json();

        if (data.success) {
            alert('Successfully logged in via magic link!');
            const simulatedUser = {
                uid: data.userId || 'magic-link-user-' + Math.random().toString(36).substring(2, 15),
                email: email,
                displayName: email.split('@')[0] || 'Magic Link User'
            };
            if (typeof window.handleSuccessfulLogin === 'function') {
                await window.handleSuccessfulLogin(simulatedUser);
            } else {
                console.error('window.handleSuccessfulLogin is not defined. Cannot redirect.');
                alert('Login successful, but cannot redirect. Please refresh.');
            }
        } else {
            alert(`Login link failed: ${data.message || 'Unknown error'}`);
            history.pushState(null, '', '/login.html');
        }
    } catch (error) {
        console.error('Error during magic link login:', error);
        alert('An error occurred during magic link login. Please try again later. Details: ' + error.message);
        history.pushState(null, '', '/login.html');
    } finally {
        document.body.style.cursor = 'default';
    }
}
window.handleMagicLinkLogin = handleMagicLinkLogin;
