// search.js - Contains all JavaScript and CSS logic for the search functionality in MIKO

document.addEventListener('DOMContentLoaded', () => {
    // Dynamically inject search-related CSS
    const style = document.createElement('style');
    style.textContent = `
        /* Header Search Bar Container */
        .header-search-bar-container {
            width: 100%;
            display: none;
            align-items: center;
            justify-content: space-between;
            opacity: 0;
            transition: opacity 0.3s ease-out;
        }

        .header-search-bar-container.active {
            display: flex;
            opacity: 1;
        }

        .header-search-bar-container .search-input {
            flex-grow: 1;
            display: flex;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 100px;
            padding: 1rem;
            margin: 3.5px;
            transition: box-shadow 0.3s ease, transform 0.3s ease-out, opacity 0.3s ease-out;
            transform: translateY(-100%);
            opacity: 0;
        }

        .header-search-bar-container.active .search-input {
            transform: translateY(0);
            opacity: 1;
        }

        .header-search-bar-container .search-input:focus-within {
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.5);
        }

        .header-search-bar-container .search-icon-in-bar {
            color: var(--white);
            font-size: 1.1rem;
            margin-right: 0.75rem;
        }

        .header-search-bar-container input {
            flex-grow: 1;
            border: none;
            background-color: transparent;
            color: var(--white);
            outline: none;
            font-size: 1rem;
            line-height: 1.5;
        }

        .header-search-bar-container input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .header-search-bar-container input:focus {
            outline: none;
        }

        .header-search-bar-container .close-search-btn {
            background: transparent;
            border: none;
            color: white;
            font-size: 1.8rem;
            margin-left: 1rem;
            cursor: pointer;
            line-height: 1;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* No Results Message */
        .no-results-message {
            display: none;
            margin-top: 2rem;
            padding: 2rem;
            background-color: white;
            border-radius: 12px;
            flex-direction: column;
            align-items: center;
            color: black;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            width: 90%;
            max-width: 400px;
        }

        .no-results-message.visible {
            display: flex;
        }

        .no-results-message .icon {
            font-size: 3rem;
            color: #ccc;
            margin-bottom: 1rem;
        }

        .no-results-message .message-text {
            font-size: 1.1rem;
            font-weight: 500;
        }

        /* Search Content Wrapper */
        .search-content-wrapper {
            width: 100%;
            display: none;
            flex-direction: column;
            align-items: center;
            text-align: left;
            padding-top: 1rem;
        }

        .search-content-wrapper.active {
            display: flex;
        }

        /* Section Container (for Popular Foodies, Hot Recommendations, Restaurants) */
        .section-container {
            width: 100%;
            max-width: 800px;
            margin-top: 1rem;
            text-align: left;
            display: none;
        }

        .section-container.visible {
            display: block;
        }

        .section-container h3 {
            font-size: 1.1rem;
            color: var(--text);
            margin-bottom: 1rem;
            padding: 0 1.5rem;
        }

        /* Tags Container (for Popular Foodies and scrollable lists) */
        .tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
            padding: 0 1.5rem;
        }

        .tags-container.scrollable {
            flex-wrap: nowrap;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            padding-bottom: 10px;
            scrollbar-width: thin;
            scrollbar-color: var(--primary) var(--neutral);
        }

        .tags-container.scrollable::-webkit-scrollbar {
            height: 8px;
        }

        .tags-container.scrollable::-webkit-scrollbar-track {
            background: var(--neutral);
            border-radius: 10px;
        }

        .tags-container.scrollable::-webkit-scrollbar-thumb {
            background-color: var(--primary);
            border-radius: 10px;
            border: 2px solid var(--neutral);
        }

        .tag-item {
            background-color: #e0e0e0;
            color: var(--text);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex-shrink: 0;
        }

        /* --- Reusable Card Base Styles --- */
        .card-base {
            background-color: var(--white);
            border-radius: 15px;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            width: 100%;
            flex-shrink: 0;
            cursor: pointer;
            position: relative;
            box-shadow: var(--shadow);
        }

        .card-image-section {
            width: 100%;
            height: 100px;
            border-top-left-radius: 15px;
            border-top-right-radius: 15px;
            overflow: hidden;
            background-color: #333333;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .card-image-section img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }

        .card-info-section {
            width: 100%;
            padding: 0.75rem 0.5rem;
            font-size: 1rem;
            color: var(--text);
            font-weight: 600;
            text-align: center;
            min-height: 40px; /* Ensure a minimum height */
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .card-info-section h5 {
            font-size: 1rem;
            margin: 0;
            color: var(--text);
            font-weight: 600;
            white-space: nowrap; /* Prevent text wrapping */
            overflow: hidden;    /* Hide overflowing text */
            text-overflow: ellipsis; /* Show ellipsis for hidden text */
            max-width: 100%; /* Ensure it respects parent width */
        }

        .card-info-section .sub-text {
            font-size: 0.8rem;
            color: #666;
            margin-top: 0.2rem;
            white-space: nowrap; /* Prevent text wrapping */
            overflow: hidden;    /* Hide overflowing text */
            text-overflow: ellipsis; /* Show ellipsis for hidden text */
            max-width: 100%; /* Ensure it respects parent width */
        }

        .card-bottom-section {
            width: 100%;
            padding: 0.75rem 0.5rem;
            font-size: 0.9rem;
            font-weight: bold;
            color: var(--primary);
            text-align: center;
            border-bottom-left-radius: 15px;
            border-bottom-right-radius: 15px;
            background-color: var(--neutral);
            min-height: 40px; /* Ensure a minimum height */
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.25rem;
        }

        .card-bottom-section .price-info {
            display: flex;
            align-items: baseline;
            justify-content: center;
            gap: 0.25rem;
            padding: 0 0.5rem; /* Added padding here */
            white-space: nowrap; /* Prevent text wrapping */
            overflow: hidden;    /* Hide overflowing text */
            text-overflow: ellipsis; /* Show ellipsis for hidden text */
            max-width: 100%; /* Ensure it respects parent width */
        }

        .card-bottom-section small {
            font-size: 0.7rem;
            color: #999;
            text-decoration: line-through;
            margin-left: 0;
        }

        /* Specific styles for hot recommendation item cards */
        .hot-recommendation-item-card {
            width: 150px; /* Fixed width for horizontal scroll */
        }

        /* Search Results List */
        .search-results-list {
            width: 100%;
            max-width: 800px;
            padding: 0;
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
            justify-content: center;
            flex-grow: 1;
            display: none;
        }
        .search-results-list.visible {
            display: flex;
        }

        .search-category-section {
            background-color: var(--white);
            border-radius: 15px;
            box-shadow: var(--shadow);
            text-align: left;
            width: 100%;
            display: none;
        }

        .search-category-section.visible {
            display: block;
        }

        .search-category-section h3 {
            font-size: 1.2rem;
            color: var(--primary);
            margin-bottom: 1rem;
            padding: 1.5rem 1.5rem 0.5rem 1.5rem;
        }

        /* Grid for food items (3 per row) */
        #foodResultsContainer {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 1rem;
            justify-items: center;
            padding: 1.5rem;
        }

        /* Grid for restaurant search results (single column, full width) */
        #restaurantSearchResultsContainer {
            display: grid;
            grid-template-columns: 1fr; /* Single column, full width */
            gap: 1rem;
            padding: 1.5rem;
        }

        /* Styles for restaurant display cards (horizontal scroll) */
        .restaurant-display-card {
            background-color: var(--white);
            border-radius: 12px;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: left;
            width: 280px; /* Fixed width for horizontal scroll */
            flex-shrink: 0;
            cursor: pointer;
            box-shadow: var(--shadow);
            position: relative;
            border: 1px solid rgba(255, 107, 53, 0.2);
        }

        .restaurant-display-card .restaurant-image-section {
            width: 100%;
            height: 150px;
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;
            overflow: hidden;
            background-color: #eee;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .restaurant-display-card .restaurant-image-section img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }

        .restaurant-display-card .restaurant-info-section {
            width: 100%;
            padding: 0.75rem 1rem;
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .restaurant-display-card .restaurant-name-rating {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.25rem;
        }

        .restaurant-display-card .restaurant-name-rating h5 {
            font-size: 1.1rem;
            color: var(--text);
            font-weight: 600;
            margin: 0;
        }

        .restaurant-display-card .rating-info {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.9rem;
            color: #666;
        }

        .restaurant-display-card .rating-info i {
            color: #FFD700;
            font-size: 0.8rem;
        }

        .restaurant-display-card .delivery-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.85rem;
            color: #666;
            margin-bottom: 0.25rem;
        }

        .restaurant-display-card .delivery-info .delivery-time,
        .restaurant-display-card .delivery-info .delivery-fee {
            display: flex;
            align-items: center;
            gap: 0.2rem;
        }

        .restaurant-display-card .delivery-info i {
            font-size: 0.75rem;
            color: #999;
        }

        .restaurant-display-card .delivery-info .delivery-fee.free {
            color: #4CAF50;
            font-weight: 600;
        }
        .restaurant-display-card .delivery-info .delivery-fee.paid {
            color: #333;
        }

        /* New styles for restaurant search result items (full width, home page card style) */
        .restaurant-search-result-item {
            background-color: var(--white);
            border-radius: 12px;
            padding: 0; /* Internal sections will have padding */
            display: flex;
            flex-direction: column; /* Image on top, info below */
            align-items: center;
            text-align: left;
            width: 100%; /* Fills the container */
            flex-shrink: 0;
            cursor: pointer;
            box-shadow: var(--shadow);
            position: relative;
            border: 1px solid rgba(255, 107, 53, 0.2);
        }

        .restaurant-search-result-item .restaurant-image-section {
            width: 100%;
            height: 150px;
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;
            overflow: hidden;
            background-color: #eee;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .restaurant-search-result-item .restaurant-image-section img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }

        .restaurant-search-result-item .restaurant-info-section {
            width: 100%;
            padding: 0.75rem 1rem;
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .restaurant-search-result-item .restaurant-name-rating {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.25rem;
        }

        .restaurant-search-result-item .restaurant-name-rating h5 {
            font-size: 1.1rem;
            color: var(--text);
            font-weight: 600;
            margin: 0;
        }

        .restaurant-search-result-item .rating-info {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.9rem;
            color: #666;
        }

        .restaurant-search-result-item .rating-info i {
            font-size: 0.8rem;
            color: #FFD700;
        }

        .restaurant-search-result-item .delivery-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.85rem;
            color: #666;
            margin-bottom: 0.25rem;
        }

        .restaurant-search-result-item .delivery-info .delivery-time,
        .restaurant-search-result-item .delivery-info .delivery-fee {
            display: flex;
            align-items: center;
            gap: 0.2rem;
        }

        .restaurant-search-result-item .delivery-info i {
            font-size: 0.75rem;
            color: #999;
        }

        .restaurant-search-result-item .delivery-info .delivery-fee.free {
            color: #4CAF50;
            font-weight: 600;
        }
        .restaurant-search-result-item .delivery-info .delivery-fee.paid {
            color: #333;
        }
    `;
    document.head.appendChild(style);

    // DOM Elements related to search functionality
    const appHeader = document.querySelector('.app-header');
    const headerLeft = document.getElementById('headerLeft');
    const headerRight = document.getElementById('headerRight');
    const headerSearchBarContainer = document.getElementById('headerSearchBarContainer');
    const headerSearchBar = document.getElementById('headerSearchBar');
    const closeSearchBtn = headerSearchBarContainer.querySelector('.close-search-btn');
    const mainContent = document.querySelector('.main-content');

    // Main content sections
    const homeContentSection = document.getElementById('homeContentSection');
    const searchContentWrapper = document.getElementById('searchContentWrapper');

    // Search content elements
    const searchResultsList = document.getElementById('searchResultsList');
    const noResultsMessageOverlay = document.getElementById('noResultsMessageOverlay');
    const hotRecommendationsSection = document.getElementById('hotRecommendationsSection');
    const hotRecommendationsTags = document.getElementById('hotRecommendationsTags');
    const popularFoodiesSection = document.getElementById('popularFoodiesSection');
    const popularFoodiesTags = document.getElementById('popularFoodiesTags');
    const restaurantsSection = document.getElementById('restaurantsSection');
    const restaurantList = document.getElementById('restaurantList');

    // Search results containers
    const foodResultsSection = document.getElementById('foodResultsSection');
    const foodResultsContainer = document.getElementById('foodResultsContainer');
    const restaurantSearchResultsSection = document.getElementById('restaurantSearchResultsSection');
    const restaurantSearchResultsContainer = document.getElementById('restaurantSearchResultsContainer');
    const matchingKeywordsSection = document.getElementById('matchingKeywordsSection');
    const matchingKeywordsContainer = document.getElementById('matchingKeywordsContainer');

    let searchIconDiv; 
    const popularFoodiesTitleElement = popularFoodiesSection.querySelector('h3');
    const originalPopularFoodiesTitle = popularFoodiesTitleElement.textContent;


    // Function to create a food item card (for hot recommendations and food search results)
    const createFoodItemCard = (item) => {
        const card = document.createElement('div');
        card.classList.add('card-base');
        card.innerHTML = `
            <div class="card-image-section">
                <img src="${item.image}" alt="${item.name}">
            </div>
            <div class="card-info-section">
                <h5>${item.name}</h5>
                ${item.restaurant_name ? `<p class="sub-text">from ${item.restaurant_name}</p>` : ''}
            </div>
            <div class="card-bottom-section">
                <div class="price-info">
                    PKR${item.price} 
                    ${item.original_price && item.original_price > item.price ? `<small>PKR${item.original_price}</small>` : ''}
                </div>
            </div>
        `;
        card.addEventListener('click', () => {
            // Assuming showMessageBox is globally available or passed
            if (typeof showMessageBox === 'function') {
                showMessageBox(`You clicked on: ${item.name}!`, [{ text: 'OK', callback: null, className: 'btn-primary-popup' }]);
            } else {
                console.log(`Clicked on: ${item.name}`);
            }
        });
        return card;
    };

    // Function to create a hot recommendation item card (uses food-card-base with specific width)
    const createHotRecommendationCard = (item) => {
        const card = createFoodItemCard(item);
        card.classList.add('hot-recommendation-item-card');
        return card;
    };

    // Function to create a restaurant card for the home page (horizontal scroll)
    const createRestaurantDisplayCard = (item) => {
        const card = document.createElement('div');
        card.classList.add('restaurant-display-card'); // Keeps fixed width for horizontal scroll
        card.innerHTML = `
            <div class="restaurant-image-section">
                <img src="${item.image}" alt="${item.name}">
            </div>
            <div class="restaurant-info-section">
                <div class="restaurant-name-rating">
                    <h5>${item.name}</h5>
                    <div class="rating-info">
                        <i class="fas fa-star"></i>
                        <span>${item.ranking || 'N/A'}</span>
                    </div>
                </div>
                <div class="delivery-info">
                    <span class="delivery-time">
                        <i class="fas fa-motorcycle"></i>
                        -- min
                    </span>
                    <span class="delivery-fee paid">
                        <i class="fas fa-dollar-sign"></i>
                        --
                    </span>
                </div>
                ${item.description ? `<p class="sub-text">${item.description}</p>` : ''}
            </div>
        `;
        card.addEventListener('click', () => {
            // Assuming showMessageBox is globally available or passed
            if (typeof showMessageBox === 'function') {
                showMessageBox(`You clicked on restaurant: ${item.name}!`, [{ text: 'OK', callback: null, className: 'btn-primary-popup' }]);
            } else {
                console.log(`Clicked on restaurant: ${item.name}`);
            }
        });
        return card;
    };

    // Function to create a restaurant card for search results (full width)
    const createRestaurantSearchResultItem = (item) => {
        const card = document.createElement('div');
        card.classList.add('restaurant-search-result-item'); // New class for full-width search results
        card.innerHTML = `
            <div class="restaurant-image-section">
                <img src="${item.image}" alt="${item.name}">
            </div>
            <div class="restaurant-info-section">
                <div class="restaurant-name-rating">
                    <h5>${item.name}</h5>
                    <div class="rating-info">
                        <i class="fas fa-star"></i>
                        <span>${item.ranking || 'N/A'}</span>
                    </div>
                </div>
                <div class="delivery-info">
                    <span class="delivery-time">
                        <i class="fas fa-motorcycle"></i>
                        -- min
                    </span>
                    <span class="delivery-fee paid">
                        <i class="fas fa-dollar-sign"></i>
                        --
                    </span>
                </div>
                ${item.description ? `<p class="sub-text">${item.description}</p>` : ''}
            </div>
        `;
        card.addEventListener('click', () => {
            // Assuming showMessageBox is globally available or passed
            if (typeof showMessageBox === 'function') {
                showMessageBox(`You clicked on restaurant: ${item.name}!`, [{ text: 'OK', callback: null, className: 'btn-primary-popup' }]);
            } else {
                console.log(`Clicked on restaurant: ${item.name}`);
            }
        });
        return card;
    };


    const renderHotRecommendations = async () => {
        hotRecommendationsTags.innerHTML = '';
        try {
            const response = await fetch('search.php?type=hot_recommendations'); 
            if (!response.ok) throw new Error('Network response was not ok.');
            const data = await response.json();
            
            if (data.length > 0) {
                hotRecommendationsSection.classList.add('visible');
                data.forEach(item => {
                    if (item.name && item.price && item.image && item.top !== null) {
                        hotRecommendationsTags.appendChild(createHotRecommendationCard(item));
                    }
                });
            } else {
                hotRecommendationsSection.classList.remove('visible');
            }
        } catch (error) {
            console.error('Error fetching hot recommendations:', error);
            hotRecommendationsSection.classList.remove('visible');
        }
    };

    const renderPopularFoodieSearches = async () => {
        popularFoodiesTags.innerHTML = '';
        try {
            const response = await fetch('search.php?type=popular_searches'); 
            if (!response.ok) throw new Error('Network response was not ok.');
            const data = await response.json();

            if (data.length > 0) {
                popularFoodiesSection.classList.add('visible');
                data.forEach(tag => {
                    if (tag.text) { 
                        const tagElement = document.createElement('span');
                        tagElement.classList.add('tag-item');
                        tagElement.textContent = tag.text;
                        tagElement.addEventListener('click', () => {
                            headerSearchBar.value = tag.text;
                            headerSearchBar.dispatchEvent(new Event('input'));
                        });
                        popularFoodiesTags.appendChild(tagElement);
                    }
                });
            } else {
                popularFoodiesSection.classList.remove('visible');
            }
        } catch (error) {
            console.error('Error fetching popular foodie searches:', error);
            popularFoodiesSection.classList.remove('visible');
        }
    };


    const renderRestaurantItems = async (placementFilter = '') => { 
        restaurantList.innerHTML = '';
        try {
            let url = 'search.php?type=restaurants';
            if (placementFilter) {
                url += `&placement=${encodeURIComponent(placementFilter)}`;
            }
            const response = await fetch(url);
            if (!response.ok) throw new Error('Network response was not ok.');
            const data = await response.json();

            if (data.length > 0) {
                restaurantsSection.classList.add('visible');
                data.forEach(item => {
                    if (item.name && item.image) { 
                        restaurantList.appendChild(createRestaurantDisplayCard(item)); // Use home page specific function
                    }
                });
            } else {
                restaurantsSection.classList.remove('visible');
            }
        } catch (error) {
            console.error('Error fetching restaurant items:', error);
            restaurantsSection.classList.remove('visible');
        }
    };


    const renderMatchingKeywords = async (query) => {
        matchingKeywordsContainer.innerHTML = '';
        matchingKeywordsSection.classList.remove('visible');

        if (query.length === 0) {
            return;
        }

        try {
            const response = await fetch(`search.php?type=keywords&query=${encodeURIComponent(query)}`);
            if (!response.ok) throw new Error('Network response was not ok.');
            const keywords = await response.json();

            if (keywords.length > 1) {
                matchingKeywordsSection.classList.add('visible');
                keywords.forEach(keyword => {
                    const tagElement = document.createElement('span');
                    tagElement.classList.add('tag-item');
                    tagElement.textContent = keyword;
                    tagElement.addEventListener('click', () => {
                        headerSearchBar.value = keyword;
                        headerSearchBar.dispatchEvent(new Event('input'));
                    });
                    matchingKeywordsContainer.appendChild(tagElement);
                });
            }
        } catch (error) {
            console.error('Error fetching matching keywords:', error);
        }
    };

    const debounce = (func, delay) => {
        let timeout;
        return function(...args) {
            const context = this;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), delay);
        };
    };

    const renderSearchResults = async (query) => {
        foodResultsContainer.innerHTML = '';
        restaurantSearchResultsContainer.innerHTML = '';
        foodResultsSection.classList.remove('visible');
        restaurantSearchResultsSection.classList.remove('visible');
        noResultsMessageOverlay.classList.remove('visible');

        await renderMatchingKeywords(query);

        try {
            const response = await fetch(`search.php?query=${encodeURIComponent(query)}`);
            if (!response.ok) throw new Error('Network response was not ok.');
            const data = await response.json();

            let foodItems = [];
            let restaurantItems = [];
            let uniqueRestaurantIds = new Set();

            data.forEach(item => {
                if (item.category !== 'restaurant') {
                    foodItems.push(item);
                } else {
                    if (!uniqueRestaurantIds.has(item.id)) {
                        restaurantItems.push(item);
                        uniqueRestaurantIds.add(item.id);
                    }
                }
            });

            if (foodItems.length > 0) {
                foodResultsSection.classList.add('visible');
                foodItems.forEach(item => {
                    foodResultsContainer.appendChild(createFoodItemCard(item));
                });
            }

            if (restaurantItems.length > 0) {
                restaurantSearchResultsSection.classList.add('visible');
                restaurantItems.sort((a, b) => a.name.localeCompare(b.name));
                restaurantItems.forEach(item => {
                    restaurantSearchResultsContainer.appendChild(createRestaurantSearchResultItem(item)); // Use search result specific function
                });
            }

            if (foodItems.length === 0 && restaurantItems.length === 0 && matchingKeywordsContainer.children.length === 0) {
                noResultsMessageOverlay.classList.remove('visible'); // Hide the standard no results message

                // Show "Popular among Foodies" and change its title
                popularFoodiesSection.classList.add('visible');
                popularFoodiesTitleElement.textContent = 'You might like'; // Change the title
                renderPopularFoodieSearches(); // Ensure it's populated

                // Hide other sections that would normally show on the search home screen
                hotRecommendationsSection.classList.remove('visible');
                restaurantsSection.classList.remove('visible');
                searchResultsList.classList.remove('visible'); // Ensure search results list is hidden
            }

        } catch (error) {
            console.error('Error fetching search results:', error);
            noResultsMessageOverlay.classList.add('visible');
            foodResultsContainer.innerHTML = '';
            restaurantSearchResultsContainer.innerHTML = '';
        }
    };

    const openSearch = () => {
        headerLeft.style.display = 'none';
        headerRight.style.display = 'none';

        headerSearchBarContainer.style.display = 'flex';
        setTimeout(() => {
            headerSearchBarContainer.classList.add('active');
            headerSearchBar.focus();
        }, 10);

        homeContentSection.classList.add('hidden'); 
        searchContentWrapper.classList.add('active');

        // Show default search content sections
        popularFoodiesSection.classList.add('visible');
        popularFoodiesTitleElement.textContent = originalPopularFoodiesTitle; // Reset title when opening search
        hotRecommendationsSection.classList.add('visible');
        restaurantsSection.classList.add('visible');

        searchResultsList.classList.remove('visible'); // Hide combined search results initially
        noResultsMessageOverlay.classList.remove('visible');
        headerSearchBar.value = '';

        mainContent.style.backgroundColor = 'var(--white)';

        renderPopularFoodieSearches();
        renderHotRecommendations();
        renderRestaurantItems('search');
    };

    const closeModal = () => {
        headerSearchBarContainer.classList.remove('active');

        searchContentWrapper.classList.remove('active');
        homeContentSection.classList.remove('hidden');

        setTimeout(() => {
            headerSearchBarContainer.style.display = 'none';
            headerLeft.style.display = 'flex';
            headerRight.style.display = 'flex';
        }, 400);

        headerSearchBar.value = '';
        noResultsMessageOverlay.classList.remove('visible');
        foodResultsContainer.innerHTML = '';
        restaurantSearchResultsContainer.innerHTML = '';
        foodResultsSection.classList.remove('visible');
        restaurantSearchResultsSection.classList.remove('visible');
        searchResultsList.classList.remove('visible');
        popularFoodiesSection.classList.remove('visible');
        hotRecommendationsSection.classList.remove('visible');
        restaurantsSection.classList.remove('visible');
        matchingKeywordsSection.classList.remove('visible');
        popularFoodiesTitleElement.textContent = originalPopularFoodiesTitle; // Reset title when closing search

        mainContent.style.backgroundColor = 'var(--neutral)';
    };

    // Initialize search icon and event listeners
    if (headerRight) {
        searchIconDiv = document.createElement('div');
        searchIconDiv.classList.add('header-icon');
        searchIconDiv.id = 'searchHeaderIcon';
        searchIconDiv.innerHTML = '<img width="32" height="32" src="https://img.icons8.com/external-kmg-design-glyph-kmg-design/32/FFFFFF/external-search-user-interface-kmg-design-glyph-kmg-design.png" alt="Search"/>';
        searchIconDiv.setAttribute('aria-label', 'Open search');
        headerRight.prepend(searchIconDiv);

        searchIconDiv.addEventListener('click', openSearch);

        const debouncedRenderSearchResults = debounce(renderSearchResults, 300);

        headerSearchBar.addEventListener('input', async () => {
            const query = headerSearchBar.value.trim();
            if (query.length >= 1) {
                popularFoodiesSection.classList.remove('visible');
                hotRecommendationsSection.classList.remove('visible');
                restaurantsSection.classList.remove('visible');
                searchResultsList.classList.add('visible');
                
                debouncedRenderSearchResults(query);
            } else {
                foodResultsContainer.innerHTML = '';
                restaurantSearchResultsContainer.innerHTML = '';
                foodResultsSection.classList.remove('visible');
                restaurantSearchResultsSection.classList.remove('visible');
                searchResultsList.classList.remove('visible');
                noResultsMessageOverlay.classList.remove('visible');
                matchingKeywordsSection.classList.remove('visible');

                popularFoodiesSection.classList.add('visible');
                popularFoodiesTitleElement.textContent = originalPopularFoodiesTitle; // Reset title when query is cleared
                hotRecommendationsSection.classList.add('visible');
                restaurantsSection.classList.add('visible');

                renderPopularFoodieSearches();
                renderHotRecommendations();
                renderRestaurantItems('search');
            }
        });

        closeSearchBtn.addEventListener('click', closeModal);

        noResultsMessageOverlay.addEventListener('click', (event) => {
            event.stopPropagation();
        });
    } else {
        console.error('Could not find .app-header .header-right to append search icon.');
    }
});
