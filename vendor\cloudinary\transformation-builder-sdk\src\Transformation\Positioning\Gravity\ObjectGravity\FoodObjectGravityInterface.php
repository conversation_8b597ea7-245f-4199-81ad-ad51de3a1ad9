<?php
/**
 * This file is part of the Cloudinary PHP package.
 *
 * (c) Cloudinary
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Cloudinary\Transformation;

/**
 * Interface FoodObjectGravityInterface
 *
 * @api
 */
interface FoodObjectGravityInterface
{
    //Food Category
    public const FOOD = 'food';
    public const BANANA = 'banana';
    public const APPLE  = 'apple';
    public const SANDWICH = 'sandwich';
    public const ORANGE   = 'orange';
    public const BROCCOLI = 'broccoli';
    public const CARROT   = 'carrot';
    public const HOTDOG = 'hotdog';
    public const PIZZA  = 'pizza';
    public const DONUT = 'donut';
    public const CAKE  = 'cake';
}
